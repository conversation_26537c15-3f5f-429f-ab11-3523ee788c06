{"logs": [{"outputFile": "com.app.notiminimalist.app-mergeReleaseResources-41:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\18b22e515b4201f34167a2d64b698d76\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1028,1115,1193,1270,1344,1417,1493,1560", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1023,1110,1188,1265,1339,1412,1488,1555,1674"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1024,1123,1227,1317,1403,7994,8081,8169,8255,8342,8420,8497,8571,8745,8821,8888", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,76,73,72,75,66,118", "endOffsets": "932,1019,1118,1222,1312,1398,1499,8076,8164,8250,8337,8415,8492,8566,8639,8816,8883,9002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a87ea0a2ae3b302fa8b2e67fc66b56aa\\transformed\\foundation-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,91", "endOffsets": "138,230"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "9007,9095", "endColumns": "87,91", "endOffsets": "9090,9182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac5730e1294228dab72f8d7b7acde09d\\transformed\\material3-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4919,5016,5124,5204,5292,5390,5503,5598,5709,5799,5914,6016,6129,6261,6341,6448", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4914,5011,5119,5199,5287,5385,5498,5593,5704,5794,5909,6011,6124,6256,6336,6443,6540"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1504,1624,1750,1876,1999,2099,2193,2304,2456,2574,2731,2816,2921,3021,3123,3246,3379,3489,3625,3767,3898,4102,4236,4360,4490,4624,4725,4823,4941,5072,5171,5273,5386,5524,5670,5784,5893,5969,6067,6167,6281,6368,6465,6573,6653,6741,6839,6952,7047,7158,7248,7363,7465,7578,7710,7790,7897", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "1619,1745,1871,1994,2094,2188,2299,2451,2569,2726,2811,2916,3016,3118,3241,3374,3484,3620,3762,3893,4097,4231,4355,4485,4619,4720,4818,4936,5067,5166,5268,5381,5519,5665,5779,5888,5964,6062,6162,6276,6363,6460,6568,6648,6736,6834,6947,7042,7153,7243,7358,7460,7573,7705,7785,7892,7989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\38ea0471cd817173b4203329613a319d\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,412,516,619,720,8644", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "202,305,407,511,614,715,837,8740"}}]}]}