package com.app.notiminimalist.data

/**
 * Data class representing app usage information for analytics and display purposes.
 */
data class AppUsageInfo(
    val packageName: String,
    val appName: String,
    val silencingMode: SilencingMode,
    val notificationCount: Int = 0,
    val lastNotificationTime: Long = 0L
) {
    /**
     * Get user-friendly description of the app's current state
     */
    fun getStatusDescription(): String {
        return when (silencingMode) {
            SilencingMode.SILENCED -> "Silenced during scheduled hours"
            SilencingMode.ALWAYS_ALLOWED -> "Always allowed"
        }
    }
    
    /**
     * Check if this app has recent notification activity
     */
    fun hasRecentActivity(withinHours: Int = 24): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeThreshold = withinHours * 60 * 60 * 1000L // Convert hours to milliseconds
        return (currentTime - lastNotificationTime) <= timeThreshold
    }
}
