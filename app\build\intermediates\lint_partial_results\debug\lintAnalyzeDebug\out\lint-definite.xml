<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.2" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="218"
            column="22"
            startOffset="7844"
            endLine="218"
            endColumn="56"
            endOffset="7878"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="22"
            column="13"
            startOffset="979"
            endLine="22"
            endColumn="45"
            endOffset="1011"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.12.2 is available: 8.13.0">
        <fix-replace
            description="Replace with 8.13.0"
            family="Update versions"
            oldString="8.12.2"
            replacement="8.13.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
        <map>
            <entry
                name="coordinate"
                string="com.android.application"/>
            <entry
                name="revision"
                string="8.13.0"/>
        </map>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose.material:material-icons-extended than 1.5.4 is available: 1.7.8">
        <fix-replace
            description="Change to 1.7.8"
            family="Update versions"
            oldString="1.5.4"
            replacement="1.7.8"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1472"
            endLine="56"
            endColumn="77"
            endOffset="1529"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5">
        <fix-replace
            description="Change to 2.1.5"
            family="Update versions"
            oldString="2.0.4"
            replacement="2.1.5"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="27"
            startOffset="1615"
            endLine="59"
            endColumn="69"
            endOffset="1657"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0">
        <fix-replace
            description="Change to 1.17.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.17.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="54"
            endLine="4"
            endColumn="19"
            endOffset="62"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0">
        <fix-replace
            description="Change to 1.3.0"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.3.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="95"
            endLine="6"
            endColumn="23"
            endOffset="102"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0">
        <fix-replace
            description="Change to 3.7.0"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.7.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="118"
            endLine="7"
            endColumn="23"
            endOffset="125"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.3">
        <fix-replace
            description="Change to 2.9.3"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.3"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="148"
            endLine="8"
            endColumn="30"
            endOffset="155"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="174"
            endLine="9"
            endColumn="26"
            endOffset="181"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.08.01">
        <fix-replace
            description="Change to 2025.08.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.08.01"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="195"
            endLine="10"
            endColumn="26"
            endOffset="207"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10">
        <fix-replace
            description="Change to 2.2.10"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.10"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10">
        <fix-replace
            description="Change to 2.2.10"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.10"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="96"
            column="17"
            startOffset="2783"
            endLine="96"
            endColumn="63"
            endOffset="2829"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="119"
            column="17"
            startOffset="3570"
            endLine="119"
            endColumn="63"
            endOffset="3616"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="139"
            column="17"
            startOffset="4240"
            endLine="139"
            endColumn="63"
            endOffset="4286"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="158"
            column="17"
            startOffset="4823"
            endLine="158"
            endColumn="63"
            endOffset="4869"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="186"
            column="17"
            startOffset="5816"
            endLine="186"
            endColumn="63"
            endOffset="5862"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT` is never true here">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="194"
            column="17"
            startOffset="6194"
            endLine="194"
            endColumn="68"
            endOffset="6245"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/AlarmScheduler.kt"
            line="243"
            column="17"
            startOffset="7835"
            endLine="243"
            endColumn="63"
            endOffset="7881"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="28"
            column="20"
            startOffset="839"
            endLine="28"
            endColumn="66"
            endOffset="885"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="39"
            column="20"
            startOffset="1215"
            endLine="39"
            endColumn="66"
            endOffset="1261"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="53"
            column="20"
            startOffset="1751"
            endLine="53"
            endColumn="66"
            endOffset="1797"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="75"
            column="20"
            startOffset="2509"
            endLine="75"
            endColumn="66"
            endOffset="2555"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="139"
            column="13"
            startOffset="5136"
            endLine="139"
            endColumn="59"
            endOffset="5182"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="46"
            column="20"
            startOffset="1963"
            endLine="46"
            endColumn="66"
            endOffset="2009"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="74"
            column="17"
            startOffset="3283"
            endLine="74"
            endColumn="63"
            endOffset="3329"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="113"
            column="17"
            startOffset="4983"
            endLine="113"
            endColumn="63"
            endOffset="5029"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="167"
            column="20"
            startOffset="7137"
            endLine="167"
            endColumn="66"
            endOffset="7183"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="217"
            column="20"
            startOffset="9843"
            endLine="217"
            endColumn="66"
            endOffset="9889"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="371"
            column="13"
            startOffset="16600"
            endLine="371"
            endColumn="59"
            endOffset="16646"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="396"
            column="13"
            startOffset="17667"
            endLine="396"
            endColumn="59"
            endOffset="17713"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="176"
            column="17"
            startOffset="7341"
            endLine="176"
            endColumn="70"
            endOffset="7394"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationListenerService.kt"
            line="136"
            column="17"
            startOffset="5388"
            endLine="136"
            endColumn="70"
            endOffset="5441"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationListenerService.kt"
            line="162"
            column="17"
            startOffset="6589"
            endLine="162"
            endColumn="70"
            endOffset="6642"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
            line="63"
            column="20"
            startOffset="2464"
            endLine="63"
            endColumn="66"
            endOffset="2510"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
            line="107"
            column="13"
            startOffset="4003"
            endLine="107"
            endColumn="59"
            endOffset="4049"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/app_logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/app_logo.png"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:${context.packageName}&quot;)"
            replacement="&quot;package:${context.packageName}&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
                startOffset="1369"
                endOffset="1412"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="41"
            column="24"
            startOffset="1369"
            endLine="41"
            endColumn="67"
            endOffset="1412"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:${context.packageName}&quot;)"
            replacement="&quot;package:${context.packageName}&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
                startOffset="1369"
                endOffset="1412"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="41"
            column="24"
            startOffset="1369"
            endLine="41"
            endColumn="67"
            endOffset="1412"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="18642"
                    endOffset="18647"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="18647"
                    endOffset="18648"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="18648"
                    endOffset="18649"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="18707"
                    endOffset="18715"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="419"
            column="13"
            startOffset="18630"
            endLine="419"
            endColumn="31"
            endOffset="18648"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="19447"
                    endOffset="19452"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="19452"
                    endOffset="19453"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="19453"
                    endOffset="19454"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="19512"
                    endOffset="19520"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="437"
            column="13"
            startOffset="19435"
            endLine="437"
            endColumn="31"
            endOffset="19453"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="21465"
                    endOffset="21470"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="21470"
                    endOffset="21471"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="21471"
                    endOffset="21472"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="21530"
                    endOffset="21538"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="482"
            column="13"
            startOffset="21453"
            endLine="482"
            endColumn="31"
            endOffset="21471"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="22511"
                    endOffset="22516"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="22516"
                    endOffset="22517"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="22517"
                    endOffset="22518"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
                    startOffset="22560"
                    endOffset="22568"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="504"
            column="17"
            startOffset="22499"
            endLine="504"
            endColumn="35"
            endOffset="22517"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                    startOffset="6964"
                    endOffset="6969"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                    startOffset="6969"
                    endOffset="6970"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                    startOffset="6970"
                    endOffset="6971"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                    startOffset="7008"
                    endOffset="7016"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="166"
            column="9"
            startOffset="6953"
            endLine="166"
            endColumn="26"
            endOffset="6970"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(&quot;#1C1C1E&quot;)"
            replacement="&quot;#1C1C1E&quot;.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                startOffset="7480"
                endOffset="7524"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="178"
            column="21"
            startOffset="7480"
            endLine="178"
            endColumn="65"
            endOffset="7524"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(&quot;#1C1C1E&quot;)"
            replacement="&quot;#1C1C1E&quot;.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                startOffset="7480"
                endOffset="7524"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="178"
            column="21"
            startOffset="7480"
            endLine="178"
            endColumn="65"
            endOffset="7524"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(&quot;#2B9EDA&quot;)"
            replacement="&quot;#2B9EDA&quot;.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                startOffset="7592"
                endOffset="7636"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="180"
            column="21"
            startOffset="7592"
            endLine="180"
            endColumn="65"
            endOffset="7636"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(&quot;#2B9EDA&quot;)"
            replacement="&quot;#2B9EDA&quot;.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
                startOffset="7592"
                endOffset="7636"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/MainActivity.kt"
            line="180"
            column="21"
            startOffset="7592"
            endLine="180"
            endColumn="65"
            endOffset="7636"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:${activity.packageName}&quot;)"
            replacement="&quot;package:${activity.packageName}&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
                startOffset="4530"
                endOffset="4574"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
            line="121"
            column="24"
            startOffset="4530"
            endLine="121"
            endColumn="68"
            endOffset="4574"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="Uri.parse(&quot;package:${activity.packageName}&quot;)"
            replacement="&quot;package:${activity.packageName}&quot;.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
                startOffset="4530"
                endOffset="4574"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/PermissionManager.kt"
            line="121"
            column="24"
            startOffset="4530"
            endLine="121"
            endColumn="68"
            endOffset="4574"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="1683"
                    endOffset="1688"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="1688"
                    endOffset="1689"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="1689"
                    endOffset="1690"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="1730"
                    endOffset="1738"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="44"
            column="22"
            startOffset="1671"
            endLine="44"
            endColumn="40"
            endOffset="1689"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2044"
                    endOffset="2049"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2049"
                    endOffset="2050"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2063"
                    endOffset="2064"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2163"
                    endOffset="2171"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2051"
                    endOffset="2051"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2099"
                    endOffset="2099"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="54"
            column="22"
            startOffset="2032"
            endLine="54"
            endColumn="40"
            endOffset="2050"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2465"
                    endOffset="2470"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2470"
                    endOffset="2471"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2484"
                    endOffset="2485"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2580"
                    endOffset="2588"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2472"
                    endOffset="2472"/>
            </fix-replace>
            <fix-replace
                description="Replace with     "
                robot="true"
                independent="true"
                oldString=""
                replacement="    "
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2518"
                    endOffset="2518"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="67"
            column="22"
            startOffset="2453"
            endLine="67"
            endColumn="40"
            endOffset="2471"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2904"
                    endOffset="2909"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2909"
                    endOffset="2910"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2910"
                    endOffset="2911"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="2949"
                    endOffset="2957"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="78"
            column="22"
            startOffset="2892"
            endLine="78"
            endColumn="40"
            endOffset="2910"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3211"
                    endOffset="3216"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3216"
                    endOffset="3217"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3217"
                    endOffset="3218"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3256"
                    endOffset="3264"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="85"
            column="22"
            startOffset="3199"
            endLine="85"
            endColumn="40"
            endOffset="3217"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3531"
                    endOffset="3536"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3536"
                    endOffset="3537"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3537"
                    endOffset="3538"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3582"
                    endOffset="3590"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="92"
            column="22"
            startOffset="3519"
            endLine="92"
            endColumn="40"
            endOffset="3537"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3809"
                    endOffset="3814"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3814"
                    endOffset="3815"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3815"
                    endOffset="3816"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="3854"
                    endOffset="3862"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="99"
            column="22"
            startOffset="3797"
            endLine="99"
            endColumn="40"
            endOffset="3815"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="4054"
                    endOffset="4059"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="4059"
                    endOffset="4060"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="4060"
                    endOffset="4061"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="4093"
                    endOffset="4101"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="106"
            column="22"
            startOffset="4042"
            endLine="106"
            endColumn="40"
            endOffset="4060"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="8294"
                    endOffset="8299"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="8299"
                    endOffset="8300"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="8300"
                    endOffset="8301"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
                    startOffset="8308"
                    endOffset="8316"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/data/SchedulePreferences.kt"
            line="234"
            column="9"
            startOffset="8282"
            endLine="234"
            endColumn="27"
            endOffset="8300"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-compose-material-material-icons-extended2"
            robot="true">
            <fix-replace
                description="Replace with androidxMaterialIconsExtendedVersion = &quot;1.5.4&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMaterialIconsExtendedVersion = &quot;1.5.4&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-compose-material-material-icons-extended2 = { module = &quot;androidx.compose.material:material-icons-extended&quot;, version.ref = &quot;androidxMaterialIconsExtendedVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-compose-material-material-icons-extended2 = { module = &quot;androidx.compose.material:material-icons-extended&quot;, version.ref = &quot;androidxMaterialIconsExtendedVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
                    startOffset="221"
                    endOffset="221"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.compose.material.material.icons.extended2"
                robot="true"
                replacement="libs.androidx.compose.material.material.icons.extended2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1472"
                    endOffset="1529"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="56"
            column="20"
            startOffset="1472"
            endLine="56"
            endColumn="77"
            endOffset="1529"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-android-tools-desugar_jdk_libs"
            robot="true">
            <fix-replace
                description="Replace with androidDesugar_jdk_libsVersion = &quot;2.0.4&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidDesugar_jdk_libsVersion = &quot;2.0.4&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-android-tools-desugar_jdk_libs = { module = &quot;com.android.tools:desugar_jdk_libs&quot;, version.ref = &quot;androidDesugar_jdk_libsVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-android-tools-desugar_jdk_libs = { module = &quot;com.android.tools:desugar_jdk_libs&quot;, version.ref = &quot;androidDesugar_jdk_libsVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/Notiminimalist/gradle/libs.versions.toml"
                    startOffset="313"
                    endOffset="313"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.android.tools.desugar.jdk.libs"
                robot="true"
                replacement="libs.com.android.tools.desugar.jdk.libs"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1615"
                    endOffset="1657"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="59"
            column="27"
            startOffset="1615"
            endLine="59"
            endColumn="69"
            endOffset="1657"/>
    </incident>

</incidents>
