**Notiminimalist -- Requirements Document**

**1. Overview**

Notiminimalist is a minimalist Android app that allows users to schedule
automatic silencing of notifications. The app prioritizes simplicity,
low battery usage, and accessibility while ensuring full offline
functionality.

**2. Core Features**

**2.1 Main Controls**

-   **Toggle Button:** Enable or disable scheduled silencing.

-   **Configuration Button:** Open settings for timing and app
    > selections.

**2.2 Scheduling**

-   Users can set **daily start and end times** for notification
    > silencing.

-   Default schedule: **10:00 PM -- 7:00 AM**.

**2.3 App Selection**

-   Configuration screen lists all installed apps.

-   Includes **"Select All"** checkbox.

-   Each app has a toggle:

-   **Silenced** during scheduled hours.

-   **Always allowed** (notifications bypass silencing).

**3. Technical Implementation**

**3.1 Notification Silencing**

-   Use **Do Not Disturb (DND)** for **API 23+**.

-   Use **Notification Channels** for **API 26+** where applicable.

**3.2 Scheduling Mechanism**

-   Use **AlarmManager** to schedule start/end silencing events.

-   Handle events via **BroadcastReceiver**.

**3.3 Permissions**

-   **Notification Policy Access** (to control DND).

-   **Exact Alarm Permission** (for scheduling).

**3.4 Data Storage**

-   Store configuration (timings, app selections, preferences) in
    > **SharedPreferences**.

-   No internet access required (all data is local).

**4. UI/UX Requirements**

-   Minimalist interface (only essential controls).

-   Light and dark mode support.

-   Accessibility compliance (screen readers, large text).

**5. System Requirements**

-   Minimum supported version: **Android API 23 (Marshmallow)**.

-   Optimized for **minimal battery usage**.

-   Must correctly handle:

-   **Device reboots**.

-   **System time changes** (manual or automatic).

**6. Non-Functional Requirements**

-   App must not request unnecessary permissions.

-   No background network activity.

-   Must remain lightweight and responsive.\
    > \
    > \
    > **Note:**\
    > \
    > \
    > Without root or system-level privileges, Android does not let an
    > app completely block another app's notifications before they
    > appear. But --- with a Notification Listener Service, an app can
    > auto-dismiss them the moment they appear. This feels like they
    > never showed up (just a quick flash if you're looking at the
    > tray).
