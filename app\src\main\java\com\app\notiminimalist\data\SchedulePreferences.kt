package com.app.notiminimalist.data

import android.content.Context
import android.content.SharedPreferences
import java.time.LocalTime

/**
 * Manages app configuration storage using SharedPreferences.
 * Stores scheduling settings, app selection preferences, and general app state.
 */
class SchedulePreferences(context: Context) {
    
    private val preferences: SharedPreferences = context.getSharedPreferences(
        PREFS_NAME, Context.MODE_PRIVATE
    )
    
    companion object {
        private const val PREFS_NAME = "notiminimalist_prefs"
        
        // Keys for storing preferences
        private const val KEY_SILENCING_ENABLED = "silencing_enabled"
        private const val KEY_START_HOUR = "start_hour"
        private const val KEY_START_MINUTE = "start_minute"
        private const val KEY_END_HOUR = "end_hour"
        private const val KEY_END_MINUTE = "end_minute"
        private const val KEY_SELECTED_APPS = "selected_apps"
        private const val KEY_SILENCED_APPS = "silenced_apps"
        private const val KEY_ALWAYS_ALLOWED_APPS = "always_allowed_apps"
        private const val KEY_SELECT_ALL_APPS = "select_all_apps"
        private const val KEY_FIRST_RUN = "first_run"
        
        // Default values
        private const val DEFAULT_START_HOUR = 22 // 10:00 PM
        private const val DEFAULT_START_MINUTE = 0
        private const val DEFAULT_END_HOUR = 7 // 7:00 AM
        private const val DEFAULT_END_MINUTE = 0
    }
    
    /**
     * Whether scheduled silencing is enabled.
     */
    var isSilencingEnabled: Boolean
        get() = preferences.getBoolean(KEY_SILENCING_ENABLED, false)
        set(value) = preferences.edit().putBoolean(KEY_SILENCING_ENABLED, value).apply()
    
    /**
     * Start time for scheduled silencing.
     */
    var startTime: LocalTime
        get() = LocalTime.of(
            preferences.getInt(KEY_START_HOUR, DEFAULT_START_HOUR),
            preferences.getInt(KEY_START_MINUTE, DEFAULT_START_MINUTE)
        )
        set(value) = preferences.edit()
            .putInt(KEY_START_HOUR, value.hour)
            .putInt(KEY_START_MINUTE, value.minute)
            .apply()
    
    /**
     * End time for scheduled silencing.
     */
    var endTime: LocalTime
        get() = LocalTime.of(
            preferences.getInt(KEY_END_HOUR, DEFAULT_END_HOUR),
            preferences.getInt(KEY_END_MINUTE, DEFAULT_END_MINUTE)
        )
        set(value) = preferences.edit()
            .putInt(KEY_END_HOUR, value.hour)
            .putInt(KEY_END_MINUTE, value.minute)
            .apply()
    
    /**
     * Set of package names for apps that should be silenced.
     * This is kept for backward compatibility with the legacy implementation.
     */
    var selectedApps: Set<String>
        get() = preferences.getStringSet(KEY_SELECTED_APPS, emptySet()) ?: emptySet()
        set(value) = preferences.edit().putStringSet(KEY_SELECTED_APPS, value).apply()
    
    /**
     * Set of package names for apps that are silenced during scheduled hours.
     */
    var silencedApps: Set<String>
        get() = preferences.getStringSet(KEY_SILENCED_APPS, emptySet()) ?: emptySet()
        set(value) = preferences.edit().putStringSet(KEY_SILENCED_APPS, value).apply()
    
    /**
     * Set of package names for apps that are always allowed (bypass silencing).
     */
    var alwaysAllowedApps: Set<String>
        get() = preferences.getStringSet(KEY_ALWAYS_ALLOWED_APPS, emptySet()) ?: emptySet()
        set(value) = preferences.edit().putStringSet(KEY_ALWAYS_ALLOWED_APPS, value).apply()
    
    /**
     * Whether "Select All" is enabled for app selection.
     */
    var isSelectAllAppsEnabled: Boolean
        get() = preferences.getBoolean(KEY_SELECT_ALL_APPS, true)
        set(value) = preferences.edit().putBoolean(KEY_SELECT_ALL_APPS, value).apply()
    
    /**
     * Whether this is the first run of the app.
     */
    var isFirstRun: Boolean
        get() = preferences.getBoolean(KEY_FIRST_RUN, true)
        set(value) = preferences.edit().putBoolean(KEY_FIRST_RUN, value).apply()
    
    /**
     * Add an app package name to the selected apps set.
     */
    fun addSelectedApp(packageName: String) {
        val currentApps = selectedApps.toMutableSet()
        currentApps.add(packageName)
        selectedApps = currentApps
    }
    
    /**
     * Remove an app package name from the selected apps set.
     */
    fun removeSelectedApp(packageName: String) {
        val currentApps = selectedApps.toMutableSet()
        currentApps.remove(packageName)
        selectedApps = currentApps
    }
    
    /**
     * Check if an app is selected for silencing.
     */
    fun isAppSelected(packageName: String): Boolean {
        return packageName in selectedApps
    }
    
    /**
     * Clear all selected apps.
     */
    fun clearSelectedApps() {
        selectedApps = emptySet()
    }
    
    /**
     * Get the silencing mode for a specific app.
     * Default to ALWAYS_ALLOWED for unconfigured apps.
     */
    fun getAppSilencingMode(packageName: String): SilencingMode {
        return when {
            packageName in silencedApps -> SilencingMode.SILENCED
            packageName in alwaysAllowedApps -> SilencingMode.ALWAYS_ALLOWED
            else -> SilencingMode.ALWAYS_ALLOWED // Default to always allowed
        }
    }
    
    /**
     * Set the silencing mode for a specific app.
     */
    fun setAppSilencingMode(packageName: String, mode: SilencingMode) {
        // Remove from all sets first
        removeFromAllSilencingSets(packageName)
        
        // Add to appropriate set based on mode
        when (mode) {
            SilencingMode.SILENCED -> {
                val currentSilenced = silencedApps.toMutableSet()
                currentSilenced.add(packageName)
                silencedApps = currentSilenced
                
                // For legacy compatibility, only silenced apps are in selectedApps
                val currentSelected = selectedApps.toMutableSet()
                currentSelected.add(packageName)
                selectedApps = currentSelected
            }
            SilencingMode.ALWAYS_ALLOWED -> {
                val currentAllowed = alwaysAllowedApps.toMutableSet()
                currentAllowed.add(packageName)
                alwaysAllowedApps = currentAllowed
                
                // Remove from legacy selectedApps since this app is not silenced
                val currentSelected = selectedApps.toMutableSet()
                currentSelected.remove(packageName)
                selectedApps = currentSelected
            }
        }
    }
    
    /**
     * Remove a package from all silencing sets.
     */
    private fun removeFromAllSilencingSets(packageName: String) {
        // Remove from silenced apps
        val currentSilenced = silencedApps.toMutableSet()
        currentSilenced.remove(packageName)
        silencedApps = currentSilenced
        
        // Remove from always allowed apps
        val currentAllowed = alwaysAllowedApps.toMutableSet()
        currentAllowed.remove(packageName)
        alwaysAllowedApps = currentAllowed
        
        // Remove from legacy selected apps
        val currentSelected = selectedApps.toMutableSet()
        currentSelected.remove(packageName)
        selectedApps = currentSelected
    }
    
    /**
     * Clear all silencing configurations.
     */
    fun clearAllSilencingConfigurations() {
        silencedApps = emptySet()
        alwaysAllowedApps = emptySet()
        selectedApps = emptySet()
    }
    
    /**
     * Get a formatted time string for display.
     */
    fun getFormattedTime(time: LocalTime): String {
        val hour = if (time.hour == 0) 12 else if (time.hour > 12) time.hour - 12 else time.hour
        val minute = String.format("%02d", time.minute)
        val amPm = if (time.hour < 12) "AM" else "PM"
        return "$hour:$minute $amPm"
    }
    
    /**
     * Get the formatted schedule string for display.
     */
    fun getFormattedSchedule(): String {
        return "${getFormattedTime(startTime)} - ${getFormattedTime(endTime)}"
    }
    
    /**
     * Reset all preferences to default values.
     */
    fun resetToDefaults() {
        preferences.edit().clear().apply()
    }
}
