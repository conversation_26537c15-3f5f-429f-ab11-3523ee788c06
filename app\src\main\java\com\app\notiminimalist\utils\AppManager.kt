package com.app.notiminimalist.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.util.Log
import com.app.notiminimalist.data.AppInfo
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.data.SilencingMode


import com.app.notiminimalist.data.AppUsageInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Utility class for managing installed applications.
 * Provides functionality to get, filter, and manage app selections.
 */
class AppManager(private val context: Context) {
    
    private val packageManager = context.packageManager
    private val schedulePreferences = SchedulePreferences(context)
    
    companion object {
        private const val TAG = "AppManager"
    }
    
    /**
     * Get all installed apps that can send notifications.
     * This runs on a background thread to avoid blocking the UI.
     *
     * @param filterMode Controls which apps to show:
     *   - "user_only": Only user-installed apps (no system apps)
     *   - "normal": User apps + essential system apps (default)
     *   - "all": All apps including system apps (for debugging)
     */
    suspend fun getInstalledApps(filterMode: String = "normal"): List<AppInfo> = withContext(Dispatchers.IO) {
        try {
            // Get ALL installed applications, not just those with launcher activities
            val installedPackages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            val selectedApps = schedulePreferences.selectedApps
            
            val appList = installedPackages.mapNotNull { applicationInfo ->
                try {
                    val packageName = applicationInfo.packageName
                    val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                    val icon = try {
                        packageManager.getApplicationIcon(applicationInfo)
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not get icon for $packageName")
                        null
                    }
                    val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                    val isSelected = packageName in selectedApps
                    val silencingMode = schedulePreferences.getAppSilencingMode(packageName)
                    
                    AppInfo(
                        packageName = packageName,
                        appName = appName,
                        icon = icon,
                        isSystemApp = isSystemApp,
                        isSelected = isSelected,
                        silencingMode = silencingMode
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "Error processing app: ${applicationInfo.packageName}", e)
                    null
                }
            }
            
            // Filter and sort apps based on filter mode
            val filteredApps = when (filterMode) {
                "user_only" -> {
                    // Show only user-installed apps (include Notiminimalist for testing)
                    appList.filter { !it.isSystemApp || it.packageName == "com.app.notiminimalist" }
                }
                "all" -> {
                    // Show all apps except the most critical system ones (for debugging)
                    appList.filter { it.packageName != "android" }
                }
                else -> {
                    // Normal filtering: user apps + essential system apps
                    appList.filter { it.shouldShow() }
                }
            }

            filteredApps
                .sortedBy { it.appName.lowercase() }
                .also {
                    Log.d(TAG, "Found ${it.size} apps (filterMode: $filterMode, total installed: ${appList.size})")
                    when (filterMode) {
                        "user_only" -> Log.d(TAG, "Showing user-installed apps only")
                        "all" -> Log.d(TAG, "Showing all apps for debugging")
                        else -> Log.d(TAG, "Showing user apps + essential system apps")
                    }
                }
                
        } catch (e: Exception) {
            Log.e(TAG, "Error getting installed apps", e)
            emptyList()
        }
    }
    
    /**
     * Toggle selection for a specific app.
     */
    fun toggleAppSelection(packageName: String) {
        if (schedulePreferences.isAppSelected(packageName)) {
            schedulePreferences.removeSelectedApp(packageName)
        } else {
            schedulePreferences.addSelectedApp(packageName)
        }
    }
    
    /**
     * Select all apps.
     */
    suspend fun selectAllApps() {
        val allApps = getInstalledApps("user_only")
        val allPackageNames = allApps.map { it.packageName }.toSet()
        schedulePreferences.selectedApps = allPackageNames
        schedulePreferences.isSelectAllAppsEnabled = true
    }
    
    /**
     * Deselect all apps.
     */
    fun deselectAllApps() {
        schedulePreferences.clearSelectedApps()
        schedulePreferences.isSelectAllAppsEnabled = false
    }
    
    /**
     * Check if all apps are selected.
     */
    suspend fun areAllAppsSelected(): Boolean {
        val allApps = getInstalledApps("user_only")
        val selectedApps = schedulePreferences.selectedApps
        return allApps.all { it.packageName in selectedApps }
    }
    
    /**
     * Get count of selected apps.
     */
    fun getSelectedAppCount(): Int {
        return schedulePreferences.selectedApps.size
    }
    
    /**
     * Get description of current selection.
     */
    suspend fun getSelectionDescription(): String {
        val selectedCount = getSelectedAppCount()
        val totalCount = getInstalledApps("user_only").size

        return when {
            selectedCount == 0 -> "No apps selected"
            selectedCount == totalCount -> "All apps selected"
            else -> "$selectedCount of $totalCount apps selected"
        }
    }
    
    /**
     * Check if a specific app is selected.
     */
    fun isAppSelected(packageName: String): Boolean {
        return schedulePreferences.isAppSelected(packageName)
    }
    
    /**
     * Set the silencing mode for a specific app.
     */
    fun setAppSilencingMode(packageName: String, mode: SilencingMode) {
        schedulePreferences.setAppSilencingMode(packageName, mode)
        
        // Update notification channels if supported
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val dndManager = com.app.notiminimalist.service.DndManager(context)
            dndManager.updateNotificationChannels()
        }
    }
    
    /**
     * Set silencing mode for multiple apps in batch for better performance.
     */
    fun setAppsSilencingModeBatch(packageNames: List<String>, mode: SilencingMode) {
        packageNames.forEach { packageName ->
            schedulePreferences.setAppSilencingMode(packageName, mode)
        }
        
        // Update notification channels once after all changes if supported
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val dndManager = com.app.notiminimalist.service.DndManager(context)
            dndManager.updateNotificationChannels()
        }
    }
    
    /**
     * Set all apps to a specific silencing mode.
     */
    suspend fun setAllAppsSilencingMode(mode: SilencingMode): List<AppInfo> {
        return withContext(Dispatchers.IO) {
            val apps = getInstalledApps("user_only")
            val packageNames = apps.map { it.packageName }

            // Batch update all apps
            setAppsSilencingModeBatch(packageNames, mode)

            // Return updated app list
            apps.map { it.copy(
                silencingMode = mode,
                isSelected = mode == SilencingMode.SILENCED
            ) }
        }
    }
    

    

    

    

    

    

    

    


    /**
     * Get count of apps by silencing mode.
     */
    suspend fun getAppsCountByMode(mode: SilencingMode): Int = withContext(Dispatchers.IO) {
        try {
            val apps = getInstalledApps("user_only")
            apps.count { it.silencingMode == mode }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting apps count by mode", e)
            0
        }
    }

    /**
     * Get enhanced description of current app selection with detailed statistics.
     */
    suspend fun getEnhancedSelectionDescription(): String = withContext(Dispatchers.IO) {
        try {
            val apps = getInstalledApps("user_only")
            val silencedCount = apps.count { it.silencingMode == SilencingMode.SILENCED }
            val allowedCount = apps.count { it.silencingMode == SilencingMode.ALWAYS_ALLOWED }
            val totalCount = apps.size

            when {
                totalCount == 0 -> "No apps found"
                silencedCount == 0 -> "All $totalCount apps are always allowed"
                allowedCount == 0 -> "All $totalCount apps will be silenced"
                silencedCount == totalCount -> "All apps will be silenced"
                else -> "$silencedCount silenced, $allowedCount allowed"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting enhanced selection description", e)
            "Unable to load app statistics"
        }
    }

    /**
     * Get detailed app statistics for debugging app visibility issues.
     */
    suspend fun getAppStatistics(): String = withContext(Dispatchers.IO) {
        try {
            val allInstalledApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            val userApps = allInstalledApps.filter { (it.flags and ApplicationInfo.FLAG_SYSTEM) == 0 }
            val systemApps = allInstalledApps.filter { (it.flags and ApplicationInfo.FLAG_SYSTEM) != 0 }

            val visibleApps = getInstalledApps("user_only")
            val allAppsIncludingSystem = getInstalledApps("all")

            """
            App Statistics:
            - Total installed apps: ${allInstalledApps.size}
            - User apps: ${userApps.size}
            - System apps: ${systemApps.size}
            - Visible in app list: ${visibleApps.size}
            - Visible with all apps mode: ${allAppsIncludingSystem.size}
            - Hidden apps: ${allInstalledApps.size - allAppsIncludingSystem.size}

            Device Info:
            - Manufacturer: ${android.os.Build.MANUFACTURER}
            - Model: ${android.os.Build.MODEL}
            - Android Version: ${android.os.Build.VERSION.RELEASE}
            - API Level: ${android.os.Build.VERSION.SDK_INT}
            """.trimIndent()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app statistics", e)
            "Error getting app statistics: ${e.message}"
        }
    }

    /**
     * Get list of hidden apps for debugging.
     */
    suspend fun getHiddenApps(): List<String> = withContext(Dispatchers.IO) {
        try {
            val allInstalledApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            val visiblePackages = getInstalledApps("all").map { it.packageName }.toSet()

            allInstalledApps
                .filter { it.packageName !in visiblePackages }
                .map { "${packageManager.getApplicationLabel(it)} (${it.packageName})" }
                .sorted()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting hidden apps", e)
            emptyList()
        }
    }
}
