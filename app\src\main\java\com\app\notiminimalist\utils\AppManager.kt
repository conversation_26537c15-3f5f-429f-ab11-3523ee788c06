package com.app.notiminimalist.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.util.Log
import com.app.notiminimalist.data.AppInfo
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.data.SilencingMode


import com.app.notiminimalist.data.AppUsageInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Utility class for managing installed applications.
 * Provides functionality to get, filter, and manage app selections.
 */
class AppManager(private val context: Context) {
    
    private val packageManager = context.packageManager
    private val schedulePreferences = SchedulePreferences(context)
    
    companion object {
        private const val TAG = "AppManager"
    }
    
    /**
     * Get all installed apps that can send notifications.
     * This runs on a background thread to avoid blocking the UI.
     */
    suspend fun getInstalledApps(): List<AppInfo> = withContext(Dispatchers.IO) {
        try {
            // Get ALL installed applications, not just those with launcher activities
            val installedPackages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            val selectedApps = schedulePreferences.selectedApps
            
            val appList = installedPackages.mapNotNull { applicationInfo ->
                try {
                    val packageName = applicationInfo.packageName
                    val appName = packageManager.getApplicationLabel(applicationInfo).toString()
                    val icon = try {
                        packageManager.getApplicationIcon(applicationInfo)
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not get icon for $packageName")
                        null
                    }
                    val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                    val isSelected = packageName in selectedApps
                    val silencingMode = schedulePreferences.getAppSilencingMode(packageName)
                    
                    AppInfo(
                        packageName = packageName,
                        appName = appName,
                        icon = icon,
                        isSystemApp = isSystemApp,
                        isSelected = isSelected,
                        silencingMode = silencingMode
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "Error processing app: ${applicationInfo.packageName}", e)
                    null
                }
            }
            
            // Filter and sort apps
            appList
                .filter { it.shouldShow() }
                .sortedBy { it.appName.lowercase() }
                .also { 
                    Log.d(TAG, "Found ${it.size} user apps")
                }
                
        } catch (e: Exception) {
            Log.e(TAG, "Error getting installed apps", e)
            emptyList()
        }
    }
    
    /**
     * Toggle selection for a specific app.
     */
    fun toggleAppSelection(packageName: String) {
        if (schedulePreferences.isAppSelected(packageName)) {
            schedulePreferences.removeSelectedApp(packageName)
        } else {
            schedulePreferences.addSelectedApp(packageName)
        }
    }
    
    /**
     * Select all apps.
     */
    suspend fun selectAllApps() {
        val allApps = getInstalledApps()
        val allPackageNames = allApps.map { it.packageName }.toSet()
        schedulePreferences.selectedApps = allPackageNames
        schedulePreferences.isSelectAllAppsEnabled = true
    }
    
    /**
     * Deselect all apps.
     */
    fun deselectAllApps() {
        schedulePreferences.clearSelectedApps()
        schedulePreferences.isSelectAllAppsEnabled = false
    }
    
    /**
     * Check if all apps are selected.
     */
    suspend fun areAllAppsSelected(): Boolean {
        val allApps = getInstalledApps()
        val selectedApps = schedulePreferences.selectedApps
        return allApps.all { it.packageName in selectedApps }
    }
    
    /**
     * Get count of selected apps.
     */
    fun getSelectedAppCount(): Int {
        return schedulePreferences.selectedApps.size
    }
    
    /**
     * Get description of current selection.
     */
    suspend fun getSelectionDescription(): String {
        val selectedCount = getSelectedAppCount()
        val totalCount = getInstalledApps().size
        
        return when {
            selectedCount == 0 -> "No apps selected"
            selectedCount == totalCount -> "All apps selected"
            else -> "$selectedCount of $totalCount apps selected"
        }
    }
    
    /**
     * Check if a specific app is selected.
     */
    fun isAppSelected(packageName: String): Boolean {
        return schedulePreferences.isAppSelected(packageName)
    }
    
    /**
     * Set the silencing mode for a specific app.
     */
    fun setAppSilencingMode(packageName: String, mode: SilencingMode) {
        schedulePreferences.setAppSilencingMode(packageName, mode)
        
        // Update notification channels if supported
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val dndManager = com.app.notiminimalist.service.DndManager(context)
            dndManager.updateNotificationChannels()
        }
    }
    
    /**
     * Set silencing mode for multiple apps in batch for better performance.
     */
    fun setAppsSilencingModeBatch(packageNames: List<String>, mode: SilencingMode) {
        packageNames.forEach { packageName ->
            schedulePreferences.setAppSilencingMode(packageName, mode)
        }
        
        // Update notification channels once after all changes if supported
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val dndManager = com.app.notiminimalist.service.DndManager(context)
            dndManager.updateNotificationChannels()
        }
    }
    
    /**
     * Set all apps to a specific silencing mode.
     */
    suspend fun setAllAppsSilencingMode(mode: SilencingMode): List<AppInfo> {
        return withContext(Dispatchers.IO) {
            val apps = getInstalledApps()
            val packageNames = apps.map { it.packageName }
            
            // Batch update all apps
            setAppsSilencingModeBatch(packageNames, mode)
            
            // Return updated app list
            apps.map { it.copy(
                silencingMode = mode,
                isSelected = mode == SilencingMode.SILENCED
            ) }
        }
    }
    

    

    

    

    

    

    

    


    /**
     * Get count of apps by silencing mode.
     */
    suspend fun getAppsCountByMode(mode: SilencingMode): Int = withContext(Dispatchers.IO) {
        try {
            val apps = getInstalledApps()
            apps.count { it.silencingMode == mode }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting apps count by mode", e)
            0
        }
    }

    /**
     * Get enhanced description of current app selection with detailed statistics.
     */
    suspend fun getEnhancedSelectionDescription(): String = withContext(Dispatchers.IO) {
        try {
            val apps = getInstalledApps()
            val silencedCount = apps.count { it.silencingMode == SilencingMode.SILENCED }
            val allowedCount = apps.count { it.silencingMode == SilencingMode.ALWAYS_ALLOWED }
            val totalCount = apps.size

            when {
                totalCount == 0 -> "No apps found"
                silencedCount == 0 -> "All $totalCount apps are always allowed"
                allowedCount == 0 -> "All $totalCount apps will be silenced"
                silencedCount == totalCount -> "All apps will be silenced"
                else -> "$silencedCount silenced, $allowedCount allowed"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting enhanced selection description", e)
            "Unable to load app statistics"
        }
    }
}
