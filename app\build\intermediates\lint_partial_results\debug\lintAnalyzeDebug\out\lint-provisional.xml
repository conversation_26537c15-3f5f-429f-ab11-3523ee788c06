<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.2" type="conditional_incidents">

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="173"
            column="36"
            startOffset="7530"
            endLine="173"
            endColumn="87"
            endOffset="7581"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_ALARMS"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="174"
            column="34"
            startOffset="7618"
            endLine="174"
            endColumn="85"
            endOffset="7669"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_SYSTEM"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="187"
            column="17"
            startOffset="8592"
            endLine="187"
            endColumn="69"
            endOffset="8644"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_AMBIENT"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="188"
            column="17"
            startOffset="8664"
            endLine="188"
            endColumn="67"
            endOffset="8714"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_BADGE"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="225"
            column="21"
            startOffset="10312"
            endLine="225"
            endColumn="72"
            endOffset="10363"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_ALARMS"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="226"
            column="21"
            startOffset="10387"
            endLine="226"
            endColumn="72"
            endOffset="10438"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_SYSTEM"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="232"
            column="21"
            startOffset="10757"
            endLine="232"
            endColumn="72"
            endOffset="10808"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_ALARMS"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="233"
            column="21"
            startOffset="10832"
            endLine="233"
            endColumn="72"
            endOffset="10883"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_SYSTEM"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="238"
            column="21"
            startOffset="11099"
            endLine="238"
            endColumn="72"
            endOffset="11150"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_ALARMS"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="239"
            column="21"
            startOffset="11174"
            endLine="239"
            endColumn="72"
            endOffset="11225"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="PRIORITY_CATEGORY_SYSTEM"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="256"
            column="17"
            startOffset="12276"
            endLine="256"
            endColumn="69"
            endOffset="12328"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_AMBIENT"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="257"
            column="17"
            startOffset="12348"
            endLine="257"
            endColumn="67"
            endOffset="12398"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_BADGE"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="258"
            column="17"
            startOffset="12418"
            endLine="258"
            endColumn="80"
            endOffset="12481"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_FULL_SCREEN_INTENT`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_FULL_SCREEN_INTENT"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="259"
            column="17"
            startOffset="12501"
            endLine="259"
            endColumn="68"
            endOffset="12552"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_LIGHTS`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_LIGHTS"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="260"
            column="17"
            startOffset="12572"
            endLine="260"
            endColumn="79"
            endOffset="12634"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_NOTIFICATION_LIST`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_NOTIFICATION_LIST"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="261"
            column="17"
            startOffset="12654"
            endLine="261"
            endColumn="66"
            endOffset="12703"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_PEEK`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_PEEK"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="26-28" requiresApi="28-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/DndManager.kt"
            line="264"
            column="17"
            startOffset="12872"
            endLine="264"
            endColumn="72"
            endOffset="12927"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_STATUS_BAR`"/>
            <api-levels id="minSdk"
                value="26-28"/>
            <entry
                name="name"
                string="SUPPRESSED_EFFECT_STATUS_BAR"/>
            <entry
                name="owner"
                string="android.app.NotificationManager.Policy"/>
            <api-levels id="requiresApi"
                value="28-∞"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/BatteryOptimizer.kt"
            line="40"
            column="29"
            startOffset="1293"
            endLine="40"
            endColumn="72"
            endOffset="1336"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="42"
            column="19"
            startOffset="1531"
            endLine="42"
            endColumn="22"
            endOffset="1534"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="51"
            column="19"
            startOffset="1872"
            endLine="51"
            endColumn="22"
            endOffset="1875"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="64"
            column="19"
            startOffset="2479"
            endLine="64"
            endColumn="22"
            endOffset="2482"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="91"
            column="27"
            startOffset="3669"
            endLine="91"
            endColumn="30"
            endOffset="3672"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="104"
            column="27"
            startOffset="4357"
            endLine="104"
            endColumn="30"
            endOffset="4360"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="109"
            column="19"
            startOffset="4513"
            endLine="109"
            endColumn="22"
            endOffset="4516"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="142"
            column="23"
            startOffset="5991"
            endLine="142"
            endColumn="26"
            endOffset="5994"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="148"
            column="27"
            startOffset="6293"
            endLine="148"
            endColumn="30"
            endOffset="6296"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="153"
            column="19"
            startOffset="6452"
            endLine="153"
            endColumn="22"
            endOffset="6455"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="195"
            column="23"
            startOffset="8242"
            endLine="195"
            endColumn="26"
            endOffset="8245"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="198"
            column="19"
            startOffset="8341"
            endLine="198"
            endColumn="22"
            endOffset="8344"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="201"
            column="19"
            startOffset="8487"
            endLine="201"
            endColumn="22"
            endOffset="8490"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="229"
            column="23"
            startOffset="9661"
            endLine="229"
            endColumn="26"
            endOffset="9664"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="245"
            column="23"
            startOffset="10425"
            endLine="245"
            endColumn="26"
            endOffset="10428"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="249"
            column="19"
            startOffset="10576"
            endLine="249"
            endColumn="22"
            endOffset="10579"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="269"
            column="31"
            startOffset="11402"
            endLine="269"
            endColumn="34"
            endOffset="11405"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="275"
            column="19"
            startOffset="11569"
            endLine="275"
            endColumn="22"
            endOffset="11572"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="321"
            column="19"
            startOffset="13218"
            endLine="321"
            endColumn="22"
            endOffset="13221"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="371"
            column="27"
            startOffset="15362"
            endLine="371"
            endColumn="30"
            endOffset="15365"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="374"
            column="27"
            startOffset="15522"
            endLine="374"
            endColumn="30"
            endOffset="15525"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="379"
            column="19"
            startOffset="15690"
            endLine="379"
            endColumn="22"
            endOffset="15693"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="394"
            column="19"
            startOffset="16332"
            endLine="394"
            endColumn="22"
            endOffset="16335"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 26 (NotificationChannelManager)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="397"
            column="19"
            startOffset="16495"
            endLine="397"
            endColumn="22"
            endOffset="16498"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/AppManager.kt"
            line="39"
            column="52"
            startOffset="1376"
            endLine="39"
            endColumn="76"
            endOffset="1400"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/AppManager.kt"
            line="264"
            column="51"
            startOffset="9119"
            endLine="264"
            endColumn="75"
            endOffset="9143"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/utils/AppManager.kt"
            line="297"
            column="51"
            startOffset="10599"
            endLine="297"
            endColumn="75"
            endOffset="10623"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="1387"
                endOffset="1422"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="39"
            column="5"
            startOffset="1387"
            endLine="39"
            endColumn="40"
            endOffset="1422"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="2651"
                endOffset="2686"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="71"
            column="5"
            startOffset="2651"
            endLine="71"
            endColumn="40"
            endOffset="2686"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="4769"
                endOffset="4804"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="117"
            column="5"
            startOffset="4769"
            endLine="117"
            endColumn="40"
            endOffset="4804"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="6666"
                endOffset="6701"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="161"
            column="5"
            startOffset="6666"
            endLine="161"
            endColumn="40"
            endOffset="6701"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="8675"
                endOffset="8710"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="208"
            column="5"
            startOffset="8675"
            endLine="208"
            endColumn="40"
            endOffset="8710"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="10709"
                endOffset="10744"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="256"
            column="5"
            startOffset="10709"
            endLine="256"
            endColumn="40"
            endOffset="10744"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="13462"
                endOffset="13497"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="330"
            column="5"
            startOffset="13462"
            endLine="330"
            endColumn="40"
            endOffset="13497"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 26">
        <fix-replace
            description="Delete @RequiresApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
                startOffset="15948"
                endOffset="15983"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/app/notiminimalist/service/NotificationChannelManager.kt"
            line="387"
            column="5"
            startOffset="15948"
            endLine="387"
            endColumn="40"
            endOffset="15983"/>
        <map>
            <condition minGE="26-∞"/>
        </map>
    </incident>

</incidents>
