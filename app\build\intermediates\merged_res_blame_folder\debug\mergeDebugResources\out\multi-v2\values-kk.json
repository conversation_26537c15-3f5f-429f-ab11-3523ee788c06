{"logs": [{"outputFile": "com.app.notiminimalist.app-mergeDebugResources-45:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a87ea0a2ae3b302fa8b2e67fc66b56aa\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,147", "endColumns": "91,95", "endOffsets": "142,238"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8721,8813", "endColumns": "91,95", "endOffsets": "8808,8904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac5730e1294228dab72f8d7b7acde09d\\transformed\\material3-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4638,4724,4808,4912,5001,5086,5187,5291,5388,5484,5571,5675,5774,5872,6009,6099,6210", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4633,4719,4803,4907,4996,5081,5182,5286,5383,5479,5566,5670,5769,5867,6004,6094,6205,6306"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1463,1581,1699,1814,1930,2032,2133,2251,2389,2514,2639,2723,2826,2916,3013,3129,3253,3361,3503,3643,3775,3934,4057,4172,4291,4406,4497,4595,4718,4853,4957,5068,5174,5313,5458,5566,5666,5752,5845,5938,6046,6132,6216,6320,6409,6494,6595,6699,6796,6892,6979,7083,7182,7280,7417,7507,7618", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "1576,1694,1809,1925,2027,2128,2246,2384,2509,2634,2718,2821,2911,3008,3124,3248,3356,3498,3638,3770,3929,4052,4167,4286,4401,4492,4590,4713,4848,4952,5063,5169,5308,5453,5561,5661,5747,5840,5933,6041,6127,6211,6315,6404,6489,6590,6694,6791,6887,6974,7078,7177,7275,7412,7502,7613,7714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\38ea0471cd817173b4203329613a319d\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,404,507,611,708,8356", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "195,297,399,502,606,703,814,8452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\18b22e515b4201f34167a2d64b698d76\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,1004,1089,1162,1236,1312,1386,1462,1532", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,999,1084,1157,1231,1307,1381,1457,1527,1645"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "819,912,995,1100,1202,1289,1370,7719,7809,7891,7974,8059,8132,8206,8282,8457,8533,8603", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,73,75,73,75,69,117", "endOffsets": "907,990,1095,1197,1284,1365,1458,7804,7886,7969,8054,8127,8201,8277,8351,8528,8598,8716"}}]}]}