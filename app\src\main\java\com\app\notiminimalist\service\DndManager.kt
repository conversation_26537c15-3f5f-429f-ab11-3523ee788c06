package com.app.notiminimalist.service

import android.app.NotificationManager
import android.content.Context
import android.media.AudioManager
import android.os.Build
import android.util.Log
import com.app.notiminimalist.data.SchedulePreferences

/**
 * Manages selective notification silencing WITHOUT system-wide Do Not Disturb.
 * 
 * This implementation provides per-app notification control by:
 * - API 26+: Using notification channels to silence specific apps while preserving others
 * - API 23-25: Relying entirely on NotificationListenerService for selective dismissal
 * - All APIs: NotificationListenerService handles the actual dismissal of silenced apps
 * 
 * Key Benefits:
 * - No system-wide DND interference (other apps work normally)
 * - Precise per-app control based on user selection
 * - Always-allowed apps are completely unaffected
 * - System notifications (alarms, calls) always work
 * - User's manual DND settings are preserved
 */
class DndManager(private val context: Context) {
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private val schedulePreferences = SchedulePreferences(context)
    private val channelManager = NotificationChannelManager(context)
    
    companion object {
        private const val TAG = "DndManager"
        private const val PREFS_ORIGINAL_NOTIFICATION_VOLUME = "original_notification_volume"
    }
    
    // Store original volume to restore later
    private val preferences = context.getSharedPreferences("dnd_audio_prefs", Context.MODE_PRIVATE)
    
    /**
     * Check if DND permission is granted.
     * Note: Since we no longer use system-wide DND, this permission is optional.
     * The app works fine with just NotificationListenerService permission.
     */
    fun hasPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            notificationManager.isNotificationPolicyAccessGranted
        } else {
            true // No permission needed for API < 23
        }
    }
    
    /**
     * Enable selective notification control WITHOUT system-wide DND.
     * This relies entirely on NotificationListenerService for per-app control.
     */
    fun enableDnd(): Boolean {
        // Note: DND permission is now optional since we don't use system DND
        // We'll still check it for API 26+ notification channel management
        
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    // For API 26+, use enhanced channel muting + aggressive audio suppression
                    if (hasPermission()) {
                        // Apply enhanced channel muting for maximum silencing effect
                        applyEnhancedChannelMuting()
                        Log.d(TAG, "Selective silencing enabled via enhanced channels + aggressive audio suppression (API 26+)")
                    } else {
                        Log.d(TAG, "Selective silencing enabled via aggressive audio suppression only (no channel permission)")
                    }
                    true
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // For API 23-25, rely on NotificationListenerService + audio suppression
                    applyConservativeAudioSuppression()
                    
                    Log.d(TAG, "Selective silencing enabled via NotificationListener + audio suppression (API 23-25)")
                    true
                }
                else -> {
                    // For older APIs, apply audio suppression as fallback
                    applyConservativeAudioSuppression()
                    Log.d(TAG, "Selective silencing enabled via NotificationListener + audio suppression (API < 23)")
                    true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to enable selective silencing", e)
            false
        }
    }
    
    /**
     * Disable selective notification control.
     * This only restores notification channels, no system DND to disable.
     */
    fun disableDnd(): Boolean {
        // Note: DND permission is now optional since we don't use system DND
        
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    // For API 26+, restore original notification channels
                    if (hasPermission()) {
                        restoreEnhancedChannelMuting()
                        Log.d(TAG, "Selective silencing disabled - original channels restored (API 26+)")
                    } else {
                        Log.d(TAG, "Selective silencing disabled (no channel permission)")
                    }
                    true
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // For API 23-25, restore audio
                    restoreConservativeAudioSuppression()
                    
                    Log.d(TAG, "Selective silencing disabled (API 23-25)")
                    true
                }
                else -> {
                    // For older APIs, restore audio
                    restoreConservativeAudioSuppression()
                    Log.d(TAG, "Selective silencing disabled (API < 23)")
                    true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to disable selective silencing", e)
            false
        }
    }
    
    /**
     * Check if selective silencing is currently active.
     * Since we don't use system DND, this checks if we're in silencing hours.
     */
    fun isDndActive(): Boolean {
        // We don't use system DND anymore, so check if silencing should be active
        val schedulePreferences = SchedulePreferences(context)
        return schedulePreferences.isSilencingEnabled && isCurrentlyInSilencingHours()
    }
    
    /**
     * Check if current time is within silencing hours
     */
    private fun isCurrentlyInSilencingHours(): Boolean {
        val schedulePreferences = SchedulePreferences(context)
        val now = java.time.LocalTime.now()
        val startTime = schedulePreferences.startTime
        val endTime = schedulePreferences.endTime
        
        return if (startTime.isBefore(endTime)) {
            // Same day range (e.g., 9 AM - 5 PM)
            !now.isBefore(startTime) && now.isBefore(endTime)
        } else {
            // Cross midnight range (e.g., 10 PM - 7 AM)
            !now.isBefore(startTime) || now.isBefore(endTime)
        }
    }
    
    /**
     * Create a selective notification policy that allows app notifications through
     * but relies on notification channels for actual silencing control.
     * This enables per-app control rather than blocking all apps globally.
     */
    private fun createSelectiveNotificationPolicy(): NotificationManager.Policy {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val silencedApps = schedulePreferences.silencedApps
            val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
            
            // Create a permissive policy that allows app notifications through
            // The actual silencing will be handled by notification channels
            val policyCategories = NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                                 NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                                 NotificationManager.Policy.PRIORITY_CATEGORY_CALLS or
                                 NotificationManager.Policy.PRIORITY_CATEGORY_MESSAGES or
                                 NotificationManager.Policy.PRIORITY_CATEGORY_EVENTS or
                                 NotificationManager.Policy.PRIORITY_CATEGORY_REMINDERS
            
            // For API 28+, minimize suppressed visual effects to allow notifications through
            val suppressedVisualEffects = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Only suppress ambient and badge effects, allow others through
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // For API 26-27, minimal suppression
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE
            } else {
                0
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                NotificationManager.Policy(
                    policyCategories,
                    0, // Allow all callers (channels will handle filtering)
                    0, // Allow all message senders (channels will handle filtering)
                    suppressedVisualEffects
                )
            } else {
                NotificationManager.Policy(
                    policyCategories,
                    0, // Allow all callers (channels will handle filtering)
                    0  // Allow all message senders (channels will handle filtering)
                )
            }
        } else {
            throw UnsupportedOperationException("Notification policy not supported on API < 23")
        }
    }
    
    /**
     * Create a notification policy based on app selection and silencing modes.
     * This allows certain apps to bypass DND while silencing others.
     */
    private fun createNotificationPolicy(): NotificationManager.Policy {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val silencedApps = schedulePreferences.silencedApps
            val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
            
            // Create a more restrictive policy that blocks most notifications and their sounds
            val policyCategories = when {
                // Always allow critical system functions
                alwaysAllowedApps.isNotEmpty() -> {
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
                    // Note: Removed MESSAGES to block most app notifications and sounds
                }
                // If we only have silenced apps, be very restrictive
                silencedApps.isNotEmpty() -> {
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
                }
                // If no apps are configured, use default restrictive policy
                else -> {
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
                }
            }
            
            // For API 28+, include suppressed visual effects to block sounds
            val suppressedVisualEffects = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
            } else {
                0
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                NotificationManager.Policy(
                    policyCategories,
                    0, // Only starred contacts
                    0, // Only starred contacts for messages
                    suppressedVisualEffects
                )
            } else {
                NotificationManager.Policy(
                    policyCategories,
                    0, // Only starred contacts
                    0  // Only starred contacts for messages
                )
            }
        } else {
            throw UnsupportedOperationException("Notification policy not supported on API < 23")
        }
    }
    
    /**
     * Get current selective silencing status as a user-friendly string.
     */
    fun getDndStatusDescription(): String {
        return when {
            !hasPermission() -> "Notification access permission not granted"
            isDndActive() -> "Selective silencing is active"
            else -> "Selective silencing is inactive"
        }
    }
    
    /**
     * Toggle selective silencing state.
     */
    fun toggleDnd(): Boolean {
        return if (isDndActive()) {
            disableDnd()
        } else {
            enableDnd()
        }
    }
    
    /**
     * Initialize or update notification channels based on current app configurations.
     * Should be called when app silencing modes change.
     */
    fun updateNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channelManager.updateNotificationChannels()
            Log.d(TAG, "Updated notification channels")
        }
    }
    
    /**
     * Clean up unused notification channels.
     */
    fun cleanupNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channelManager.cleanupUnusedChannels()
            Log.d(TAG, "Cleaned up notification channels")
        }
    }
    
    /**
     * Get comprehensive DND status including channel information.
     */
    fun getComprehensiveDndStatus(): String {
        val dndStatus = getDndStatusDescription()
        val channelStatus = channelManager.getChannelStatusDescription()
        
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> "$dndStatus | $channelStatus"
            else -> dndStatus
        }
    }
    
    /**
     * Get detailed policy information for debugging/display.
     */
    fun getPolicyDetails(): String {
        val silencedCount = schedulePreferences.silencedApps.size
        val allowedCount = schedulePreferences.alwaysAllowedApps.size
        
        return when {
            silencedCount == 0 && allowedCount == 0 -> "No apps configured for silencing"
            allowedCount > 0 -> "Policy: Permissive (${allowedCount} apps always allowed, ${silencedCount} silenced)"
            silencedCount > 0 -> "Policy: Restrictive (${silencedCount} apps silenced)"
            else -> "Policy: Default restrictive"
        }
    }
    
    /**
     * Check if the current DND configuration is optimal.
     */
    fun isConfigurationOptimal(): Boolean {
        val silencedApps = schedulePreferences.silencedApps
        val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
        
        return when {
            // If using API 26+, notification channels provide better control
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> channelManager.areChannelsProperlyConfigured()
            
            // For older APIs, having at least some configuration is good
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> (silencedApps.isNotEmpty() || alwaysAllowedApps.isNotEmpty())
            
            // For very old APIs, any configuration is suboptimal
            else -> false
        }
    }
    
    /**
     * Get recommendations for improving the configuration.
     */
    fun getConfigurationRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val silencedApps = schedulePreferences.silencedApps
        val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
        
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                if (!channelManager.areChannelsProperlyConfigured()) {
                    recommendations.add("Configure notification channels for better per-app control")
                }
                if (silencedApps.isEmpty() && alwaysAllowedApps.isEmpty()) {
                    recommendations.add("Configure at least some apps for silencing or allowing")
                }
            }
            
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                recommendations.add("Consider updating to Android 8.0+ for better notification control")
                if (silencedApps.isEmpty() && alwaysAllowedApps.isEmpty()) {
                    recommendations.add("Configure apps for silencing to benefit from DND features")
                }
            }
            
            else -> {
                recommendations.add("Android version too old - limited DND functionality available")
                recommendations.add("Consider updating to Android 6.0+ for DND support")
            }
        }
        
        return recommendations
    }
    
    /**
     * Suppress notification audio by reducing notification stream volume.
     */
    private fun suppressNotificationAudio() {
        try {
            // Store original volume
            val originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            
            // Set notification stream volume to 0
            audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, 0, 0)
            Log.d(TAG, "Notification audio suppressed (volume: $originalVolume -> 0)")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to suppress notification audio", e)
        }
    }
    
    /**
     * Apply conservative audio suppression during silencing hours.
     * This reduces notification volume but doesn't mute completely.
     */
    private fun applyConservativeAudioSuppression() {
        try {
            // Store original volume
            val originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            
            // Reduce notification stream volume to 20% of original (not completely silent)
            val reducedVolume = maxOf(1, (originalVolume * 0.2).toInt())
            audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, reducedVolume, 0)
            
            Log.d(TAG, "Applied conservative audio suppression (volume: $originalVolume -> $reducedVolume)")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to apply conservative audio suppression", e)
        }
    }
    
    /**
     * Apply enhanced channel muting using BuzzKill technique
     * This provides maximum silencing effect by temporarily muting existing channels
     */
    private fun applyEnhancedChannelMuting() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val silencedApps = schedulePreferences.silencedApps
                
                silencedApps.forEach { packageName ->
                    // Apply enhanced channel muting for each silenced app
                    channelManager.muteExistingChannelsTemporarily(packageName)
                }
                
                Log.d(TAG, "Applied enhanced channel muting for ${silencedApps.size} silenced apps")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to apply enhanced channel muting", e)
            }
        }
        
        // Also apply aggressive audio suppression for maximum silencing effect
        applyAggressiveAudioSuppression()
    }

    /**
     * Apply aggressive audio suppression to prevent notification sounds during silencing
     * This is more aggressive than conservative suppression and sets volume to 0
     */
    private fun applyAggressiveAudioSuppression() {
        try {
            // Store original volume
            val originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            
            // Set notification stream volume to 0 (completely silent)
            audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, 0, 0)
            
            Log.d(TAG, "Applied aggressive audio suppression (volume: $originalVolume -> 0)")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to apply aggressive audio suppression", e)
        }
    }
    
    /**
     * Restore audio from conservative suppression.
     */
    private fun restoreConservativeAudioSuppression() {
        try {
            val originalVolume = preferences.getInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, -1)
            if (originalVolume >= 0) {
                audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, originalVolume, 0)
                Log.d(TAG, "Restored notification audio from conservative suppression (volume: -> $originalVolume)")
                
                // Clear the stored volume
                preferences.edit().remove(PREFS_ORIGINAL_NOTIFICATION_VOLUME).apply()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore notification audio from conservative suppression", e)
        }
    }

    /**
     * Restore enhanced channel muting by restoring original channel settings
     */
    private fun restoreEnhancedChannelMuting() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val silencedApps = schedulePreferences.silencedApps
                
                silencedApps.forEach { packageName ->
                    // Restore original channel settings for each silenced app
                    channelManager.restoreOriginalChannels(packageName)
                }
                
                Log.d(TAG, "Restored original channels for ${silencedApps.size} silenced apps")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restore enhanced channel muting", e)
            }
        }
        
        // Also restore audio suppression
        restoreConservativeAudioSuppression()
    }
}
