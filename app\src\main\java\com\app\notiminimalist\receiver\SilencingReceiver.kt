package com.app.notiminimalist.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.service.AlarmScheduler
import com.app.notiminimalist.service.DndManager
import kotlinx.coroutines.*

/**
 * BroadcastReceiver that handles scheduled silencing events.
 * Responds to alarms to start/end notification silencing and device boot events.
 */
class SilencingReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "SilencingReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received broadcast: ${intent.action}")
        
        when (intent.action) {
            AlarmScheduler.ACTION_START_SILENCING -> {
                handleStartSilencing(context)
            }
            AlarmScheduler.ACTION_END_SILENCING -> {
                handleEndSilencing(context)
            }
            Intent.ACTION_BOOT_COMPLETED -> {
                handleBootCompleted(context)
            }
            Intent.ACTION_TIME_CHANGED -> {
                handleTimeChanged(context)
            }
            Intent.ACTION_TIMEZONE_CHANGED -> {
                handleTimeZoneChanged(context)
            }
        }
    }
    
    /**
     * Handle the start of scheduled silencing.
     */
    private fun handleStartSilencing(context: Context) {
        Log.d(TAG, "Starting scheduled silencing")
        
        val schedulePreferences = SchedulePreferences(context)
        if (!schedulePreferences.isSilencingEnabled) {
            Log.d(TAG, "Silencing is disabled, skipping start event")
            return
        }
        
        val dndManager = DndManager(context)
        val success = dndManager.enableDnd()
        
        if (success) {
            Log.i(TAG, "Successfully started notification silencing")
        } else {
            Log.w(TAG, "Failed to start notification silencing")
        }
        
        // Schedule the next start alarm (for tomorrow)
        val alarmScheduler = AlarmScheduler(context)
        CoroutineScope(Dispatchers.IO).launch {
            try {
                alarmScheduler.scheduleAlarms()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to schedule alarms after start silencing", e)
            }
        }
    }
    
    /**
     * Handle the end of scheduled silencing.
     */
    private fun handleEndSilencing(context: Context) {
        Log.d(TAG, "Ending scheduled silencing")
        
        val schedulePreferences = SchedulePreferences(context)
        if (!schedulePreferences.isSilencingEnabled) {
            Log.d(TAG, "Silencing is disabled, skipping end event")
            return
        }
        
        val dndManager = DndManager(context)
        val success = dndManager.disableDnd()
        
        if (success) {
            Log.i(TAG, "Successfully ended notification silencing")
        } else {
            Log.w(TAG, "Failed to end notification silencing")
        }
        
        // Schedule the next end alarm (for tomorrow)
        val alarmScheduler = AlarmScheduler(context)
        CoroutineScope(Dispatchers.IO).launch {
            try {
                alarmScheduler.scheduleAlarms()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to schedule alarms after end silencing", e)
            }
        }
    }
    
    /**
     * Handle device boot completion.
     * Reschedule alarms if silencing is enabled.
     */
    private fun handleBootCompleted(context: Context) {
        Log.d(TAG, "Device boot completed, rescheduling alarms")
        
        val schedulePreferences = SchedulePreferences(context)
        if (schedulePreferences.isSilencingEnabled) {
            val alarmScheduler = AlarmScheduler(context)
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    alarmScheduler.scheduleAlarms()
                    Log.i(TAG, "Rescheduled alarms after boot")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reschedule alarms after boot", e)
                }
            }
        } else {
            Log.d(TAG, "Silencing disabled, no alarms to reschedule")
        }
    }
    
    /**
     * Handle system time changes.
     * Reschedule alarms to account for time changes.
     */
    private fun handleTimeChanged(context: Context) {
        Log.d(TAG, "System time changed, rescheduling alarms")
        
        val schedulePreferences = SchedulePreferences(context)
        if (schedulePreferences.isSilencingEnabled) {
            val alarmScheduler = AlarmScheduler(context)
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    alarmScheduler.rescheduleAlarms()
                    Log.i(TAG, "Rescheduled alarms after time change")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reschedule alarms after time change", e)
                }
            }
        }
    }
    
    /**
     * Handle timezone changes.
     * Reschedule alarms to account for timezone changes.
     */
    private fun handleTimeZoneChanged(context: Context) {
        Log.d(TAG, "Timezone changed, rescheduling alarms")
        
        val schedulePreferences = SchedulePreferences(context)
        if (schedulePreferences.isSilencingEnabled) {
            val alarmScheduler = AlarmScheduler(context)
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    alarmScheduler.rescheduleAlarms()
                    Log.i(TAG, "Rescheduled alarms after timezone change")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reschedule alarms after timezone change", e)
                }
            }
        }
    }
}
