package com.app.notiminimalist

import com.app.notiminimalist.data.AppInfo
import com.app.notiminimalist.data.SilencingMode
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class to verify app filtering logic works correctly across different device types.
 */
class AppFilteringTest {

    @Test
    fun testUserAppsAreAlwaysShown() {
        val userApp = AppInfo(
            packageName = "com.example.userapp",
            appName = "User App",
            icon = null,
            isSystemApp = false
        )
        
        assertTrue("User apps should always be shown", userApp.shouldShow())
    }

    @Test
    fun testCriticalSystemAppsAreHidden() {
        val criticalApps = listOf(
            "android",
            "com.android.providers.settings",
            "com.android.systemui",
            "com.android.keychain",
            "com.android.inputmethod.latin",
            "com.android.server.telecom",
            "com.qualcomm.qti.services.systemhelper",
            "vendor.qti.hardware.servicetracker"
        )
        
        criticalApps.forEach { packageName ->
            val systemApp = AppInfo(
                packageName = packageName,
                appName = "System App",
                icon = null,
                isSystemApp = true
            )
            
            assertFalse("Critical system app $packageName should be hidden", systemApp.shouldShow())
        }
    }

    @Test
    fun testManufacturerAppsAreShown() {
        val manufacturerApps = listOf(
            // Samsung
            "com.samsung.android.messaging" to "Samsung Messages",
            "com.samsung.android.email.provider" to "Samsung Email",
            "com.sec.android.app.clockpackage" to "Samsung Clock",
            
            // Xiaomi
            "com.miui.securitycenter" to "Security",
            "com.xiaomi.market" to "Mi Store",
            "com.miui.notes" to "Notes",
            
            // OnePlus
            "net.oneplus.launcher" to "OnePlus Launcher",
            "com.oneplus.security" to "OnePlus Security",
            
            // Huawei
            "com.huawei.appmarket" to "AppGallery",
            "com.huawei.android.launcher" to "Huawei Launcher",
            
            // Google
            "com.google.android.apps.messaging" to "Messages",
            "com.google.android.gm" to "Gmail",
            "com.android.chrome" to "Chrome"
        )
        
        manufacturerApps.forEach { (packageName, appName) ->
            val manufacturerApp = AppInfo(
                packageName = packageName,
                appName = appName,
                icon = null,
                isSystemApp = true
            )
            
            assertTrue("Manufacturer app $packageName should be shown", manufacturerApp.shouldShow())
        }
    }

    @Test
    fun testPopularAppsAreShown() {
        val popularApps = listOf(
            "com.whatsapp" to "WhatsApp",
            "org.telegram.messenger" to "Telegram",
            "com.facebook.orca" to "Messenger",
            "com.instagram.android" to "Instagram",
            "com.twitter.android" to "Twitter",
            "com.spotify.music" to "Spotify",
            "com.netflix.mediaclient" to "Netflix"
        )
        
        popularApps.forEach { (packageName, appName) ->
            val popularApp = AppInfo(
                packageName = packageName,
                appName = appName,
                icon = null,
                isSystemApp = false
            )
            
            assertTrue("Popular app $packageName should be shown", popularApp.shouldShow())
        }
    }

    @Test
    fun testSettingsAppsAreHidden() {
        val settingsApps = listOf(
            "com.android.settings" to "Settings",
            "com.samsung.android.settings" to "Samsung Settings",
            "com.miui.securitycenter" to "Security Center", // This might be shown due to name
            "com.oneplus.settings" to "OnePlus Settings"
        )
        
        settingsApps.forEach { (packageName, appName) ->
            val settingsApp = AppInfo(
                packageName = packageName,
                appName = appName,
                icon = null,
                isSystemApp = true
            )
            
            // Most settings apps should be hidden, but some security apps might be shown
            if (appName.lowercase().contains("security") && !appName.lowercase().contains("settings")) {
                // Security apps might be shown as they can send notifications
                // This is acceptable behavior
            } else {
                assertFalse("Settings app $packageName should be hidden", settingsApp.shouldShow())
            }
        }
    }

    @Test
    fun testNotiminimalistAppIsHidden() {
        val notiminimalistApp = AppInfo(
            packageName = "com.app.notiminimalist",
            appName = "Notiminimalist",
            icon = null,
            isSystemApp = false
        )
        
        assertFalse("Notiminimalist app should hide itself", notiminimalistApp.shouldShow())
    }
}
