package com.app.notiminimalist.service

import android.app.Notification
import android.app.NotificationManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.data.SilencingMode
import java.time.LocalTime

/**
 * NotificationListenerService implementation for selective auto-dismissing notifications
 * based on user-configured silencing rules during scheduled hours.
 * 
 * This service provides granular per-app notification control by intercepting
 * and dismissing notifications ONLY from apps marked as SILENCED, giving users
 * the experience that silenced notifications never showed up while allowing
 * ALWAYS_ALLOWED apps to continue working normally.
 * 
 * Key Features:
 * - Only dismisses notifications from apps explicitly marked as SILENCED
 * - Preserves system and critical notifications (alarms, calls, system alerts)
 * - Works in coordination with DndManager for sound control
 * - Allows ALWAYS_ALLOWED apps to bypass all silencing
 */
class NotiminimalistNotificationListener : NotificationListenerService() {
    
    private lateinit var schedulePreferences: SchedulePreferences
    private lateinit var dndManager: DndManager
    private lateinit var audioManager: AudioManager
    private var originalNotificationVolume: Int = -1
    
    companion object {
        private const val TAG = "NotiminimalistListener"
    }
    
    override fun onCreate() {
        super.onCreate()
        schedulePreferences = SchedulePreferences(this)
        dndManager = DndManager(this)
        audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        Log.d(TAG, "NotificationListenerService created")
    }
    
    override fun onNotificationPosted(sbn: StatusBarNotification) {
        super.onNotificationPosted(sbn)
        
        try {
            // Only process notifications when silencing is enabled
            if (!schedulePreferences.isSilencingEnabled) {
                Log.v(TAG, "Silencing disabled, allowing notification: ${sbn.packageName}")
                return
            }
            
            // Check if we're currently in silencing hours
            if (!isCurrentlyInSilencingHours()) {
                Log.v(TAG, "Outside silencing hours, allowing notification: ${sbn.packageName}")
                return
            }
            
            val packageName = sbn.packageName
            val silencingMode = schedulePreferences.getAppSilencingMode(packageName)
            
            // Skip system and critical notifications
            if (isSystemOrCriticalNotification(sbn)) {
                Log.d(TAG, "Allowing system/critical notification: $packageName")
                return
            }
            
            when (silencingMode) {
                SilencingMode.SILENCED -> {
                    // STEP 1: Pre-emptive audio suppression (BEFORE sound can play)
                    suppressNotificationAudioPreemptively()
                    
                    // STEP 2: Auto-dismiss silenced notifications immediately and aggressively
                    dismissNotificationAggressively(sbn)
                    
                    // STEP 3: Restore audio after a short delay
                    restoreNotificationAudioDelayed()
                    
                    Log.d(TAG, "Silenced notification from: $packageName (audio suppressed + dismissed)")
                }
                SilencingMode.ALWAYS_ALLOWED -> {
                    // Let always allowed notifications through
                    Log.d(TAG, "Allowing notification from always allowed app: $packageName")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing notification from ${sbn.packageName}", e)
        }
    }
    
    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        super.onNotificationRemoved(sbn)
        Log.v(TAG, "Notification removed: ${sbn.packageName}")
    }
    
    override fun onListenerConnected() {
        super.onListenerConnected()
        Log.i(TAG, "NotificationListenerService connected")
        
        try {
            // Clear any existing notifications from silenced apps if we're in silencing hours
            if (schedulePreferences.isSilencingEnabled && isCurrentlyInSilencingHours()) {
                clearExistingSilencedNotifications()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during listener connection setup", e)
        }
    }
    
    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        Log.i(TAG, "NotificationListenerService disconnected")
        
        // Attempt to reconnect
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            requestRebind(ComponentName(this, NotiminimalistNotificationListener::class.java))
        }
    }
    
    /**
     * Dismiss a specific notification
     */
    private fun dismissNotification(sbn: StatusBarNotification) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                cancelNotification(sbn.key)
                Log.v(TAG, "Dismissed notification: ${sbn.packageName} via key")
            } else {
                @Suppress("DEPRECATION")
                cancelNotification(sbn.packageName, sbn.tag, sbn.id)
                Log.v(TAG, "Dismissed notification: ${sbn.packageName} via legacy method")
            }
        } catch (e: SecurityException) {
            Log.w(TAG, "Failed to dismiss notification due to security restriction: ${sbn.packageName}", e)
        } catch (e: IllegalArgumentException) {
            Log.w(TAG, "Failed to dismiss notification due to invalid arguments: ${sbn.packageName}", e)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to dismiss notification: ${sbn.packageName}", e)
        }
    }
    
    /**
     * Aggressively dismiss a notification with multiple attempts
     */
    private fun dismissNotificationAggressively(sbn: StatusBarNotification) {
        try {
            // First attempt - immediate dismissal
            dismissNotification(sbn)
            
            // Second attempt - try alternative methods
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // Try to cancel by key again with a slight delay
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        cancelNotification(sbn.key)
                        Log.v(TAG, "Aggressively dismissed notification: ${sbn.packageName}")
                    } catch (e: Exception) {
                        Log.w(TAG, "Secondary dismissal attempt failed: ${sbn.packageName}", e)
                    }
                }, 10) // 10ms delay
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to aggressively dismiss notification: ${sbn.packageName}", e)
        }
    }
    
    /**
     * Check if current time is within silencing hours
     */
    private fun isCurrentlyInSilencingHours(): Boolean {
        val now = LocalTime.now()
        val startTime = schedulePreferences.startTime
        val endTime = schedulePreferences.endTime
        
        return if (startTime.isBefore(endTime)) {
            // Same day range (e.g., 9 AM - 5 PM)
            !now.isBefore(startTime) && now.isBefore(endTime)
        } else {
            // Cross midnight range (e.g., 10 PM - 7 AM)
            !now.isBefore(startTime) || now.isBefore(endTime)
        }
    }
    
    /**
     * Check if a notification is from a system app or is critical and should not be silenced
     */
    private fun isSystemOrCriticalNotification(sbn: StatusBarNotification): Boolean {
        val packageName = sbn.packageName
        val notification = sbn.notification
        
        // Don't silence system-critical packages
        if (packageName == "android" || 
            packageName.startsWith("com.android.systemui") ||
            packageName.startsWith("com.android.phone") ||
            packageName.startsWith("com.android.dialer")) {
            return true
        }
        
        // Don't silence critical notification categories
        if (notification.category == Notification.CATEGORY_ALARM ||
            notification.category == Notification.CATEGORY_CALL ||
            notification.category == Notification.CATEGORY_SYSTEM) {
            return true
        }
        
        // Don't silence ongoing notifications (calls, music, etc.)
        if (notification.flags and Notification.FLAG_ONGOING_EVENT != 0) {
            return true
        }
        
        // Don't silence high priority notifications from always allowed apps
        val silencingMode = schedulePreferences.getAppSilencingMode(packageName)
        if (silencingMode == SilencingMode.ALWAYS_ALLOWED) {
            // For API 26+, use importance instead of deprecated priority
            val isHighPriority = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                notification.channelId != null && 
                try {
                    val notificationManager = this.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    val channel = notificationManager.getNotificationChannel(notification.channelId)
                    channel?.importance == NotificationManager.IMPORTANCE_HIGH
                } catch (e: Exception) {
                    false
                }
            } else {
                @Suppress("DEPRECATION")
                notification.priority >= Notification.PRIORITY_HIGH
            }
            
            if (isHighPriority) {
                return true
            }
        }
        
        return false
    }
    

    /**
     * Clear existing notifications from silenced apps when listener connects
     * during silencing hours
     */
    private fun clearExistingSilencedNotifications() {
        try {
            val activeNotifications = activeNotifications ?: return
            
            activeNotifications.forEach { sbn ->
                val packageName = sbn.packageName
                val silencingMode = schedulePreferences.getAppSilencingMode(packageName)
                
                when (silencingMode) {
                    SilencingMode.SILENCED -> {
                        dismissNotificationAggressively(sbn)
                        Log.d(TAG, "Cleared existing notification from silenced app: $packageName")
                    }
                    SilencingMode.ALWAYS_ALLOWED -> {
                        // Leave always allowed notifications
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing existing silenced notifications", e)
        }
    }
    
    /**
     * Check if a notification should be dismissed based on its properties
     */
    private fun shouldDismissNotification(notification: Notification): Boolean {
        // Don't dismiss certain critical notifications
        return when {
            // Keep ongoing notifications (like calls, music playback)
            notification.flags and Notification.FLAG_ONGOING_EVENT != 0 -> false
            
            // Keep high priority notifications if they're from always allowed apps
            // Use modern importance checking for API 26+, fallback to priority for older versions
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && notification.channelId != null -> {
                try {
                    val notificationManager = this.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    val channel = notificationManager.getNotificationChannel(notification.channelId)
                    channel?.importance == NotificationManager.IMPORTANCE_HIGH
                } catch (e: Exception) {
                    false
                }
            }
            Build.VERSION.SDK_INT < Build.VERSION_CODES.O -> {
                @Suppress("DEPRECATION")
                notification.priority >= Notification.PRIORITY_HIGH
            }
            
            // Keep system alerts and alarms
            notification.category == Notification.CATEGORY_ALARM -> false
            notification.category == Notification.CATEGORY_CALL -> false
            notification.category == Notification.CATEGORY_SYSTEM -> false
            
            else -> true
        }
    }
    
    /**
     * Get notification statistics for debugging/monitoring
     */
    fun getNotificationStats(): Map<String, Int> {
        return try {
            val activeNotifications = activeNotifications ?: return emptyMap()
            val stats = mutableMapOf<String, Int>()
            
            activeNotifications.groupBy { it.packageName }.forEach { (packageName, notifications) ->
                stats[packageName] = notifications.size
            }
            
            stats
        } catch (e: Exception) {
            Log.e(TAG, "Error getting notification stats", e)
            emptyMap()
        }
    }
    
    /**
     * Temporarily suppress notification audio to prevent sound from silenced apps.
     * This is used as a quick intervention when a silenced notification arrives.
     */
    private fun suppressNotificationAudio() {
        try {
            // Store the current volume only if we haven't stored it yet
            if (originalNotificationVolume == -1) {
                originalNotificationVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
                Log.v(TAG, "Stored original notification volume: $originalNotificationVolume")
            }
            
            // Temporarily set notification volume to 0
            audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, 0, 0)
            Log.v(TAG, "Temporarily suppressed notification audio")
            
        } catch (e: Exception) {
            Log.w(TAG, "Failed to suppress notification audio", e)
        }
    }

    /**
     * Pre-emptively suppress notification audio to prevent sound from silenced apps.
     * This is applied before the notification's audio can potentially play.
     */
    private fun suppressNotificationAudioPreemptively() {
        try {
            // Store the current volume only if we haven't stored it yet
            if (originalNotificationVolume == -1) {
                originalNotificationVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
                Log.v(TAG, "Stored original notification volume for pre-emptive suppression: $originalNotificationVolume")
            }
            
            // Temporarily set notification volume to 0
            audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, 0, 0)
            Log.v(TAG, "Pre-emptively suppressed notification audio")
            
        } catch (e: Exception) {
            Log.w(TAG, "Failed to pre-emptively suppress notification audio", e)
        }
    }
    
    /**
     * Restore notification audio after a short delay.
     * This allows time for the silenced notification to be dismissed.
     */
    private fun restoreNotificationAudioDelayed() {
        // Restore audio after 100ms to allow the dismissal to complete
        Handler(Looper.getMainLooper()).postDelayed({
            restoreNotificationAudio()
        }, 100)
    }
    
    /**
     * Restore the original notification audio volume.
     */
    private fun restoreNotificationAudio() {
        try {
            if (originalNotificationVolume >= 0) {
                audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, originalNotificationVolume, 0)
                Log.v(TAG, "Restored notification audio to volume: $originalNotificationVolume")
                originalNotificationVolume = -1 // Reset for next use
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to restore notification audio", e)
        }
    }
}