<lint-module
    format="1"
    dir="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app"
    name=":app"
    type="APP"
    maven="Notiminimalist:app:unspecified"
    agpVersion="8.12.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-36\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-36"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
