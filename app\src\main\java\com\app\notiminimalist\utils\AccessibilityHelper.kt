package com.app.notiminimalist.utils

import android.content.Context
import android.view.accessibility.AccessibilityManager
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.*
import androidx.compose.ui.state.ToggleableState
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Accessibility utility class for Notiminimalist app.
 * Provides consistent accessibility patterns and helpers.
 */
object AccessibilityHelper {
    
    /**
     * Check if accessibility services are enabled.
     */
    fun isAccessibilityEnabled(context: Context): Boolean {
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        return accessibilityManager.isEnabled
    }
    
    /**
     * Check if TalkBack or other screen readers are enabled.
     */
    fun isScreenReaderEnabled(context: Context): Boolean {
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        return accessibilityManager.isTouchExplorationEnabled
    }
    
    /**
     * Get recommended font scale for better readability.
     */
    fun getAccessibleFontScale(context: Context): Float {
        val fontScale = context.resources.configuration.fontScale
        return when {
            fontScale <= 1.0f -> 1.0f
            fontScale <= 1.15f -> 1.15f
            fontScale <= 1.3f -> 1.3f
            else -> 1.5f // Cap at 1.5x for layout stability
        }
    }
    
    /**
     * Get accessible text size based on system font scale.
     */
    fun getAccessibleTextSize(baseSize: TextUnit, context: Context): TextUnit {
        val scale = getAccessibleFontScale(context)
        return baseSize * scale
    }
    
    /**
     * Get accessible spacing for better touch targets.
     */
    fun getAccessibleSpacing(baseSpacing: Dp, context: Context): Dp {
        return if (isAccessibilityEnabled(context)) {
            baseSpacing * 1.2f
        } else {
            baseSpacing
        }
    }
    
    /**
     * Get minimum touch target size (48dp as per Material Design guidelines).
     */
    fun getMinimumTouchTargetSize(): Dp = 48.dp
    
    /**
     * Create comprehensive semantics for interactive elements.
     */
    fun createInteractiveSemantics(
        description: String,
        role: Role = Role.Button,
        onClick: (() -> Unit)? = null,
        enabled: Boolean = true,
        selected: Boolean? = null,
        expanded: Boolean? = null
    ): SemanticsPropertyReceiver.() -> Unit = {
        contentDescription = description
        this.role = role
        
        if (!enabled) {
            disabled()
        }
        
        selected?.let { 
            this.selected = it
        }
        
        expanded?.let {
            // Expanded state for accessibility
        }
        
        onClick?.let { clickAction ->
            onClick(label = description) {
                clickAction()
                true
            }
        }
    }
    
    /**
     * Create semantics for informational content.
     */
    fun createInformationalSemantics(
        description: String,
        heading: Boolean = false,
        live: Boolean = false
    ): SemanticsPropertyReceiver.() -> Unit = {
        contentDescription = description
        
        if (heading) {
            heading()
        }
        
        if (live) {
            liveRegion = LiveRegionMode.Polite
        }
    }
    
    /**
     * Create semantics for form inputs.
     */
    fun createInputSemantics(
        label: String,
        value: String,
        required: Boolean = false,
        error: String? = null
    ): SemanticsPropertyReceiver.() -> Unit = {
        contentDescription = label
        text = AnnotatedString(value)
        
        if (required) {
            // Mark as required for screen readers
            contentDescription = "$label, required"
        }
        
        error?.let {
            // Set error state for accessibility
            contentDescription = "$label, $it"
        }
    }
    
    /**
     * Create semantics for toggle elements (switches, checkboxes).
     */
    fun createToggleSemantics(
        label: String,
        checked: Boolean,
        onToggle: () -> Unit
    ): SemanticsPropertyReceiver.() -> Unit = {
        contentDescription = "$label, ${if (checked) "enabled" else "disabled"}"
        role = Role.Switch
        toggleableState = ToggleableState(checked)
        
        onClick(label = if (checked) "Disable $label" else "Enable $label") {
            onToggle()
            true
        }
    }
    
    /**
     * Create semantics for navigation elements.
     */
    fun createNavigationSemantics(
        label: String,
        selected: Boolean = false,
        index: Int? = null,
        total: Int? = null
    ): SemanticsPropertyReceiver.() -> Unit = {
        val description = buildString {
            append(label)
            if (selected) append(", selected")
            if (index != null && total != null) {
                append(", tab ${index + 1} of $total")
            }
        }
        
        contentDescription = description
        role = Role.Tab
        this.selected = selected
    }
    
    /**
     * Create semantics for progress indicators.
     */
    fun createProgressSemantics(
        label: String,
        progress: Float? = null
    ): SemanticsPropertyReceiver.() -> Unit = {
        val description = if (progress != null) {
            "$label, ${(progress * 100).toInt()}% complete"
        } else {
            "$label, loading"
        }
        
        contentDescription = description
        
        progress?.let {
            this.progressBarRangeInfo = ProgressBarRangeInfo(it, 0f..1f)
        }
    }
}

/**
 * Composable function to get accessible colors with proper contrast.
 */
@Composable
fun getAccessibleColors(
    isHighContrast: Boolean = false
): AccessibleColorScheme {
    val context = LocalContext.current
    val isScreenReaderEnabled = remember { AccessibilityHelper.isScreenReaderEnabled(context) }
    
    return if (isHighContrast || isScreenReaderEnabled) {
        AccessibleColorScheme(
            primary = Color(0xFF000000),
            onPrimary = Color(0xFFFFFFFF),
            secondary = Color(0xFF444444),
            onSecondary = Color(0xFFFFFFFF),
            background = Color(0xFFFFFFFF),
            onBackground = Color(0xFF000000),
            surface = Color(0xFFF5F5F5),
            onSurface = Color(0xFF000000),
            error = Color(0xFFCC0000),
            onError = Color(0xFFFFFFFF)
        )
    } else {
        AccessibleColorScheme(
            primary = MaterialTheme.colorScheme.primary,
            onPrimary = MaterialTheme.colorScheme.onPrimary,
            secondary = MaterialTheme.colorScheme.secondary,
            onSecondary = MaterialTheme.colorScheme.onSecondary,
            background = MaterialTheme.colorScheme.background,
            onBackground = MaterialTheme.colorScheme.onBackground,
            surface = MaterialTheme.colorScheme.surface,
            onSurface = MaterialTheme.colorScheme.onSurface,
            error = MaterialTheme.colorScheme.error,
            onError = MaterialTheme.colorScheme.onError
        )
    }
}

/**
 * Data class for accessible color scheme.
 */
data class AccessibleColorScheme(
    val primary: Color,
    val onPrimary: Color,
    val secondary: Color,
    val onSecondary: Color,
    val background: Color,
    val onBackground: Color,
    val surface: Color,
    val onSurface: Color,
    val error: Color,
    val onError: Color
)

/**
 * Composable function to create accessible spacing.
 */
@Composable
fun AccessibleSpacer(height: Dp) {
    val context = LocalContext.current
    val accessibleHeight = AccessibilityHelper.getAccessibleSpacing(height, context)
    Spacer(modifier = Modifier.size(accessibleHeight))
}

/**
 * Extension function to make any composable more accessible.
 */
fun Modifier.accessibleClickable(
    description: String,
    enabled: Boolean = true,
    role: Role = Role.Button,
    onClick: () -> Unit
): Modifier = this.semantics {
    AccessibilityHelper.createInteractiveSemantics(
        description = description,
        role = role,
        onClick = onClick,
        enabled = enabled
    ).invoke(this)
}

/**
 * Extension function for accessible toggle elements.
 */
fun Modifier.accessibleToggle(
    label: String,
    checked: Boolean,
    onToggle: () -> Unit
): Modifier = this.semantics {
    AccessibilityHelper.createToggleSemantics(
        label = label,
        checked = checked,
        onToggle = onToggle
    ).invoke(this)
}