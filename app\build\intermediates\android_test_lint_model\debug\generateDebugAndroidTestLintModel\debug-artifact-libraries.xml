<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ffaa21cd382915b566a6d5339f22880d\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ffaa21cd382915b566a6d5339f22880d\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6e1700bc52105d60e567802566085d\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6e1700bc52105d60e567802566085d\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2b21dce8d89dafd13fa35f750ae20858\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2b21dce8d89dafd13fa35f750ae20858\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac5730e1294228dab72f8d7b7acde09d\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ac5730e1294228dab72f8d7b7acde09d\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a39290aa22095b2e488f61663c5a0a3a\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a39290aa22095b2e488f61663c5a0a3a\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8b4c268e3ae6251dc57e280dfdf4a9\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d8b4c268e3ae6251dc57e280dfdf4a9\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c17b34355c0c127c8cd28e77b5dbaa32\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c17b34355c0c127c8cd28e77b5dbaa32\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\25b0d00c93f176cd5facfe58e0d4e080\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\25b0d00c93f176cd5facfe58e0d4e080\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\623dcb6f1b7edf26164c9621c45bbee3\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\623dcb6f1b7edf26164c9621c45bbee3\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6717377685af3be63bcfc49a43e8e806\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6717377685af3be63bcfc49a43e8e806\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a87ea0a2ae3b302fa8b2e67fc66b56aa\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a87ea0a2ae3b302fa8b2e67fc66b56aa\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b7ad5177f22eeb2e084bc292c5faf8\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b7ad5177f22eeb2e084bc292c5faf8\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0d56488bef50ee50e908eddadde8d7d4\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0d56488bef50ee50e908eddadde8d7d4\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\540ea2c2ddb1c4107494f3c5556a991e\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\540ea2c2ddb1c4107494f3c5556a991e\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a2a03e508c40aa0d352139dfa2f950b\transformed\ui-test-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a2a03e508c40aa0d352139dfa2f950b\transformed\ui-test-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\13ef5695d785179f0f728fabb08f9d94\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\13ef5695d785179f0f728fabb08f9d94\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7732d0a0f5d98bbbe112de2864b3a0ce\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7732d0a0f5d98bbbe112de2864b3a0ce\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a9e5bcea40c83bf517e31592dfc0951\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a9e5bcea40c83bf517e31592dfc0951\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f24571c21c1144f98162a78f929193d\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f24571c21c1144f98162a78f929193d\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\18b22e515b4201f34167a2d64b698d76\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\18b22e515b4201f34167a2d64b698d76\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-junit4-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf1bb5f8b35a4a1dc30c8a653544c0fc\transformed\ui-test-junit4-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-junit4-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf1bb5f8b35a4a1dc30c8a653544c0fc\transformed\ui-test-junit4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\28e46119fa72ce1e71c4a15b46851539\transformed\junit-1.1.5\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\28e46119fa72ce1e71c4a15b46851539\transformed\junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e8e1b25a4fc20806f94ab6121848865\transformed\espresso-core-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4e8e1b25a4fc20806f94ab6121848865\transformed\espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf18bfe4545cf27571e3d8e993ede7f7\transformed\core-1.5.0\jars\classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bf18bfe4545cf27571e3d8e993ede7f7\transformed\core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\882d27877add86e2b335fbe428c03e77\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\882d27877add86e2b335fbe428c03e77\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4aed5566519b0f81f4a189775ecc2b59\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4aed5566519b0f81f4a189775ecc2b59\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\11521babd937ead1f6e6bc8ced683418\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\11521babd937ead1f6e6bc8ced683418\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a793a9f776500db9a512c994363d2c86\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a793a9f776500db9a512c994363d2c86\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e2bee68cc41cebd450f9d464130954f\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e2bee68cc41cebd450f9d464130954f\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d14f8cfa4cfb4476a32327ac6fc5a4e\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1d14f8cfa4cfb4476a32327ac6fc5a4e\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a2192c77ee41596542d323b8d5f33249\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a2192c77ee41596542d323b8d5f33249\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\396dea2693102db46df23fdf42494efa\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\396dea2693102db46df23fdf42494efa\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2b039c83fe884aa92ec31c67dc5c012\transformed\runner-1.5.2\jars\classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2b039c83fe884aa92ec31c67dc5c012\transformed\runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\beae50a979f6920d018747ce984f505d\transformed\storage-1.4.2\jars\classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\beae50a979f6920d018747ce984f505d\transformed\storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\15933055a110ff30da29aca50d9de7f5\transformed\monitor-1.6.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\15933055a110ff30da29aca50d9de7f5\transformed\monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b4750176c9c8a10b9dc7a04a024efc29\transformed\annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b4750176c9c8a10b9dc7a04a024efc29\transformed\annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a135ce44b13cb25ba588df2ef22b70ce\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a135ce44b13cb25ba588df2ef22b70ce\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb97008c560988d20a76b01e2082e14\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb97008c560988d20a76b01e2082e14\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\546813cb354de50c73f35e380eb3ae54\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\546813cb354de50c73f35e380eb3ae54\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e0a2fec9cdf904d492cffe4e0ba5405\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e0a2fec9cdf904d492cffe4e0ba5405\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\541c4938b27778b7c31e54ff56cc7acc\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\541c4938b27778b7c31e54ff56cc7acc\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.0\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b84aa3fcfa735d66aedbe95a87ec7a9a\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b84aa3fcfa735d66aedbe95a87ec7a9a\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-test-jvm\1.7.3\89e34400f452dab68fbb3caa66d854c89aaafa07\kotlinx-coroutines-test-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1956927975d4f2df66db537f04eebd\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1956927975d4f2df66db537f04eebd\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:2.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\2.0.2\516c03b21d50a644d538de0f0369c620989cd8f0\jsr305-2.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:2.0.2"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ab570376b2fc2f5e7699b570a8a0ba39\transformed\espresso-idling-resource-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ab570376b2fc2f5e7699b570a8a0ba39\transformed\espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2165b983087d9129cd0033deb133aeb9\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2165b983087d9129cd0033deb133aeb9\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d308d79b51f5c603a1feead4029c6fcd\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d308d79b51f5c603a1feead4029c6fcd\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\83a9f202729ca698293e6d420a5350d3\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\83a9f202729ca698293e6d420a5350d3\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b2a4632e43128804c18a10dbdfaf1e0\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b2a4632e43128804c18a10dbdfaf1e0\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e887e576d57b6e23af27a8269a5bd2f3\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e887e576d57b6e23af27a8269a5bd2f3\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10c2fdf6d9f565890bed39048e834aaf\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10c2fdf6d9f565890bed39048e834aaf\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
