[{"key": "androidx/compose/runtime/saveable/ListSaverKt$listSaver$1.class", "name": "androidx/compose/runtime/saveable/ListSaverKt$listSaver$1.class", "size": 3384, "crc": 905671927}, {"key": "androidx/compose/runtime/saveable/ListSaverKt.class", "name": "androidx/compose/runtime/saveable/ListSaverKt.class", "size": 2301, "crc": -2143846229}, {"key": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$1.class", "name": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$1.class", "size": 3808, "crc": -517060956}, {"key": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$2.class", "name": "androidx/compose/runtime/saveable/MapSaverKt$mapSaver$2.class", "size": 3210, "crc": -1782997031}, {"key": "androidx/compose/runtime/saveable/MapSaverKt.class", "name": "androidx/compose/runtime/saveable/MapSaverKt.class", "size": 2002, "crc": -525516425}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$1.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$1.class", "size": 3628, "crc": 423379260}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$2.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$mutableStateSaver$1$2.class", "size": 3293, "crc": 543400786}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt$rememberSaveable$1$1.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt$rememberSaveable$1$1.class", "size": 2630, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/RememberSaveableKt.class", "name": "androidx/compose/runtime/saveable/RememberSaveableKt.class", "size": 12391, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableHolder$valueProvider$1.class", "name": "androidx/compose/runtime/saveable/SaveableHolder$valueProvider$1.class", "size": 2937, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableHolder.class", "name": "androidx/compose/runtime/saveable/SaveableHolder.class", "size": 6238, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolder.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolder.class", "size": 1173, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$1.class", "size": 2332, "crc": -861124921}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$2.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion$Saver$2.class", "size": 1983, "crc": -901287244}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$Companion.class", "size": 1424, "crc": 1035906908}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$RegistryHolder$registry$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$RegistryHolder$registry$1.class", "size": 2033, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$RegistryHolder.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$RegistryHolder.class", "size": 3220, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1$invoke$$inlined$onDispose$1.class", "size": 2971, "crc": 863957289}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$1$1$1.class", "size": 4630, "crc": 49285657}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$2.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl$SaveableStateProvider$2.class", "size": 2354, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderImpl.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderImpl.class", "size": 12679, "crc": -3436083}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderKt$rememberSaveableStateHolder$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderKt$rememberSaveableStateHolder$1.class", "size": 1655, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateHolderKt.class", "name": "androidx/compose/runtime/saveable/SaveableStateHolderKt.class", "size": 4432, "crc": -213201361}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistry$Entry.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistry$Entry.class", "size": 607, "crc": 600563243}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistry.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistry.class", "size": 1625, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl$registerProvider$3.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl$registerProvider$3.class", "size": 2348, "crc": 301280422}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryImpl.class", "size": 8011, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryKt$LocalSaveableStateRegistry$1.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryKt$LocalSaveableStateRegistry$1.class", "size": 1377, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaveableStateRegistryKt.class", "name": "androidx/compose/runtime/saveable/SaveableStateRegistryKt.class", "size": 3119, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/Saver.class", "name": "androidx/compose/runtime/saveable/Saver.class", "size": 1156, "crc": -**********}, {"key": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$1.class", "name": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$1.class", "size": 1624, "crc": **********}, {"key": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$2.class", "name": "androidx/compose/runtime/saveable/SaverKt$AutoSaver$2.class", "size": 1225, "crc": 406808218}, {"key": "androidx/compose/runtime/saveable/SaverKt$Saver$1.class", "name": "androidx/compose/runtime/saveable/SaverKt$Saver$1.class", "size": 2503, "crc": 2092833680}, {"key": "androidx/compose/runtime/saveable/SaverKt.class", "name": "androidx/compose/runtime/saveable/SaverKt.class", "size": 2864, "crc": -1947082258}, {"key": "androidx/compose/runtime/saveable/SaverScope.class", "name": "androidx/compose/runtime/saveable/SaverScope.class", "size": 589, "crc": 622299896}, {"key": "META-INF/androidx.compose.runtime_runtime-saveable.version", "name": "META-INF/androidx.compose.runtime_runtime-saveable.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/runtime-saveable_release.kotlin_module", "name": "META-INF/runtime-saveable_release.kotlin_module", "size": 164, "crc": 1632253590}]