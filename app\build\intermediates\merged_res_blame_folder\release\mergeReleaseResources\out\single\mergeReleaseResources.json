[{"merged": "com.app.notiminimalist.app-release-43:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/xml_backup_rules.xml.flat", "source": "com.app.notiminimalist.app-main-44:/xml/backup_rules.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/drawable_ic_launcher_background.xml.flat", "source": "com.app.notiminimalist.app-main-44:/drawable/ic_launcher_background.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/xml_data_extraction_rules.xml.flat", "source": "com.app.notiminimalist.app-main-44:/xml/data_extraction_rules.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/drawable_app_logo.png.flat", "source": "com.app.notiminimalist.app-main-44:/drawable/app_logo.png"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "com.app.notiminimalist.app-pngs-39:/drawable-anydpi-v24/ic_launcher_foreground.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.app.notiminimalist.app-release-43:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.app.notiminimalist.app-main-44:/mipmap-hdpi/ic_launcher.webp"}]