1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.notiminimalist"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:5-85
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:22-82
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:22-65
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:10:5-52:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.Notiminimalist" >
34-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:18:9-52
35        <activity
35-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:19:9-29:20
36            android:name="com.app.notiminimalist.MainActivity"
36-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:20:13-41
37            android:exported="true"
37-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:21:13-36
38            android:label="@string/app_name"
38-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:22:13-45
39            android:theme="@style/Theme.Notiminimalist" >
39-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:23:13-56
40            <intent-filter>
40-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:24:13-28:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:25:17-69
41-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:25:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:27:17-77
43-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:27:27-74
44            </intent-filter>
45        </activity>
46
47        <receiver
47-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:31:9-41:20
48            android:name="com.app.notiminimalist.receiver.SilencingReceiver"
48-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:32:13-55
49            android:enabled="true"
49-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:33:13-35
50            android:exported="false" >
50-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:34:13-37
51            <intent-filter android:priority="1000" >
51-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:35:13-40:29
51-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:35:28-51
52                <action android:name="android.intent.action.BOOT_COMPLETED" />
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:17-79
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:25-76
53                <action android:name="android.intent.action.TIME_SET" />
53-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:17-73
53-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:25-70
54                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:17-81
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:25-78
55
56                <category android:name="android.intent.category.DEFAULT" />
56-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:17-76
56-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:27-73
57            </intent-filter>
58        </receiver>
59
60        <service
60-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:43:9-51:19
61            android:name="com.app.notiminimalist.service.NotiminimalistNotificationListener"
61-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:44:13-71
62            android:enabled="true"
62-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:45:13-35
63            android:exported="true"
63-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:46:13-36
64            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
64-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:47:13-87
65            <intent-filter>
65-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:48:13-50:29
66                <action android:name="android.service.notification.NotificationListenerService" />
66-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:49:17-99
66-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:49:25-96
67            </intent-filter>
68        </service>
69
70        <activity
70-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
71            android:name="androidx.compose.ui.tooling.PreviewActivity"
71-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
72            android:exported="true" />
72-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
73        <activity
73-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
74            android:name="androidx.activity.ComponentActivity"
74-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
75            android:exported="true" />
75-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
76
77        <provider
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
78            android:name="androidx.startup.InitializationProvider"
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
79            android:authorities="com.app.notiminimalist.androidx-startup"
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
80            android:exported="false" >
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
81            <meta-data
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
82                android:name="androidx.emoji2.text.EmojiCompatInitializer"
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
83                android:value="androidx.startup" />
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
84            <meta-data
84-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
85-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
86                android:value="androidx.startup" />
86-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
89                android:value="androidx.startup" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
90        </provider>
91
92        <receiver
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
93            android:name="androidx.profileinstaller.ProfileInstallReceiver"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
94            android:directBootAware="false"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
95            android:enabled="true"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
96            android:exported="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
97            android:permission="android.permission.DUMP" >
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
99                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
102                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
105                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
108                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
109            </intent-filter>
110        </receiver>
111    </application>
112
113</manifest>
