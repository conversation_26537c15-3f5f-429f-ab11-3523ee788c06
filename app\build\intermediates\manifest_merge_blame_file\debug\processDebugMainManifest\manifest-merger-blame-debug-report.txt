1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.notiminimalist"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:5-85
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:22-82
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:9:22-74
16
17    <permission
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
18        android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:11:5-53:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:12:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:13:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:14:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:15:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:16:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:17:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:18:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.Notiminimalist" >
35-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:19:9-52
36        <activity
36-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:20:9-30:20
37            android:name="com.app.notiminimalist.MainActivity"
37-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:21:13-41
38            android:exported="true"
38-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:22:13-36
39            android:label="@string/app_name"
39-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:23:13-45
40            android:theme="@style/Theme.Notiminimalist" >
40-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:24:13-56
41            <intent-filter>
41-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:25:13-29:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:26:17-69
42-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:26:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:28:17-77
44-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:28:27-74
45            </intent-filter>
46        </activity>
47
48        <receiver
48-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:32:9-42:20
49            android:name="com.app.notiminimalist.receiver.SilencingReceiver"
49-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:33:13-55
50            android:enabled="true"
50-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:34:13-35
51            android:exported="false" >
51-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:35:13-37
52            <intent-filter android:priority="1000" >
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:13-41:29
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:28-51
53                <action android:name="android.intent.action.BOOT_COMPLETED" />
53-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:17-79
53-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:25-76
54                <action android:name="android.intent.action.TIME_SET" />
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:17-73
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:25-70
55                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
55-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:17-81
55-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:25-78
56
57                <category android:name="android.intent.category.DEFAULT" />
57-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:40:17-76
57-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:40:27-73
58            </intent-filter>
59        </receiver>
60
61        <service
61-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:44:9-52:19
62            android:name="com.app.notiminimalist.service.NotiminimalistNotificationListener"
62-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:45:13-71
63            android:enabled="true"
63-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:46:13-35
64            android:exported="true"
64-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:47:13-36
65            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
65-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:48:13-87
66            <intent-filter>
66-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:49:13-51:29
67                <action android:name="android.service.notification.NotificationListenerService" />
67-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:50:17-99
67-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:50:25-96
68            </intent-filter>
69        </service>
70
71        <activity
71-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
72            android:name="androidx.compose.ui.tooling.PreviewActivity"
72-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
73            android:exported="true" />
73-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\846e86ee3246d5a9cea034b0aedc493e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
74        <activity
74-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
75            android:name="androidx.activity.ComponentActivity"
75-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
76            android:exported="true" />
76-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcf573098ddc91faa1d534b1e6ccf005\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
77
78        <provider
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
80            android:authorities="com.app.notiminimalist.androidx-startup"
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
90                android:value="androidx.startup" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
91        </provider>
92
93        <receiver
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
94            android:name="androidx.profileinstaller.ProfileInstallReceiver"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
95            android:directBootAware="false"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
96            android:enabled="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
97            android:exported="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
98            android:permission="android.permission.DUMP" >
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
100                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
103                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
106                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
109                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
110            </intent-filter>
111        </receiver>
112    </application>
113
114</manifest>
