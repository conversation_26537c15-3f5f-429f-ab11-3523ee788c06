package com.app.notiminimalist.service

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.app.notiminimalist.data.SchedulePreferences


import com.app.notiminimalist.receiver.SilencingReceiver
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.util.*

/**
 * Manages alarm scheduling for automatic notification silencing.
 * Schedules daily start and end events using AlarmManager.
 */
class AlarmScheduler(private val context: Context) {
    
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    private val schedulePreferences = SchedulePreferences(context)

    
    companion object {
        private const val TAG = "AlarmScheduler"
        private const val START_ALARM_REQUEST_CODE = 1001
        private const val END_ALARM_REQUEST_CODE = 1002
        
        const val ACTION_START_SILENCING = "com.app.notiminimalist.START_SILENCING"
        const val ACTION_END_SILENCING = "com.app.notiminimalist.END_SILENCING"
    }
    
    /**
     * Check if the app can schedule exact alarms.
     */
    fun canScheduleExactAlarms(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true // No special permission needed for API < 31
        }
    }
    
    /**
     * Schedule both start and end alarms for silencing.
     */
    suspend fun scheduleAlarms() {
        if (!canScheduleExactAlarms()) {
            Log.w(TAG, "Cannot schedule exact alarms - permission not granted")
            return
        }
        
        scheduleSimpleAlarms()
        Log.d(TAG, "Scheduled alarms using simple mode")
    }
    
    /**
     * Schedule alarms using simple mode (legacy).
     */
    private fun scheduleSimpleAlarms() {
        scheduleStartAlarm()
        scheduleEndAlarm()
    }
    

    
    /**
     * Cancel all scheduled alarms.
     */
    fun cancelAlarms() {
        cancelStartAlarm()
        cancelEndAlarm()
        Log.d(TAG, "Cancelled all alarms")
    }
    
    /**
     * Schedule the start silencing alarm.
     */
    private fun scheduleStartAlarm() {
        val startTime = schedulePreferences.startTime
        val triggerTime = calculateNextTriggerTime(startTime)
        
        val intent = Intent(context, SilencingReceiver::class.java).apply {
            action = ACTION_START_SILENCING
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            START_ALARM_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        scheduleExactAlarm(triggerTime, pendingIntent)
        Log.d(TAG, "Scheduled start alarm for ${Date(triggerTime)}")
    }
    
    /**
     * Schedule the end silencing alarm.
     */
    private fun scheduleEndAlarm() {
        val endTime = schedulePreferences.endTime
        val triggerTime = calculateNextTriggerTime(endTime)
        
        val intent = Intent(context, SilencingReceiver::class.java).apply {
            action = ACTION_END_SILENCING
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            END_ALARM_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        scheduleExactAlarm(triggerTime, pendingIntent)
        Log.d(TAG, "Scheduled end alarm for ${Date(triggerTime)}")
    }
    
    /**
     * Cancel the start silencing alarm.
     */
    private fun cancelStartAlarm() {
        val intent = Intent(context, SilencingReceiver::class.java).apply {
            action = ACTION_START_SILENCING
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            START_ALARM_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        alarmManager.cancel(pendingIntent)
    }
    
    /**
     * Cancel the end silencing alarm.
     */
    private fun cancelEndAlarm() {
        val intent = Intent(context, SilencingReceiver::class.java).apply {
            action = ACTION_END_SILENCING
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            END_ALARM_REQUEST_CODE,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        alarmManager.cancel(pendingIntent)
    }
    
    /**
     * Calculate the next trigger time for a given time of day.
     * If the time has already passed today, schedule for tomorrow.
     */
    private fun calculateNextTriggerTime(time: LocalTime): Long {
        val now = LocalDateTime.now()
        var targetDateTime = now.toLocalDate().atTime(time)
        
        // If the time has already passed today, schedule for tomorrow
        if (targetDateTime.isBefore(now)) {
            targetDateTime = targetDateTime.plusDays(1)
        }
        
        return targetDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }
    
    /**
     * Schedule an exact alarm with appropriate method based on API level.
     */
    private fun scheduleExactAlarm(triggerTime: Long, pendingIntent: PendingIntent) {
        try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Use setExactAndAllowWhileIdle for API 23+ to work in Doze mode
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                    // Use setExact for API 19-22
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                else -> {
                    // Use set for API < 19
                    alarmManager.set(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Failed to schedule exact alarm - permission denied", e)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to schedule alarm", e)
        }
    }
    
    /**
     * Reschedule alarms when settings change.
     */
    suspend fun rescheduleAlarms() {
        cancelAlarms()
        if (schedulePreferences.isSilencingEnabled) {
            scheduleAlarms()
        }
    }
    
    /**
     * Schedule an alarm for a specific date and time.
     */
    private fun scheduleEventAlarm(dateTime: LocalDateTime, action: String, requestCode: Int) {
        val triggerTime = dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        
        val intent = Intent(context, SilencingReceiver::class.java).apply {
            this.action = action
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        scheduleExactAlarm(triggerTime, pendingIntent)
        Log.d(TAG, "Scheduled $action for ${Date(triggerTime)}")
    }
    
    /**
     * Get a description of the next scheduled alarm.
     */
    suspend fun getNextAlarmDescription(): String {
        if (!schedulePreferences.isSilencingEnabled) {
            return "Scheduling disabled"
        }
        
        return getSimpleAlarmDescription()
    }
    
    private fun getSimpleAlarmDescription(): String {
        val startTime = schedulePreferences.startTime
        val endTime = schedulePreferences.endTime
        val nextStartTrigger = calculateNextTriggerTime(startTime)
        val nextEndTrigger = calculateNextTriggerTime(endTime)
        
        val nextTrigger = minOf(nextStartTrigger, nextEndTrigger)
        val isStartEvent = nextTrigger == nextStartTrigger
        
        val calendar = Calendar.getInstance().apply {
            timeInMillis = nextTrigger
        }
        
        val eventType = if (isStartEvent) "start" else "end"
        return "Next $eventType: ${android.text.format.DateFormat.format("MMM d, h:mm a", calendar)}"
    }
    

}
