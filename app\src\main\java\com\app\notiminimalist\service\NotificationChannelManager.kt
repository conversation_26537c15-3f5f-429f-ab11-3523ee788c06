package com.app.notiminimalist.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.data.SilencingMode

/**
 * Manages notification channels for API 26+ devices.
 * Handles per-app notification control through notification channels.
 */
class NotificationChannelManager(private val context: Context) {
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val schedulePreferences = SchedulePreferences(context)
    private val packageManager = context.packageManager
    
    companion object {
        private const val TAG = "NotificationChannelManager"
        private const val CHANNEL_PREFIX = "notiminimalist_"
    }
    
    /**
     * Check if notification channels are supported (API 26+).
     */
    fun areNotificationChannelsSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }
    
    /**
     * Update notification channels based on current app silencing configurations.
     * This should be called when silencing starts/stops and when app configurations change.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun updateNotificationChannels() {
        if (!areNotificationChannelsSupported()) {
            Log.d(TAG, "Notification channels not supported on this API level")
            return
        }
        
        try {
            // Get all apps with silencing configurations
            val silencedApps = schedulePreferences.silencedApps
            val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
            
            Log.d(TAG, "Updating notification channels for ${silencedApps.size} silenced apps and ${alwaysAllowedApps.size} always allowed apps")
            
            // Update channels for silenced apps
            silencedApps.forEach { packageName ->
                updateChannelForApp(packageName, SilencingMode.SILENCED)
            }
            
            // Update channels for always allowed apps
            alwaysAllowedApps.forEach { packageName ->
                updateChannelForApp(packageName, SilencingMode.ALWAYS_ALLOWED)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification channels", e)
        }
    }
    
    /**
     * Update notification channel for a specific app based on its silencing mode.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun updateChannelForApp(packageName: String, mode: SilencingMode) {
        try {
            val appName = getAppName(packageName) ?: packageName
            val channelId = "${CHANNEL_PREFIX}${packageName}"
            
            when (mode) {
                SilencingMode.SILENCED -> {
                    // Create or update channel with low importance during silencing hours
                    val channel = NotificationChannel(
                        channelId,
                        "$appName (Silenced)",
                        NotificationManager.IMPORTANCE_LOW
                    ).apply {
                        description = "Notifications from $appName - silenced during scheduled hours"
                        setSound(null, null)
                        enableVibration(false)
                        enableLights(false)
                    }
                    notificationManager.createNotificationChannel(channel)
                    Log.d(TAG, "Created/updated silenced channel for $appName")
                }
                
                SilencingMode.ALWAYS_ALLOWED -> {
                    // Create or update channel with high importance (always allowed)
                    val channel = NotificationChannel(
                        channelId,
                        "$appName (Always Allowed)",
                        NotificationManager.IMPORTANCE_HIGH
                    ).apply {
                        description = "Notifications from $appName - always allowed even during silencing"
                    }
                    notificationManager.createNotificationChannel(channel)
                    Log.d(TAG, "Created/updated always allowed channel for $appName")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification channel for $packageName", e)
        }
    }
    
    /**
     * Enable silencing for all configured apps by updating their channels.
     * This now uses a more aggressive approach to ensure sound silencing.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun enableSilencingChannels() {
        if (!areNotificationChannelsSupported()) return
        
        try {
            val silencedApps = schedulePreferences.silencedApps
            
            silencedApps.forEach { packageName ->
                // Strategy 1: Update our managed channel
                val channelId = "${CHANNEL_PREFIX}${packageName}"
                val appName = getAppName(packageName) ?: packageName
                
                val silencedChannel = NotificationChannel(
                    channelId,
                    "$appName (Silenced)",
                    NotificationManager.IMPORTANCE_MIN // Use IMPORTANCE_MIN for maximum silencing
                ).apply {
                    description = "Notifications from $appName - silenced during scheduled hours"
                    setSound(null, null)
                    enableVibration(false)
                    enableLights(false)
                    setShowBadge(false)
                    setBypassDnd(false) // Ensure it doesn't bypass DND if user has it enabled
                }
                notificationManager.createNotificationChannel(silencedChannel)
                Log.d(TAG, "Created/updated silenced channel for: $appName")
                
                // Strategy 2: Try to get and modify the app's existing channels
                try {
                    silenceExistingAppChannels(packageName)
                } catch (e: Exception) {
                    Log.w(TAG, "Could not modify existing channels for $packageName", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error enabling silencing channels", e)
        }
    }
    
    /**
     * Attempt to silence existing notification channels for an app.
     * This is more aggressive than the previous approach.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun silenceExistingAppChannels(packageName: String) {
        try {
            // Get all notification channels for the system
            val allChannels = notificationManager.notificationChannels
            
            // Create common channel IDs that apps typically use
            val commonChannelIds = listOf(
                "default",
                "general", 
                "notification",
                "notifications",
                "main",
                packageName,
                "${packageName}_default",
                "${packageName}_notifications",
                "miscellaneous"
            )
            
            // Create silent versions of common channel IDs for this app
            commonChannelIds.forEach { channelId ->
                val silencedChannel = NotificationChannel(
                    "${CHANNEL_PREFIX}${packageName}_${channelId}",
                    "${getAppName(packageName) ?: packageName} - $channelId (Silenced)",
                    NotificationManager.IMPORTANCE_MIN // Use IMPORTANCE_MIN for maximum silencing
                ).apply {
                    description = "Silenced notifications for $packageName - $channelId channel"
                    setSound(null, null)
                    enableVibration(false)
                    enableLights(false)
                    setShowBadge(false)
                    setBypassDnd(false)
                }
                notificationManager.createNotificationChannel(silencedChannel)
                Log.v(TAG, "Created silent channel: ${silencedChannel.id}")
            }
            
            Log.d(TAG, "Created ${commonChannelIds.size} silent channel variants for $packageName")
            
        } catch (e: Exception) {
            Log.w(TAG, "Error creating silent channels for $packageName", e)
        }
    }
    
    /**
     * Disable silencing for all configured apps by restoring their channel importance.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun disableSilencingChannels() {
        if (!areNotificationChannelsSupported()) return
        
        try {
            val silencedApps = schedulePreferences.silencedApps
            val alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
            
            // Restore silenced apps to default importance
            silencedApps.forEach { packageName ->
                val channelId = "${CHANNEL_PREFIX}${packageName}"
                val appName = getAppName(packageName) ?: packageName
                
                val restoredChannel = NotificationChannel(
                    channelId,
                    "$appName (Restored)",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Notifications from $appName - restored to normal"
                }
                notificationManager.createNotificationChannel(restoredChannel)
                Log.d(TAG, "Restored channel for: $appName")
            }
            
            // Ensure always allowed apps have high importance channels
            alwaysAllowedApps.forEach { packageName ->
                val channelId = "${CHANNEL_PREFIX}${packageName}"
                val appName = getAppName(packageName) ?: packageName
                
                val allowedChannel = NotificationChannel(
                    channelId,
                    "$appName (Always Allowed)",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Notifications from $appName - always allowed"
                }
                notificationManager.createNotificationChannel(allowedChannel)
                Log.d(TAG, "Ensured high importance channel for always allowed app: $appName")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error disabling silencing channels", e)
        }
    }
    
    /**
     * Clean up unused notification channels.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun cleanupUnusedChannels() {
        if (!areNotificationChannelsSupported()) return
        
        try {
            val configuredApps = schedulePreferences.silencedApps + schedulePreferences.alwaysAllowedApps
            val allChannels = notificationManager.notificationChannels
            
            allChannels.forEach { channel ->
                if (channel.id.startsWith(CHANNEL_PREFIX)) {
                    val packageName = channel.id.removePrefix(CHANNEL_PREFIX)
                    if (packageName !in configuredApps) {
                        notificationManager.deleteNotificationChannel(channel.id)
                        Log.d(TAG, "Cleaned up unused channel: ${channel.id}")
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up channels", e)
        }
    }
    
    /**
     * Get user-friendly app name for a package.
     */
    private fun getAppName(packageName: String): String? {
        return try {
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            null
        }
    }
    
    /**
     * Get status description for notification channels.
     */
    fun getChannelStatusDescription(): String {
        return when {
            !areNotificationChannelsSupported() -> "Notification channels not supported (API < 26)"
            else -> {
                val channelCount = notificationManager.notificationChannels.count { 
                    it.id.startsWith(CHANNEL_PREFIX) 
                }
                "Managing $channelCount notification channels"
            }
        }
    }
    
    /**
     * Check if notification channels are properly configured.
     */
    fun areChannelsProperlyConfigured(): Boolean {
        if (!areNotificationChannelsSupported()) return true // Not applicable
        
        return try {
            val configuredApps = schedulePreferences.silencedApps + schedulePreferences.alwaysAllowedApps
            val managedChannels = notificationManager.notificationChannels.count { 
                it.id.startsWith(CHANNEL_PREFIX) 
            }
            
            // Should have channels for all configured apps
            managedChannels >= configuredApps.size
        } catch (e: Exception) {
            Log.e(TAG, "Error checking channel configuration", e)
            false
        }
    }

    /**
     * Enhanced channel muting using BuzzKill technique
     * Temporarily mutes existing notification channels for maximum silencing effect
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun muteExistingChannelsTemporarily(packageName: String) {
        if (!areNotificationChannelsSupported()) return
        
        try {
            // Get all existing channels for this app
            // Note: NotificationChannel doesn't have packageName property
            // We'll create muted channels with the same IDs that apps typically use
            val commonChannelIds = listOf(
                "default",
                "general", 
                "notification",
                "notifications",
                "main",
                packageName,
                "${packageName}_default",
                "${packageName}_notifications",
                "miscellaneous"
            )
            
            val appName = getAppName(packageName) ?: packageName
            
            commonChannelIds.forEach { channelId ->
                try {
                    // Create a muted channel with this ID for the app
                    val mutedChannel = NotificationChannel(
                        channelId,
                        "$appName - $channelId (Silenced)",
                        NotificationManager.IMPORTANCE_MIN // Maximum silencing
                    ).apply {
                        description = "Silenced notifications for $appName - $channelId channel"
                        enableVibration(false) // Disable vibration
                        setSound(null, null)   // Disable sound
                        enableLights(false)    // Disable lights
                        setShowBadge(false)    // Disable badges
                        setBypassDnd(false)    // Ensure it doesn't bypass DND
                    }
                    
                    // Apply the muted channel
                    notificationManager.createNotificationChannel(mutedChannel)
                    
                    Log.d(TAG, "Temporarily muted channel: $channelId for package: $packageName")
                    
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to mute channel: $channelId for package: $packageName", e)
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error muting existing channels for $packageName", e)
        }
    }

    /**
     * Restore original channel settings after silencing period ends
     * Note: Since we're not storing original settings, this method now just logs the action
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun restoreOriginalChannels(packageName: String) {
        if (!areNotificationChannelsSupported()) return
        
        try {
            // Since we're not storing original channel settings, we can't restore them
            // The system will handle channel restoration automatically when our muted channels are deleted
            Log.d(TAG, "Channel restoration requested for package: $packageName (system will handle automatically)")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during channel restoration for $packageName", e)
        }
    }


}
