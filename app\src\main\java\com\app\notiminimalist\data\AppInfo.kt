package com.app.notiminimalist.data

import android.graphics.drawable.Drawable

/**
 * Represents the silencing behavior for an app
 */
enum class SilencingMode {
    SILENCED,       // App notifications are silenced during scheduled hours
    ALWAYS_ALLOWED  // App notifications always bypass silencing (always allowed - default state)
}

/**
 * Data class representing an installed app with its relevant information.
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val icon: Drawable?,
    val isSystemApp: Boolean = false,
    val isSelected: Boolean = false,
    val silencingMode: SilencingMode = SilencingMode.ALWAYS_ALLOWED
) {
    /**
     * Returns a display-friendly app name, limited to 30 characters.
     */
    fun getDisplayName(): String {
        return if (appName.length > 30) {
            "${appName.take(27)}..."
        } else {
            appName
        }
    }
    
    /**
     * Determines if this app should be shown in the selection list.
     * Shows only user-installed apps and essential system apps that users actually interact with.
     */
    fun shouldShow(): <PERSON><PERSON>an {
        // Always exclude the app itself
        if (packageName == "com.app.notiminimalist") return false

        // For user apps, always show them (these are apps users installed from Play Store, etc.)
        if (!isSystemApp) return true

        // For system apps, only show those that users typically interact with and can send notifications
        return isUserFacingSystemApp()
    }

    /**
     * Check if this system app is user-facing and should be shown.
     * Only shows system apps that users typically interact with and can send notifications.
     */
    private fun isUserFacingSystemApp(): Boolean {
        val packageLower = packageName.lowercase()
        val nameLower = appName.lowercase()

        // Essential communication apps (always show)
        if (packageName.startsWith("com.android.messaging") ||
            packageName.startsWith("com.android.dialer") ||
            packageName.startsWith("com.android.contacts") ||
            packageName.startsWith("com.android.phone") ||
            packageName.startsWith("com.android.mms") ||
            packageName.startsWith("com.android.email")) {
            return true
        }

        // Google apps that send notifications
        if (packageName.startsWith("com.google.android.apps") ||
            packageName.startsWith("com.google.android.gm") ||
            packageName.startsWith("com.google.android.talk") ||
            packageName.startsWith("com.google.android.youtube") ||
            packageName.startsWith("com.google.android.music") ||
            packageName.startsWith("com.google.android.calendar") ||
            packageName.startsWith("com.google.android.keep") ||
            packageName.startsWith("com.google.android.photos")) {
            return true
        }

        // Essential Android apps
        if (packageName.startsWith("com.android.chrome") ||
            packageName.startsWith("com.android.calendar") ||
            packageName.startsWith("com.android.camera") ||
            packageName.startsWith("com.android.gallery") ||
            packageName.startsWith("com.android.music") ||
            packageName.startsWith("com.android.calculator") ||
            packageName.startsWith("com.android.clock") ||
            packageName.startsWith("com.android.deskclock")) {
            return true
        }

        // Manufacturer apps that typically send notifications (Samsung, Xiaomi, etc.)
        if (packageName.startsWith("com.samsung.android.messaging") ||
            packageName.startsWith("com.samsung.android.email") ||
            packageName.startsWith("com.samsung.android.calendar") ||
            packageName.startsWith("com.samsung.android.app.notes") ||
            packageName.startsWith("com.sec.android.app.clockpackage") ||
            packageName.startsWith("com.miui.") ||
            packageName.startsWith("com.xiaomi.") ||
            packageName.startsWith("com.oneplus.") ||
            packageName.startsWith("com.huawei.") ||
            packageName.startsWith("com.oppo.") ||
            packageName.startsWith("com.vivo.") ||
            packageName.startsWith("com.realme.")) {

            // But exclude system/settings apps from manufacturers
            if (nameLower.contains("settings") ||
                nameLower.contains("launcher") ||
                nameLower.contains("system") ||
                nameLower.contains("setup") ||
                nameLower.contains("config") ||
                nameLower.contains("service") ||
                nameLower.contains("framework")) {
                return false
            }
            return true
        }

        // Check by app name for common notification-sending apps
        if (nameLower.contains("message") ||
            nameLower.contains("mail") ||
            nameLower.contains("calendar") ||
            nameLower.contains("clock") ||
            nameLower.contains("alarm") ||
            nameLower.contains("camera") ||
            nameLower.contains("gallery") ||
            nameLower.contains("music") ||
            nameLower.contains("video") ||
            nameLower.contains("note") ||
            nameLower.contains("weather") ||
            nameLower.contains("news") ||
            nameLower.contains("browser") ||
            nameLower.contains("chrome")) {

            // Exclude if it's clearly a system component
            if (nameLower.contains("service") ||
                nameLower.contains("provider") ||
                nameLower.contains("framework") ||
                nameLower.contains("system ui") ||
                nameLower.contains("settings")) {
                return false
            }
            return true
        }

        // Hide all other system apps
        return false
    }
    
    /**
     * Get user-friendly description of silencing mode
     */
    fun getSilencingModeDescription(): String {
        return when (silencingMode) {
            SilencingMode.SILENCED -> "Will be silenced"
            SilencingMode.ALWAYS_ALLOWED -> "Always allowed"
        }
    }
}
