package com.app.notiminimalist.data

import android.graphics.drawable.Drawable

/**
 * Represents the silencing behavior for an app
 */
enum class SilencingMode {
    SILENCED,       // App notifications are silenced during scheduled hours
    ALWAYS_ALLOWED  // App notifications always bypass silencing (always allowed - default state)
}

/**
 * Data class representing an installed app with its relevant information.
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val icon: Drawable?,
    val isSystemApp: Boolean = false,
    val isSelected: Boolean = false,
    val silencingMode: SilencingMode = SilencingMode.ALWAYS_ALLOWED
) {
    /**
     * Returns a display-friendly app name, limited to 30 characters.
     */
    fun getDisplayName(): String {
        return if (appName.length > 30) {
            "${appName.take(27)}..."
        } else {
            appName
        }
    }
    
    /**
     * Determines if this app should be shown in the selection list.
     * Uses a more inclusive approach to ensure apps are visible across all devices.
     */
    fun shouldShow(): Boolean {
        // Always exclude the app itself
        if (packageName == "com.app.notiminimalist") return false

        // Exclude only the most critical system packages that should never be silenced
        if (isSystemCriticalPackage()) return false

        // For user apps, always show them
        if (!isSystemApp) return true

        // For system apps, use inclusive filtering - show unless explicitly excluded
        return !isSystemAppToHide()
    }

    /**
     * Check if this is a critical system package that should never be shown.
     * These are core Android components that users should not interact with.
     */
    private fun isSystemCriticalPackage(): Boolean {
        return packageName == "android" ||
               packageName.startsWith("com.android.providers.") ||
               packageName.startsWith("com.android.systemui") ||
               packageName.startsWith("com.android.keychain") ||
               packageName.startsWith("com.android.inputmethod") ||
               packageName.startsWith("com.android.server") ||
               packageName == "com.android.shell" ||
               packageName == "com.android.sharedstoragebackup" ||
               packageName == "com.android.backupconfirm" ||
               packageName == "com.android.proxyhandler" ||
               packageName == "com.android.fallback" ||
               packageName.startsWith("com.android.internal") ||
               packageName.startsWith("com.qualcomm.") ||
               packageName.startsWith("com.qti.") ||
               packageName.startsWith("vendor.") ||
               packageName.startsWith("android.auto_generated_rro")
    }

    /**
     * Check if this system app should be hidden from the user.
     * This is a more conservative list - only hide apps that definitely don't send user notifications.
     */
    private fun isSystemAppToHide(): Boolean {
        val packageLower = packageName.lowercase()
        val nameLower = appName.lowercase()

        // Hide system settings and configuration apps
        if (packageName.startsWith("com.android.settings") ||
            packageName.startsWith("com.android.launcher") ||
            packageName.startsWith("com.android.wallpaper") ||
            packageName.startsWith("com.android.theme") ||
            packageName.startsWith("com.android.printspooler") ||
            packageName.startsWith("com.android.externalstorage") ||
            packageName.startsWith("com.android.documentsui") ||
            packageName.startsWith("com.android.packageinstaller") ||
            packageName.startsWith("com.android.permissioncontroller") ||
            packageName.startsWith("com.android.storagemanager") ||
            packageName.startsWith("com.android.bips") ||
            packageName.startsWith("com.android.captiveportallogin") ||
            packageName.startsWith("com.android.carrierconfig") ||
            packageName.startsWith("com.android.cts") ||
            packageName.startsWith("com.android.development") ||
            packageName.startsWith("com.android.htmlviewer") ||
            packageName.startsWith("com.android.managedprovisioning") ||
            packageName.startsWith("com.android.onetimeinitializer") ||
            packageName.startsWith("com.android.provision") ||
            packageName.startsWith("com.android.statementservice") ||
            packageName.startsWith("com.android.stk") ||
            packageName.startsWith("com.android.traceur") ||
            packageName.startsWith("com.android.vpndialogs") ||
            packageName.startsWith("com.android.wallpaperbackup") ||
            packageName.startsWith("com.android.wallpapercropper")) {
            return true
        }

        // Hide by app name patterns (more reliable across manufacturers)
        if (nameLower.contains("settings") ||
            nameLower.contains("launcher") ||
            nameLower.contains("wallpaper") ||
            nameLower.contains("theme") ||
            nameLower.contains("keyboard") ||
            nameLower.contains("input method") ||
            nameLower.contains("system ui") ||
            nameLower.contains("package installer") ||
            nameLower.contains("permission") ||
            nameLower.contains("storage") ||
            nameLower.contains("backup") ||
            nameLower.contains("setup") ||
            nameLower.contains("provision")) {
            return true
        }

        // Don't hide - let the user decide for all other system apps
        return false
    }
    
    /**
     * Get user-friendly description of silencing mode
     */
    fun getSilencingModeDescription(): String {
        return when (silencingMode) {
            SilencingMode.SILENCED -> "Will be silenced"
            SilencingMode.ALWAYS_ALLOWED -> "Always allowed"
        }
    }
}
