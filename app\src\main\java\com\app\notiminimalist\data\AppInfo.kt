package com.app.notiminimalist.data

import android.graphics.drawable.Drawable

/**
 * Represents the silencing behavior for an app
 */
enum class SilencingMode {
    SILENCED,       // App notifications are silenced during scheduled hours
    ALWAYS_ALLOWED  // App notifications always bypass silencing (always allowed - default state)
}

/**
 * Data class representing an installed app with its relevant information.
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val icon: Drawable?,
    val isSystemApp: Boolean = false,
    val isSelected: Boolean = false,
    val silencingMode: SilencingMode = SilencingMode.ALWAYS_ALLOWED
) {
    /**
     * Returns a display-friendly app name, limited to 30 characters.
     */
    fun getDisplayName(): String {
        return if (appName.length > 30) {
            "${appName.take(27)}..."
        } else {
            appName
        }
    }
    
    /**
     * Determines if this app should be shown in the selection list.
     * Filters out core system apps and the Notiminimalist app itself.
     */
    fun shouldShow(): <PERSON><PERSON><PERSON> {
        // Always exclude the app itself
        if (packageName == "com.app.notiminimalist") return false
        
        // Always exclude core Android system packages
        if (packageName == "android") return false
        if (packageName.startsWith("com.android.providers")) return false
        if (packageName.startsWith("com.android.systemui")) return false
        if (packageName.startsWith("com.android.keychain")) return false
        if (packageName.startsWith("com.android.settings")) return false
        
        // Show user apps and some useful system apps that might send notifications
        return !isSystemApp || 
               packageName.startsWith("com.google.android.apps") ||
               packageName.startsWith("com.android.chrome") ||
               packageName.startsWith("com.google.android.gm") ||
               packageName.startsWith("com.android.messaging") ||
               packageName.startsWith("com.android.dialer") ||
               packageName.startsWith("com.android.calendar") ||
               packageName.contains("messenger") ||
               packageName.contains("whatsapp") ||
               packageName.contains("telegram") ||
               packageName.contains("mail") ||
               packageName.contains("gmail") ||
               appName.lowercase().let { name ->
                   name.contains("notification") || 
                   name.contains("message") || 
                   name.contains("mail") ||
                   name.contains("phone") ||
                   name.contains("calendar")
               }
    }
    
    /**
     * Get user-friendly description of silencing mode
     */
    fun getSilencingModeDescription(): String {
        return when (silencingMode) {
            SilencingMode.SILENCED -> "Will be silenced"
            SilencingMode.ALWAYS_ALLOWED -> "Always allowed"
        }
    }
}
