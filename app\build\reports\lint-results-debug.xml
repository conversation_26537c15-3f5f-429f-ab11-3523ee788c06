<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.12.2">

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        val minute = String.format(&quot;%02d&quot;, time.minute)"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="218"
            column="22"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="            val policyCategories = NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="173"
            column="36"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                                 NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="174"
            column="34"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="187"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="188"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="225"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="226"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="232"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="233"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="238"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 23): `android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="239"
            column="21"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="256"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="257"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_FULL_SCREEN_INTENT`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="258"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_LIGHTS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="259"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_NOTIFICATION_LIST`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="260"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_PEEK`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="261"
            column="17"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 26): `android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_STATUS_BAR`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="264"
            column="17"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="40"
            column="29"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        severity="Warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details"
        category="Correctness"
        priority="5"
        summary="Using APIs affected by query permissions"
        explanation="Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a `&lt;queries>` declaration in your manifest.&#xA;&#xA;As a corollary, the methods `PackageManager#getInstalledPackages` and `PackageManager#getInstalledApplications` will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like `PackageManager#getPackageInfo` or `PackageManager#queryIntentActivities`."
        url="https://g.co/dev/packagevisibility"
        urls="https://g.co/dev/packagevisibility"
        errorLine1="            val installedPackages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\AppManager.kt"
            line="39"
            column="52"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        severity="Warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details"
        category="Correctness"
        priority="5"
        summary="Using APIs affected by query permissions"
        explanation="Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a `&lt;queries>` declaration in your manifest.&#xA;&#xA;As a corollary, the methods `PackageManager#getInstalledPackages` and `PackageManager#getInstalledApplications` will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like `PackageManager#getPackageInfo` or `PackageManager#queryIntentActivities`."
        url="https://g.co/dev/packagevisibility"
        urls="https://g.co/dev/packagevisibility"
        errorLine1="            val allInstalledApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\AppManager.kt"
            line="264"
            column="51"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        severity="Warning"
        message="As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details"
        category="Correctness"
        priority="5"
        summary="Using APIs affected by query permissions"
        explanation="Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a `&lt;queries>` declaration in your manifest.&#xA;&#xA;As a corollary, the methods `PackageManager#getInstalledPackages` and `PackageManager#getInstalledApplications` will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like `PackageManager#getPackageInfo` or `PackageManager#queryIntentActivities`."
        url="https://g.co/dev/packagevisibility"
        urls="https://g.co/dev/packagevisibility"
        errorLine1="            val allInstalledApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\AppManager.kt"
            line="297"
            column="51"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.12.2 is available: 8.13.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.12.2&quot;"
        errorLine2="      ~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose.material:material-icons-extended than 1.5.4 is available: 1.7.8"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation(&quot;androidx.compose.material:material-icons-extended:1.5.4&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts"
            line="56"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    coreLibraryDesugaring(&quot;com.android.tools:desugar_jdk_libs:2.0.4&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts"
            line="59"
            column="27"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.3"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.6.1&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="activityCompose = &quot;1.8.0&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="9"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.08.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="10"
            column="14"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="kotlin = &quot;2.0.21&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="3"
            column="10"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="kotlin = &quot;2.0.21&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml"
            line="3"
            column="10"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="96"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="139"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="158"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="186"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT` is never true here"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="194"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt"
            line="243"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="28"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="39"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &amp;&amp; !isIgnoringBatteryOptimizations()) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="53"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="75"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="139"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="74"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="167"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="217"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> (silencedApps.isNotEmpty() || alwaysAllowedApps.isNotEmpty())"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="371"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `Build.VERSION.SDK_INT >= Build.VERSION_CODES.M` is always true here (`SDK_INT` ≥ 23 and &lt; 26)"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="396"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="176"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\NotificationListenerService.kt"
            line="136"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\NotificationListenerService.kt"
            line="162"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt"
            line="63"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_500` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.white` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\drawable-v24\ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive icon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive roundIcon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/app_logo.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\drawable\app_logo.png"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toUri` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                data = Uri.parse(&quot;package:${context.packageName}&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="41"
            column="24"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toUri` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                data = Uri.parse(&quot;package:${context.packageName}&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt"
            line="41"
            column="24"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="419"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="437"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="482"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                preferences.edit().remove(PREFS_ORIGINAL_NOTIFICATION_VOLUME).apply()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt"
            line="504"
            column="17"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        themePrefs.edit().putBoolean(&quot;dark_theme&quot;, isDarkTheme).apply()"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="166"
            column="9"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    android.graphics.Color.parseColor(&quot;#1C1C1E&quot;) // Dark surface color"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="178"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    android.graphics.Color.parseColor(&quot;#1C1C1E&quot;) // Dark surface color"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="178"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    android.graphics.Color.parseColor(&quot;#2B9EDA&quot;) // Primary blue"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="180"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    android.graphics.Color.parseColor(&quot;#2B9EDA&quot;) // Primary blue"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt"
            line="180"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toUri` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                data = Uri.parse(&quot;package:${activity.packageName}&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt"
            line="121"
            column="24"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toUri` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                data = Uri.parse(&quot;package:${activity.packageName}&quot;)"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt"
            line="121"
            column="24"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putBoolean(KEY_SILENCING_ENABLED, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="44"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="54"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="67"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putStringSet(KEY_SELECTED_APPS, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="78"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putStringSet(KEY_SILENCED_APPS, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="85"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putStringSet(KEY_ALWAYS_ALLOWED_APPS, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="92"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putBoolean(KEY_SELECT_ALL_APPS, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="99"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        set(value) = preferences.edit().putBoolean(KEY_FIRST_RUN, value).apply()"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="106"
            column="22"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        preferences.edit().clear().apply()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt"
            line="234"
            column="9"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.compose.material:material-icons-extended:1.5.4&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts"
            line="56"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    coreLibraryDesugaring(&quot;com.android.tools:desugar_jdk_libs:2.0.4&quot;)"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts"
            line="59"
            column="27"/>
    </issue>

</issues>
