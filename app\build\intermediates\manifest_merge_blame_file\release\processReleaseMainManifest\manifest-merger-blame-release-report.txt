1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.notiminimalist"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:5-85
11-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:5:22-82
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:8:22-65
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.app.notiminimalist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:10:5-52:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38ea0471cd817173b4203329613a319d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:12:9-65
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:13:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:14:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:15:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:16:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:17:9-35
32        android:theme="@style/Theme.Notiminimalist" >
32-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:18:9-52
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:19:9-29:20
34            android:name="com.app.notiminimalist.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:20:13-41
35            android:exported="true"
35-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:21:13-36
36            android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:22:13-45
37            android:theme="@style/Theme.Notiminimalist" >
37-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:23:13-56
38            <intent-filter>
38-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:24:13-28:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:25:17-69
39-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:25:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:27:17-77
41-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:27:27-74
42            </intent-filter>
43        </activity>
44
45        <receiver
45-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:31:9-41:20
46            android:name="com.app.notiminimalist.receiver.SilencingReceiver"
46-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:32:13-55
47            android:enabled="true"
47-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:33:13-35
48            android:exported="false" >
48-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:34:13-37
49            <intent-filter android:priority="1000" >
49-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:35:13-40:29
49-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:35:28-51
50                <action android:name="android.intent.action.BOOT_COMPLETED" />
50-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:17-79
50-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:36:25-76
51                <action android:name="android.intent.action.TIME_SET" />
51-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:17-73
51-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:37:25-70
52                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:17-81
52-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:38:25-78
53
54                <category android:name="android.intent.category.DEFAULT" />
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:17-76
54-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:39:27-73
55            </intent-filter>
56        </receiver>
57
58        <service
58-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:43:9-51:19
59            android:name="com.app.notiminimalist.service.NotiminimalistNotificationListener"
59-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:44:13-71
60            android:enabled="true"
60-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:45:13-35
61            android:exported="true"
61-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:46:13-36
62            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
62-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:47:13-87
63            <intent-filter>
63-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:48:13-50:29
64                <action android:name="android.service.notification.NotificationListenerService" />
64-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:49:17-99
64-->C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:49:25-96
65            </intent-filter>
66        </service>
67
68        <provider
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
69            android:name="androidx.startup.InitializationProvider"
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
70            android:authorities="com.app.notiminimalist.androidx-startup"
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
71            android:exported="false" >
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
72            <meta-data
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.emoji2.text.EmojiCompatInitializer"
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
74                android:value="androidx.startup" />
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb7f68226892d000fb4b5c5902552f5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
75            <meta-data
75-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
76                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
76-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
77                android:value="androidx.startup" />
77-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8be577bffe5c1ff6215a870b38912ab4\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
78            <meta-data
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
79                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
80                android:value="androidx.startup" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
81        </provider>
82
83        <receiver
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
84            android:name="androidx.profileinstaller.ProfileInstallReceiver"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
85            android:directBootAware="false"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
86            android:enabled="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
87            android:exported="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
88            android:permission="android.permission.DUMP" >
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
90                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
93                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
96                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
99                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee0522b148fa5de8eb97850ab3faf94e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
100            </intent-filter>
101        </receiver>
102    </application>
103
104</manifest>
