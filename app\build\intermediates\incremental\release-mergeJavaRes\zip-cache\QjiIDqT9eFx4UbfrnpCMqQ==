[{"key": "androidx/lifecycle/LiveDataKt$observe$wrappedObserver$1.class", "name": "androidx/lifecycle/LiveDataKt$observe$wrappedObserver$1.class", "size": 1809, "crc": -147728495}, {"key": "androidx/lifecycle/LiveDataKt.class", "name": "androidx/lifecycle/LiveDataKt.class", "size": 2019, "crc": -50092624}, {"key": "androidx/lifecycle/Observer.class", "name": "androidx/lifecycle/Observer.class", "size": 581, "crc": -1645925231}, {"key": "androidx/lifecycle/LiveData$1.class", "name": "androidx/lifecycle/LiveData$1.class", "size": 997, "crc": 166332861}, {"key": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "name": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "size": 1058, "crc": 1943445660}, {"key": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "name": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "size": 2920, "crc": 367756699}, {"key": "androidx/lifecycle/LiveData$ObserverWrapper.class", "name": "androidx/lifecycle/LiveData$ObserverWrapper.class", "size": 1622, "crc": 1291138550}, {"key": "androidx/lifecycle/LiveData.class", "name": "androidx/lifecycle/LiveData.class", "size": 8921, "crc": 1853980223}, {"key": "androidx/lifecycle/MutableLiveData.class", "name": "androidx/lifecycle/MutableLiveData.class", "size": 946, "crc": -710747396}, {"key": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "name": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "size": 6, "crc": -1787584022}, {"key": "META-INF/lifecycle-livedata-core_release.kotlin_module", "name": "META-INF/lifecycle-livedata-core_release.kotlin_module", "size": 58, "crc": 642441246}]