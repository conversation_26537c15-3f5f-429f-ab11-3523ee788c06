  Activity android.app  AlarmManager android.app  DatePickerDialog android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  TimePickerDialog android.app  MainApp android.app.Activity  PermissionManager android.app.Activity  WindowCompat android.app.Activity  enableEdgeToEdge android.app.Activity  getSystemService android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  
RTC_WAKEUP android.app.AlarmManager  canScheduleExactAlarms android.app.AlarmManager  cancel android.app.AlarmManager  set android.app.AlarmManager  setExact android.app.AlarmManager  setExactAndAllowWhileIdle android.app.AlarmManager  OnDateSetListener android.app.DatePickerDialog  <SAM-CONSTRUCTOR> .android.app.DatePickerDialog.OnDateSetListener  CATEGORY_ALARM android.app.Notification  
CATEGORY_CALL android.app.Notification  CATEGORY_SYSTEM android.app.Notification  FLAG_ONGOING_EVENT android.app.Notification  
PRIORITY_HIGH android.app.Notification  category android.app.Notification  	channelId android.app.Notification  flags android.app.Notification  priority android.app.Notification  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  id android.app.NotificationChannel  
importance android.app.NotificationChannel  setBypassDnd android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_DEFAULT android.app.NotificationManager  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  IMPORTANCE_MIN android.app.NotificationManager  Policy android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  deleteNotificationChannel android.app.NotificationManager  getNotificationChannel android.app.NotificationManager  !isNotificationPolicyAccessGranted android.app.NotificationManager  notificationChannels android.app.NotificationManager  PRIORITY_CATEGORY_ALARMS &android.app.NotificationManager.Policy  PRIORITY_CATEGORY_CALLS &android.app.NotificationManager.Policy  PRIORITY_CATEGORY_EVENTS &android.app.NotificationManager.Policy  PRIORITY_CATEGORY_MESSAGES &android.app.NotificationManager.Policy  PRIORITY_CATEGORY_REMINDERS &android.app.NotificationManager.Policy  PRIORITY_CATEGORY_SYSTEM &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_AMBIENT &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_BADGE &android.app.NotificationManager.Policy  $SUPPRESSED_EFFECT_FULL_SCREEN_INTENT &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_LIGHTS &android.app.NotificationManager.Policy  #SUPPRESSED_EFFECT_NOTIFICATION_LIST &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_PEEK &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_SCREEN_OFF &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_SCREEN_ON &android.app.NotificationManager.Policy  SUPPRESSED_EFFECT_STATUS_BAR &android.app.NotificationManager.Policy  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getBroadcast android.app.PendingIntent  AudioManager android.app.Service  Build android.app.Service  
ComponentName android.app.Service  Context android.app.Service  
DndManager android.app.Service  	Exception android.app.Service  Handler android.app.Service  IllegalArgumentException android.app.Service  Int android.app.Service  	LocalTime android.app.Service  Log android.app.Service  Looper android.app.Service  Notification android.app.Service  NotificationManager android.app.Service  "NotiminimalistNotificationListener android.app.Service  SchedulePreferences android.app.Service  SecurityException android.app.Service  
SilencingMode android.app.Service  String android.app.Service  Suppress android.app.Service  TAG android.app.Service  android android.app.Service  
component1 android.app.Service  
component2 android.app.Service  emptyMap android.app.Service  forEach android.app.Service  groupBy android.app.Service  java android.app.Service  mutableMapOf android.app.Service  onCreate android.app.Service  
requestRebind android.app.Service  set android.app.Service  
startsWith android.app.Service  OnTimeSetListener android.app.TimePickerDialog  show android.app.TimePickerDialog  <SAM-CONSTRUCTOR> .android.app.TimePickerDialog.OnTimeSetListener  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  SharedPreferences android.content  AlarmScheduler !android.content.BroadcastReceiver  CoroutineScope !android.content.BroadcastReceiver  Dispatchers !android.content.BroadcastReceiver  
DndManager !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  SchedulePreferences !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  launch !android.content.BroadcastReceiver  flattenToString android.content.ComponentName  ACCESSIBILITY_SERVICE android.content.Context  
ALARM_SERVICE android.content.Context  
AUDIO_SERVICE android.content.Context  AudioManager android.content.Context  Build android.content.Context  
ComponentName android.content.Context  Context android.content.Context  
DndManager android.content.Context  	Exception android.content.Context  Handler android.content.Context  IllegalArgumentException android.content.Context  Int android.content.Context  	LocalTime android.content.Context  Log android.content.Context  Looper android.content.Context  MODE_PRIVATE android.content.Context  MainApp android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationManager android.content.Context  "NotiminimalistNotificationListener android.content.Context  
POWER_SERVICE android.content.Context  PermissionManager android.content.Context  SchedulePreferences android.content.Context  SecurityException android.content.Context  
SilencingMode android.content.Context  String android.content.Context  Suppress android.content.Context  TAG android.content.Context  WindowCompat android.content.Context  android android.content.Context  
component1 android.content.Context  
component2 android.content.Context  emptyMap android.content.Context  enableEdgeToEdge android.content.Context  forEach android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  groupBy android.content.Context  java android.content.Context  mutableMapOf android.content.Context  packageManager android.content.Context  packageName android.content.Context  
requestRebind android.content.Context  	resources android.content.Context  set android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  
startsWith android.content.Context  AudioManager android.content.ContextWrapper  Build android.content.ContextWrapper  
ComponentName android.content.ContextWrapper  Context android.content.ContextWrapper  
DndManager android.content.ContextWrapper  	Exception android.content.ContextWrapper  Handler android.content.ContextWrapper  IllegalArgumentException android.content.ContextWrapper  Int android.content.ContextWrapper  	LocalTime android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  MainApp android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  "NotiminimalistNotificationListener android.content.ContextWrapper  PermissionManager android.content.ContextWrapper  SchedulePreferences android.content.ContextWrapper  SecurityException android.content.ContextWrapper  
SilencingMode android.content.ContextWrapper  String android.content.ContextWrapper  Suppress android.content.ContextWrapper  TAG android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  android android.content.ContextWrapper  
component1 android.content.ContextWrapper  
component2 android.content.ContextWrapper  contentResolver android.content.ContextWrapper  emptyMap android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  forEach android.content.ContextWrapper  getSystemService android.content.ContextWrapper  groupBy android.content.ContextWrapper  java android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  packageName android.content.ContextWrapper  
requestRebind android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  
startsWith android.content.ContextWrapper  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_END_SILENCING android.content.Intent  ACTION_START_SILENCING android.content.Intent  ACTION_TIMEZONE_CHANGED android.content.Intent  ACTION_TIME_CHANGED android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Uri android.content.Intent  action android.content.Intent  activity android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  context android.content.Intent  data android.content.Intent  let android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  getStringSet !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putStringSet (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  ApplicationInfo android.content.pm  PackageManager android.content.pm  FLAG_SYSTEM "android.content.pm.ApplicationInfo  flags "android.content.pm.ApplicationInfo  packageName "android.content.pm.PackageItemInfo  
GET_META_DATA !android.content.pm.PackageManager  NameNotFoundException !android.content.pm.PackageManager  getApplicationIcon !android.content.pm.PackageManager  getApplicationInfo !android.content.pm.PackageManager  getApplicationLabel !android.content.pm.PackageManager  getInstalledApplications !android.content.pm.PackageManager  	fontScale !android.content.res.Configuration  
configuration android.content.res.Resources  
parseColor android.graphics.Color  BitmapDrawable android.graphics.drawable  Drawable android.graphics.drawable  bitmap (android.graphics.drawable.BitmapDrawable  let "android.graphics.drawable.Drawable  AudioManager 
android.media  STREAM_NOTIFICATION android.media.AudioManager  getStreamVolume android.media.AudioManager  setStreamVolume android.media.AudioManager  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  PowerManager 
android.os  MANUFACTURER android.os.Build  SDK_INT android.os.Build.VERSION  KITKAT android.os.Build.VERSION_CODES  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  postDelayed android.os.Handler  
getMainLooper android.os.Looper  isDeviceIdleMode android.os.PowerManager  isIgnoringBatteryOptimizations android.os.PowerManager  isPowerSaveMode android.os.PowerManager  Settings android.provider  %ACTION_NOTIFICATION_LISTENER_SETTINGS android.provider.Settings  *ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS android.provider.Settings  +ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS android.provider.Settings  #ACTION_REQUEST_SCHEDULE_EXACT_ALARM android.provider.Settings  	getString  android.provider.Settings.Secure  NotificationListenerService android.service.notification  StatusBarNotification android.service.notification  AudioManager 8android.service.notification.NotificationListenerService  Build 8android.service.notification.NotificationListenerService  
ComponentName 8android.service.notification.NotificationListenerService  Context 8android.service.notification.NotificationListenerService  
DndManager 8android.service.notification.NotificationListenerService  	Exception 8android.service.notification.NotificationListenerService  Handler 8android.service.notification.NotificationListenerService  IllegalArgumentException 8android.service.notification.NotificationListenerService  Int 8android.service.notification.NotificationListenerService  	LocalTime 8android.service.notification.NotificationListenerService  Log 8android.service.notification.NotificationListenerService  Looper 8android.service.notification.NotificationListenerService  Notification 8android.service.notification.NotificationListenerService  NotificationManager 8android.service.notification.NotificationListenerService  "NotiminimalistNotificationListener 8android.service.notification.NotificationListenerService  SchedulePreferences 8android.service.notification.NotificationListenerService  SecurityException 8android.service.notification.NotificationListenerService  
SilencingMode 8android.service.notification.NotificationListenerService  String 8android.service.notification.NotificationListenerService  Suppress 8android.service.notification.NotificationListenerService  TAG 8android.service.notification.NotificationListenerService  activeNotifications 8android.service.notification.NotificationListenerService  android 8android.service.notification.NotificationListenerService  cancelNotification 8android.service.notification.NotificationListenerService  
component1 8android.service.notification.NotificationListenerService  
component2 8android.service.notification.NotificationListenerService  emptyMap 8android.service.notification.NotificationListenerService  forEach 8android.service.notification.NotificationListenerService  groupBy 8android.service.notification.NotificationListenerService  java 8android.service.notification.NotificationListenerService  mutableMapOf 8android.service.notification.NotificationListenerService  onCreate 8android.service.notification.NotificationListenerService  onListenerConnected 8android.service.notification.NotificationListenerService  onListenerDisconnected 8android.service.notification.NotificationListenerService  onNotificationPosted 8android.service.notification.NotificationListenerService  onNotificationRemoved 8android.service.notification.NotificationListenerService  
requestRebind 8android.service.notification.NotificationListenerService  set 8android.service.notification.NotificationListenerService  
startsWith 8android.service.notification.NotificationListenerService  id 2android.service.notification.StatusBarNotification  key 2android.service.notification.StatusBarNotification  notification 2android.service.notification.StatusBarNotification  packageName 2android.service.notification.StatusBarNotification  tag 2android.service.notification.StatusBarNotification  format android.text.format.DateFormat  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  v android.util.Log  w android.util.Log  View android.view  MainApp  android.view.ContextThemeWrapper  PermissionManager  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  	decorView android.view.Window  statusBarColor android.view.Window  AccessibilityManager android.view.accessibility  	isEnabled /android.view.accessibility.AccessibilityManager  isTouchExplorationEnabled /android.view.accessibility.AccessibilityManager  	ImageView android.widget  	ScaleType android.widget.ImageView  android android.widget.ImageView  apply android.widget.ImageView  	scaleType android.widget.ImageView  setImageDrawable android.widget.ImageView  CENTER_CROP "android.widget.ImageView.ScaleType  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  MainApp #androidx.activity.ComponentActivity  PermissionManager #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  contentResolver #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  getSystemService #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  packageName #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  RequiresApi androidx.annotation  EaseInOutCubic androidx.compose.animation.core  Easing androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  animateDpAsState androidx.compose.animation.core  tween androidx.compose.animation.core  BorderStroke androidx.compose.foundation  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  AccessibilityHelper "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  ArrowForward "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CheckCircle "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	DateRange "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  LiveRegionMode "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  NotiBlue "androidx.compose.foundation.layout  NotiGray "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PermissionManager "androidx.compose.foundation.layout  PermissionStates "androidx.compose.foundation.layout  PermissionStatusIndicator "androidx.compose.foundation.layout  PermissionStep "androidx.compose.foundation.layout  Role "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Warning "androidx.compose.foundation.layout  
WizardContent "androidx.compose.foundation.layout  WizardHeader "androidx.compose.foundation.layout  WizardNavigation "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  canProceedToNext "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  contentDescription "androidx.compose.foundation.layout  createProgressSemantics "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  heading "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  
liveRegion "androidx.compose.foundation.layout  mutableIntStateOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  role "androidx.compose.foundation.layout  	semantics "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccessTime +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Apps +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CheckCircle +androidx.compose.foundation.layout.BoxScope  ChooseAppsScreen +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  DateTimeFormatter +androidx.compose.foundation.layout.BoxScope  EaseInOutCubic +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  
HomeScreen +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  
ModernAppIcon +androidx.compose.foundation.layout.BoxScope  ModernAppIconFallback +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  NotiBlue +androidx.compose.foundation.layout.BoxScope  NotiGray +androidx.compose.foundation.layout.BoxScope  
Notifications +androidx.compose.foundation.layout.BoxScope  NotificationsOff +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  Role +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  	SearchOff +androidx.compose.foundation.layout.BoxScope  SettingsScreen +androidx.compose.foundation.layout.BoxScope  
SilencingMode +androidx.compose.foundation.layout.BoxScope  Surface +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  animateDpAsState +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  colors +androidx.compose.foundation.layout.BoxScope  contentDescription +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getValue +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  isBlank +androidx.compose.foundation.layout.BoxScope  
isNotBlank +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  provideDelegate +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  role +androidx.compose.foundation.layout.BoxScope  run +androidx.compose.foundation.layout.BoxScope  	semantics +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  take +androidx.compose.foundation.layout.BoxScope  tween +androidx.compose.foundation.layout.BoxScope  	uppercase +androidx.compose.foundation.layout.BoxScope  
AccessTime .androidx.compose.foundation.layout.ColumnScope  AccessibilityHelper .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  	Analytics .androidx.compose.foundation.layout.ColumnScope  Apps .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Cancel .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CheckCircle .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CheckboxDefaults .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DateTimeFormatter .androidx.compose.foundation.layout.ColumnScope  DoNotDisturb .androidx.compose.foundation.layout.ColumnScope  EaseInOutCubic .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  LiveRegionMode .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  
ModernAppIcon .androidx.compose.foundation.layout.ColumnScope  ModernAppIconFallback .androidx.compose.foundation.layout.ColumnScope  ModernSettingsItem .androidx.compose.foundation.layout.ColumnScope  ModernSilencingToggle .androidx.compose.foundation.layout.ColumnScope  ModernToggleButton .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  NotiBlue .androidx.compose.foundation.layout.ColumnScope  NotiGray .androidx.compose.foundation.layout.ColumnScope  
Notifications .androidx.compose.foundation.layout.ColumnScope  NotificationsOff .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  PermissionStatusIndicator .androidx.compose.foundation.layout.ColumnScope  PhoneAndroid .androidx.compose.foundation.layout.ColumnScope  
QuickStatItem .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Schedule .androidx.compose.foundation.layout.ColumnScope  	SearchOff .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  Shield .androidx.compose.foundation.layout.ColumnScope  
SilencingMode .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Surface .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  TimeSelectionCard .androidx.compose.foundation.layout.ColumnScope  Timer .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  
WizardContent .androidx.compose.foundation.layout.ColumnScope  WizardHeader .androidx.compose.foundation.layout.ColumnScope  WizardNavigation .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  animateDpAsState .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  calculateDuration .androidx.compose.foundation.layout.ColumnScope  canProceedToNext .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  content .androidx.compose.foundation.layout.ColumnScope  contentDescription .androidx.compose.foundation.layout.ColumnScope  createProgressSemantics .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  heading .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  isBlank .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  
liveRegion .androidx.compose.foundation.layout.ColumnScope  offset .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  role .androidx.compose.foundation.layout.ColumnScope  run .androidx.compose.foundation.layout.ColumnScope  	semantics .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  tween .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  
AccessTime +androidx.compose.foundation.layout.RowScope  AccessibilityHelper +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  	Analytics +androidx.compose.foundation.layout.RowScope  Apps +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Cancel +androidx.compose.foundation.layout.RowScope  CheckCircle +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CheckboxDefaults +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  DarkMode +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  	LightMode +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  
ModernAppIcon +androidx.compose.foundation.layout.RowScope  ModernAppIconFallback +androidx.compose.foundation.layout.RowScope  ModernSilencingToggle +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  NavigationBarItemDefaults +androidx.compose.foundation.layout.RowScope  NotiBlue +androidx.compose.foundation.layout.RowScope  NotificationsOff +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  
QuickStatItem +androidx.compose.foundation.layout.RowScope  R +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Schedule +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  
SilencingMode +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Surface +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextOverflow +androidx.compose.foundation.layout.RowScope  TimeSelectionCard +androidx.compose.foundation.layout.RowScope  Timer +androidx.compose.foundation.layout.RowScope  Triple +androidx.compose.foundation.layout.RowScope  Warning +androidx.compose.foundation.layout.RowScope  align +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  calculateDuration +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  contentDescription +androidx.compose.foundation.layout.RowScope  createNavigationSemantics +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  heading +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  offset +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  painterResource +androidx.compose.foundation.layout.RowScope  run +androidx.compose.foundation.layout.RowScope  	semantics +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
AccessTime .androidx.compose.foundation.lazy.LazyItemScope  Apps .androidx.compose.foundation.lazy.LazyItemScope  CheckCircle .androidx.compose.foundation.lazy.LazyItemScope  DoNotDisturb .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Info .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  
ModernAppItem .androidx.compose.foundation.lazy.LazyItemScope  ModernAppsHeader .androidx.compose.foundation.lazy.LazyItemScope  ModernEmptyState .androidx.compose.foundation.lazy.LazyItemScope  ModernHeroSection .androidx.compose.foundation.lazy.LazyItemScope  ModernLoadingState .androidx.compose.foundation.lazy.LazyItemScope  ModernScheduleCard .androidx.compose.foundation.lazy.LazyItemScope  ModernSearchSection .androidx.compose.foundation.lazy.LazyItemScope  ModernSelectionControl .androidx.compose.foundation.lazy.LazyItemScope  ModernSettingsHeader .androidx.compose.foundation.lazy.LazyItemScope  ModernSettingsItem .androidx.compose.foundation.lazy.LazyItemScope  ModernSettingsSection .androidx.compose.foundation.lazy.LazyItemScope  
Notifications .androidx.compose.foundation.lazy.LazyItemScope  NotificationsOff .androidx.compose.foundation.lazy.LazyItemScope  PermissionWarningCard .androidx.compose.foundation.lazy.LazyItemScope  PhoneAndroid .androidx.compose.foundation.lazy.LazyItemScope  QuickStatsCard .androidx.compose.foundation.lazy.LazyItemScope  Schedule .androidx.compose.foundation.lazy.LazyItemScope  Security .androidx.compose.foundation.lazy.LazyItemScope  Shield .androidx.compose.foundation.lazy.LazyItemScope  
SilencingMode .androidx.compose.foundation.lazy.LazyItemScope  Warning .androidx.compose.foundation.lazy.LazyItemScope  contains .androidx.compose.foundation.lazy.LazyItemScope  count .androidx.compose.foundation.lazy.LazyItemScope  filter .androidx.compose.foundation.lazy.LazyItemScope  isBlank .androidx.compose.foundation.lazy.LazyItemScope  
isNotBlank .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  map .androidx.compose.foundation.lazy.LazyItemScope  
AccessTime .androidx.compose.foundation.lazy.LazyListScope  Apps .androidx.compose.foundation.lazy.LazyListScope  CheckCircle .androidx.compose.foundation.lazy.LazyListScope  DoNotDisturb .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Info .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  
ModernAppItem .androidx.compose.foundation.lazy.LazyListScope  ModernAppsHeader .androidx.compose.foundation.lazy.LazyListScope  ModernEmptyState .androidx.compose.foundation.lazy.LazyListScope  ModernHeroSection .androidx.compose.foundation.lazy.LazyListScope  ModernLoadingState .androidx.compose.foundation.lazy.LazyListScope  ModernScheduleCard .androidx.compose.foundation.lazy.LazyListScope  ModernSearchSection .androidx.compose.foundation.lazy.LazyListScope  ModernSelectionControl .androidx.compose.foundation.lazy.LazyListScope  ModernSettingsHeader .androidx.compose.foundation.lazy.LazyListScope  ModernSettingsItem .androidx.compose.foundation.lazy.LazyListScope  ModernSettingsSection .androidx.compose.foundation.lazy.LazyListScope  
Notifications .androidx.compose.foundation.lazy.LazyListScope  NotificationsOff .androidx.compose.foundation.lazy.LazyListScope  PermissionWarningCard .androidx.compose.foundation.lazy.LazyListScope  PhoneAndroid .androidx.compose.foundation.lazy.LazyListScope  QuickStatsCard .androidx.compose.foundation.lazy.LazyListScope  Schedule .androidx.compose.foundation.lazy.LazyListScope  Security .androidx.compose.foundation.lazy.LazyListScope  Shield .androidx.compose.foundation.lazy.LazyListScope  
SilencingMode .androidx.compose.foundation.lazy.LazyListScope  Warning .androidx.compose.foundation.lazy.LazyListScope  contains .androidx.compose.foundation.lazy.LazyListScope  count .androidx.compose.foundation.lazy.LazyListScope  filter .androidx.compose.foundation.lazy.LazyListScope  isBlank .androidx.compose.foundation.lazy.LazyListScope  
isNotBlank .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  map .androidx.compose.foundation.lazy.LazyListScope  
toggleable %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Outlined %androidx.compose.material.icons.Icons  
AccessTime ,androidx.compose.material.icons.Icons.Filled  	Analytics ,androidx.compose.material.icons.Icons.Filled  Apps ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  ArrowForward ,androidx.compose.material.icons.Icons.Filled  Cancel ,androidx.compose.material.icons.Icons.Filled  CheckCircle ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  DarkMode ,androidx.compose.material.icons.Icons.Filled  	DateRange ,androidx.compose.material.icons.Icons.Filled  DoNotDisturb ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  	LightMode ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  NotificationsOff ,androidx.compose.material.icons.Icons.Filled  PhoneAndroid ,androidx.compose.material.icons.Icons.Filled  Schedule ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  	SearchOff ,androidx.compose.material.icons.Icons.Filled  Security ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Shield ,androidx.compose.material.icons.Icons.Filled  Timer ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  Apps .androidx.compose.material.icons.Icons.Outlined  Home .androidx.compose.material.icons.Icons.Outlined  Settings .androidx.compose.material.icons.Icons.Outlined  
AccessTime &androidx.compose.material.icons.filled  AccessibilityHelper &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	Analytics &androidx.compose.material.icons.filled  AppInfo &androidx.compose.material.icons.filled  Apps &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  ArrowForward &androidx.compose.material.icons.filled  BitmapDrawable &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Build &androidx.compose.material.icons.filled  Bundle &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Cancel &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Checkbox &androidx.compose.material.icons.filled  CheckboxDefaults &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  ChooseAppsScreen &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  ColumnScope &androidx.compose.material.icons.filled  ComponentActivity &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Context &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  DateTimeFormatter &androidx.compose.material.icons.filled  DoNotDisturb &androidx.compose.material.icons.filled  EaseInOutCubic &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  
HomeScreen &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  	LightMode &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  LiveRegionMode &androidx.compose.material.icons.filled  	LocalDate &androidx.compose.material.icons.filled  	LocalTime &androidx.compose.material.icons.filled  MainActivity &androidx.compose.material.icons.filled  MainApp &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  
ModernAppIcon &androidx.compose.material.icons.filled  ModernAppIconFallback &androidx.compose.material.icons.filled  
ModernAppItem &androidx.compose.material.icons.filled  ModernAppsHeader &androidx.compose.material.icons.filled  ModernEmptyState &androidx.compose.material.icons.filled  ModernHeroSection &androidx.compose.material.icons.filled  ModernLoadingState &androidx.compose.material.icons.filled  ModernScheduleCard &androidx.compose.material.icons.filled  ModernSearchSection &androidx.compose.material.icons.filled  ModernSelectionControl &androidx.compose.material.icons.filled  ModernSettingsHeader &androidx.compose.material.icons.filled  ModernSettingsItem &androidx.compose.material.icons.filled  ModernSettingsSection &androidx.compose.material.icons.filled  ModernSilencingToggle &androidx.compose.material.icons.filled  ModernToggleButton &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  NavigationBarItemDefaults &androidx.compose.material.icons.filled  NotiBlue &androidx.compose.material.icons.filled  NotiGray &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsOff &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  OutlinedTextFieldDefaults &androidx.compose.material.icons.filled  PermissionManager &androidx.compose.material.icons.filled  PermissionStates &androidx.compose.material.icons.filled  PermissionStatusIndicator &androidx.compose.material.icons.filled  PermissionStep &androidx.compose.material.icons.filled  PermissionWarningCard &androidx.compose.material.icons.filled  PhoneAndroid &androidx.compose.material.icons.filled  Preview &androidx.compose.material.icons.filled  
QuickStatItem &androidx.compose.material.icons.filled  QuickStatsCard &androidx.compose.material.icons.filled  R &androidx.compose.material.icons.filled  Role &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Schedule &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  	SearchOff &androidx.compose.material.icons.filled  Security &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsScreen &androidx.compose.material.icons.filled  Shield &androidx.compose.material.icons.filled  
SilencingMode &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Surface &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TextOverflow &androidx.compose.material.icons.filled  TimeSelectionCard &androidx.compose.material.icons.filled  Timer &androidx.compose.material.icons.filled  TopAppBarDefaults &androidx.compose.material.icons.filled  Triple &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  WindowCompat &androidx.compose.material.icons.filled  
WizardContent &androidx.compose.material.icons.filled  WizardHeader &androidx.compose.material.icons.filled  WizardNavigation &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  animateDpAsState &androidx.compose.material.icons.filled  apply &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  calculateDuration &androidx.compose.material.icons.filled  canProceedToNext &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  
cardElevation &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  colors &androidx.compose.material.icons.filled  contains &androidx.compose.material.icons.filled  contentDescription &androidx.compose.material.icons.filled  count &androidx.compose.material.icons.filled  createNavigationSemantics &androidx.compose.material.icons.filled  createProgressSemantics &androidx.compose.material.icons.filled  createToggleSemantics &androidx.compose.material.icons.filled  	emptyList &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  filter &androidx.compose.material.icons.filled  forEachIndexed &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  heading &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  isBlank &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  
liveRegion &androidx.compose.material.icons.filled  map &androidx.compose.material.icons.filled  mutableIntStateOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  offset &androidx.compose.material.icons.filled  outlinedButtonColors &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  painterResource &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  radialGradient &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  role &androidx.compose.material.icons.filled  run &androidx.compose.material.icons.filled  	semantics &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  take &androidx.compose.material.icons.filled  topAppBarColors &androidx.compose.material.icons.filled  tween &androidx.compose.material.icons.filled  	uppercase &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  graphics .androidx.compose.material.icons.filled.android  drawable 7androidx.compose.material.icons.filled.android.graphics  Drawable @androidx.compose.material.icons.filled.android.graphics.drawable  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  
AccessTime (androidx.compose.material.icons.outlined  AccessibilityHelper (androidx.compose.material.icons.outlined  	Alignment (androidx.compose.material.icons.outlined  	Analytics (androidx.compose.material.icons.outlined  AppInfo (androidx.compose.material.icons.outlined  Apps (androidx.compose.material.icons.outlined  Arrangement (androidx.compose.material.icons.outlined  BitmapDrawable (androidx.compose.material.icons.outlined  Boolean (androidx.compose.material.icons.outlined  Box (androidx.compose.material.icons.outlined  Build (androidx.compose.material.icons.outlined  Bundle (androidx.compose.material.icons.outlined  Cancel (androidx.compose.material.icons.outlined  CardDefaults (androidx.compose.material.icons.outlined  CheckCircle (androidx.compose.material.icons.outlined  Checkbox (androidx.compose.material.icons.outlined  CheckboxDefaults (androidx.compose.material.icons.outlined  ChevronRight (androidx.compose.material.icons.outlined  ChooseAppsScreen (androidx.compose.material.icons.outlined  CircleShape (androidx.compose.material.icons.outlined  CircularProgressIndicator (androidx.compose.material.icons.outlined  Clear (androidx.compose.material.icons.outlined  Color (androidx.compose.material.icons.outlined  Column (androidx.compose.material.icons.outlined  ColumnScope (androidx.compose.material.icons.outlined  ComponentActivity (androidx.compose.material.icons.outlined  
Composable (androidx.compose.material.icons.outlined  Context (androidx.compose.material.icons.outlined  DarkMode (androidx.compose.material.icons.outlined  DateTimeFormatter (androidx.compose.material.icons.outlined  DoNotDisturb (androidx.compose.material.icons.outlined  EaseInOutCubic (androidx.compose.material.icons.outlined  ExperimentalMaterial3Api (androidx.compose.material.icons.outlined  
FontWeight (androidx.compose.material.icons.outlined  Home (androidx.compose.material.icons.outlined  
HomeScreen (androidx.compose.material.icons.outlined  Icon (androidx.compose.material.icons.outlined  
IconButton (androidx.compose.material.icons.outlined  Icons (androidx.compose.material.icons.outlined  Info (androidx.compose.material.icons.outlined  Int (androidx.compose.material.icons.outlined  	LightMode (androidx.compose.material.icons.outlined  List (androidx.compose.material.icons.outlined  	LocalDate (androidx.compose.material.icons.outlined  	LocalTime (androidx.compose.material.icons.outlined  MainActivity (androidx.compose.material.icons.outlined  MainApp (androidx.compose.material.icons.outlined  
MaterialTheme (androidx.compose.material.icons.outlined  
ModernAppIcon (androidx.compose.material.icons.outlined  ModernAppIconFallback (androidx.compose.material.icons.outlined  
ModernAppItem (androidx.compose.material.icons.outlined  ModernAppsHeader (androidx.compose.material.icons.outlined  ModernEmptyState (androidx.compose.material.icons.outlined  ModernHeroSection (androidx.compose.material.icons.outlined  ModernLoadingState (androidx.compose.material.icons.outlined  ModernScheduleCard (androidx.compose.material.icons.outlined  ModernSearchSection (androidx.compose.material.icons.outlined  ModernSelectionControl (androidx.compose.material.icons.outlined  ModernSettingsHeader (androidx.compose.material.icons.outlined  ModernSettingsItem (androidx.compose.material.icons.outlined  ModernSettingsSection (androidx.compose.material.icons.outlined  ModernSilencingToggle (androidx.compose.material.icons.outlined  ModernToggleButton (androidx.compose.material.icons.outlined  Modifier (androidx.compose.material.icons.outlined  NavigationBarItemDefaults (androidx.compose.material.icons.outlined  NotiBlue (androidx.compose.material.icons.outlined  NotiGray (androidx.compose.material.icons.outlined  
Notifications (androidx.compose.material.icons.outlined  NotificationsOff (androidx.compose.material.icons.outlined  OptIn (androidx.compose.material.icons.outlined  OutlinedButton (androidx.compose.material.icons.outlined  OutlinedTextField (androidx.compose.material.icons.outlined  OutlinedTextFieldDefaults (androidx.compose.material.icons.outlined  PermissionManager (androidx.compose.material.icons.outlined  PermissionStates (androidx.compose.material.icons.outlined  PermissionWarningCard (androidx.compose.material.icons.outlined  PhoneAndroid (androidx.compose.material.icons.outlined  Preview (androidx.compose.material.icons.outlined  
QuickStatItem (androidx.compose.material.icons.outlined  QuickStatsCard (androidx.compose.material.icons.outlined  R (androidx.compose.material.icons.outlined  Role (androidx.compose.material.icons.outlined  RoundedCornerShape (androidx.compose.material.icons.outlined  Row (androidx.compose.material.icons.outlined  Schedule (androidx.compose.material.icons.outlined  Search (androidx.compose.material.icons.outlined  	SearchOff (androidx.compose.material.icons.outlined  Security (androidx.compose.material.icons.outlined  Settings (androidx.compose.material.icons.outlined  SettingsScreen (androidx.compose.material.icons.outlined  Shield (androidx.compose.material.icons.outlined  
SilencingMode (androidx.compose.material.icons.outlined  Spacer (androidx.compose.material.icons.outlined  String (androidx.compose.material.icons.outlined  Surface (androidx.compose.material.icons.outlined  Text (androidx.compose.material.icons.outlined  	TextAlign (androidx.compose.material.icons.outlined  TextOverflow (androidx.compose.material.icons.outlined  TimeSelectionCard (androidx.compose.material.icons.outlined  Timer (androidx.compose.material.icons.outlined  TopAppBarDefaults (androidx.compose.material.icons.outlined  Triple (androidx.compose.material.icons.outlined  Unit (androidx.compose.material.icons.outlined  Warning (androidx.compose.material.icons.outlined  WindowCompat (androidx.compose.material.icons.outlined  align (androidx.compose.material.icons.outlined  android (androidx.compose.material.icons.outlined  androidx (androidx.compose.material.icons.outlined  animateDpAsState (androidx.compose.material.icons.outlined  apply (androidx.compose.material.icons.outlined  
background (androidx.compose.material.icons.outlined  calculateDuration (androidx.compose.material.icons.outlined  
cardColors (androidx.compose.material.icons.outlined  
cardElevation (androidx.compose.material.icons.outlined  	clickable (androidx.compose.material.icons.outlined  clip (androidx.compose.material.icons.outlined  colors (androidx.compose.material.icons.outlined  contains (androidx.compose.material.icons.outlined  count (androidx.compose.material.icons.outlined  createNavigationSemantics (androidx.compose.material.icons.outlined  createToggleSemantics (androidx.compose.material.icons.outlined  	emptyList (androidx.compose.material.icons.outlined  fillMaxSize (androidx.compose.material.icons.outlined  fillMaxWidth (androidx.compose.material.icons.outlined  filter (androidx.compose.material.icons.outlined  forEachIndexed (androidx.compose.material.icons.outlined  getValue (androidx.compose.material.icons.outlined  height (androidx.compose.material.icons.outlined  isBlank (androidx.compose.material.icons.outlined  
isNotBlank (androidx.compose.material.icons.outlined  
isNotEmpty (androidx.compose.material.icons.outlined  kotlinx (androidx.compose.material.icons.outlined  launch (androidx.compose.material.icons.outlined  let (androidx.compose.material.icons.outlined  listOf (androidx.compose.material.icons.outlined  map (androidx.compose.material.icons.outlined  offset (androidx.compose.material.icons.outlined  outlinedButtonColors (androidx.compose.material.icons.outlined  padding (androidx.compose.material.icons.outlined  painterResource (androidx.compose.material.icons.outlined  provideDelegate (androidx.compose.material.icons.outlined  radialGradient (androidx.compose.material.icons.outlined  run (androidx.compose.material.icons.outlined  	semantics (androidx.compose.material.icons.outlined  size (androidx.compose.material.icons.outlined  spacedBy (androidx.compose.material.icons.outlined  take (androidx.compose.material.icons.outlined  topAppBarColors (androidx.compose.material.icons.outlined  tween (androidx.compose.material.icons.outlined  	uppercase (androidx.compose.material.icons.outlined  weight (androidx.compose.material.icons.outlined  width (androidx.compose.material.icons.outlined  graphics 0androidx.compose.material.icons.outlined.android  drawable 9androidx.compose.material.icons.outlined.android.graphics  Drawable Bandroidx.compose.material.icons.outlined.android.graphics.drawable  compose 1androidx.compose.material.icons.outlined.androidx  ui 9androidx.compose.material.icons.outlined.androidx.compose  graphics <androidx.compose.material.icons.outlined.androidx.compose.ui  vector Eandroidx.compose.material.icons.outlined.androidx.compose.ui.graphics  ImageVector Landroidx.compose.material.icons.outlined.androidx.compose.ui.graphics.vector  AccessibilityHelper androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  ArrowForward androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CenterAlignedTopAppBar androidx.compose.material3  CheckCircle androidx.compose.material3  Checkbox androidx.compose.material3  CheckboxColors androidx.compose.material3  CheckboxDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Close androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  	DateRange androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FontWeight androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  Info androidx.compose.material3  Int androidx.compose.material3  LaunchedEffect androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  LiveRegionMode androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  NavigationBarItemColors androidx.compose.material3  NavigationBarItemDefaults androidx.compose.material3  NotiBlue androidx.compose.material3  NotiGray androidx.compose.material3  
Notifications androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  PermissionManager androidx.compose.material3  PermissionStates androidx.compose.material3  PermissionStatusIndicator androidx.compose.material3  PermissionStep androidx.compose.material3  Role androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Warning androidx.compose.material3  
WizardContent androidx.compose.material3  WizardHeader androidx.compose.material3  WizardNavigation androidx.compose.material3  buttonColors androidx.compose.material3  canProceedToNext androidx.compose.material3  
cardColors androidx.compose.material3  contentDescription androidx.compose.material3  createProgressSemantics androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  heading androidx.compose.material3  height androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  
liveRegion androidx.compose.material3  mutableIntStateOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  role androidx.compose.material3  	semantics androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  colors +androidx.compose.material3.CheckboxDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onBackground &androidx.compose.material3.ColorScheme  onError &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondary &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.NavigationBarItemDefaults  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelLarge %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  AccessibilityHelper androidx.compose.runtime  AccessibilityManager androidx.compose.runtime  AccessibleColorScheme androidx.compose.runtime  	Alignment androidx.compose.runtime  AnnotatedString androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  ArrowForward androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CheckCircle androidx.compose.runtime  Close androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  	DateRange androidx.compose.runtime  Dp androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Float androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  Info androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  LiveRegionMode androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  NotiBlue androidx.compose.runtime  NotiGray androidx.compose.runtime  
Notifications androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  PermissionManager androidx.compose.runtime  PermissionStates androidx.compose.runtime  PermissionStatusIndicator androidx.compose.runtime  PermissionStep androidx.compose.runtime  ProgressBarRangeInfo androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Role androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  SemanticsPropertyReceiver androidx.compose.runtime  Settings androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TextUnit androidx.compose.runtime  ToggleableState androidx.compose.runtime  Unit androidx.compose.runtime  Warning androidx.compose.runtime  
WizardContent androidx.compose.runtime  WizardHeader androidx.compose.runtime  WizardNavigation androidx.compose.runtime  buildString androidx.compose.runtime  buttonColors androidx.compose.runtime  canProceedToNext androidx.compose.runtime  
cardColors androidx.compose.runtime  collectAsState androidx.compose.runtime  contentDescription androidx.compose.runtime  createInteractiveSemantics androidx.compose.runtime  createProgressSemantics androidx.compose.runtime  createToggleSemantics androidx.compose.runtime  disabled androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getAccessibleSpacing androidx.compose.runtime  getValue androidx.compose.runtime  heading androidx.compose.runtime  height androidx.compose.runtime  isScreenReaderEnabled androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  
liveRegion androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  onClick androidx.compose.runtime  padding androidx.compose.runtime  progressBarRangeInfo androidx.compose.runtime  provideDelegate androidx.compose.runtime  rangeTo androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  role androidx.compose.runtime  selected androidx.compose.runtime  	semantics androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  text androidx.compose.runtime  toggleableState androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue (androidx.compose.runtime.MutableIntState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction1  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  	CenterEnd androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  	CenterEnd 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  AccessibilityHelper androidx.compose.ui.Modifier  	Companion androidx.compose.ui.Modifier  Role androidx.compose.ui.Modifier  accessibleToggle androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  createInteractiveSemantics androidx.compose.ui.Modifier  createToggleSemantics androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  	semantics androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  	semantics &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ImageBitmap androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  AccessibilityHelper androidx.compose.ui.semantics  AccessibilityManager androidx.compose.ui.semantics  AccessibleColorScheme androidx.compose.ui.semantics  	Alignment androidx.compose.ui.semantics  AnnotatedString androidx.compose.ui.semantics  Arrangement androidx.compose.ui.semantics  	ArrowBack androidx.compose.ui.semantics  ArrowForward androidx.compose.ui.semantics  Boolean androidx.compose.ui.semantics  Box androidx.compose.ui.semantics  Button androidx.compose.ui.semantics  ButtonDefaults androidx.compose.ui.semantics  Card androidx.compose.ui.semantics  CardDefaults androidx.compose.ui.semantics  CheckCircle androidx.compose.ui.semantics  Close androidx.compose.ui.semantics  Color androidx.compose.ui.semantics  Column androidx.compose.ui.semantics  
Composable androidx.compose.ui.semantics  Context androidx.compose.ui.semantics  	DateRange androidx.compose.ui.semantics  Dp androidx.compose.ui.semantics  ExperimentalMaterial3Api androidx.compose.ui.semantics  Float androidx.compose.ui.semantics  
FontWeight androidx.compose.ui.semantics  Icon androidx.compose.ui.semantics  
IconButton androidx.compose.ui.semantics  Icons androidx.compose.ui.semantics  ImageVector androidx.compose.ui.semantics  Info androidx.compose.ui.semantics  Int androidx.compose.ui.semantics  LaunchedEffect androidx.compose.ui.semantics  LinearProgressIndicator androidx.compose.ui.semantics  List androidx.compose.ui.semantics  LiveRegionMode androidx.compose.ui.semantics  
MaterialTheme androidx.compose.ui.semantics  Modifier androidx.compose.ui.semantics  NotiBlue androidx.compose.ui.semantics  NotiGray androidx.compose.ui.semantics  
Notifications androidx.compose.ui.semantics  OptIn androidx.compose.ui.semantics  OutlinedButton androidx.compose.ui.semantics  PermissionManager androidx.compose.ui.semantics  PermissionStates androidx.compose.ui.semantics  PermissionStatusIndicator androidx.compose.ui.semantics  PermissionStep androidx.compose.ui.semantics  ProgressBarRangeInfo androidx.compose.ui.semantics  Role androidx.compose.ui.semantics  RoundedCornerShape androidx.compose.ui.semantics  Row androidx.compose.ui.semantics  SemanticsPropertyReceiver androidx.compose.ui.semantics  Settings androidx.compose.ui.semantics  Spacer androidx.compose.ui.semantics  String androidx.compose.ui.semantics  Text androidx.compose.ui.semantics  	TextAlign androidx.compose.ui.semantics  
TextButton androidx.compose.ui.semantics  TextUnit androidx.compose.ui.semantics  ToggleableState androidx.compose.ui.semantics  Unit androidx.compose.ui.semantics  Warning androidx.compose.ui.semantics  
WizardContent androidx.compose.ui.semantics  WizardHeader androidx.compose.ui.semantics  WizardNavigation androidx.compose.ui.semantics  buildString androidx.compose.ui.semantics  buttonColors androidx.compose.ui.semantics  canProceedToNext androidx.compose.ui.semantics  
cardColors androidx.compose.ui.semantics  contentDescription androidx.compose.ui.semantics  createInteractiveSemantics androidx.compose.ui.semantics  createProgressSemantics androidx.compose.ui.semantics  createToggleSemantics androidx.compose.ui.semantics  disabled androidx.compose.ui.semantics  
fillMaxHeight androidx.compose.ui.semantics  fillMaxSize androidx.compose.ui.semantics  fillMaxWidth androidx.compose.ui.semantics  getAccessibleSpacing androidx.compose.ui.semantics  getValue androidx.compose.ui.semantics  heading androidx.compose.ui.semantics  height androidx.compose.ui.semantics  isScreenReaderEnabled androidx.compose.ui.semantics  kotlinx androidx.compose.ui.semantics  let androidx.compose.ui.semantics  listOf androidx.compose.ui.semantics  
liveRegion androidx.compose.ui.semantics  mutableIntStateOf androidx.compose.ui.semantics  mutableStateOf androidx.compose.ui.semantics  onClick androidx.compose.ui.semantics  padding androidx.compose.ui.semantics  progressBarRangeInfo androidx.compose.ui.semantics  provideDelegate androidx.compose.ui.semantics  rangeTo androidx.compose.ui.semantics  remember androidx.compose.ui.semantics  role androidx.compose.ui.semantics  selected androidx.compose.ui.semantics  	semantics androidx.compose.ui.semantics  setValue androidx.compose.ui.semantics  size androidx.compose.ui.semantics  text androidx.compose.ui.semantics  toggleableState androidx.compose.ui.semantics  weight androidx.compose.ui.semantics  width androidx.compose.ui.semantics  	Companion ,androidx.compose.ui.semantics.LiveRegionMode  Polite ,androidx.compose.ui.semantics.LiveRegionMode  Polite 6androidx.compose.ui.semantics.LiveRegionMode.Companion  Button "androidx.compose.ui.semantics.Role  	Companion "androidx.compose.ui.semantics.Role  Switch "androidx.compose.ui.semantics.Role  Tab "androidx.compose.ui.semantics.Role  Button ,androidx.compose.ui.semantics.Role.Companion  Switch ,androidx.compose.ui.semantics.Role.Companion  Tab ,androidx.compose.ui.semantics.Role.Companion  AccessibilityHelper 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  AnnotatedString 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  DateTimeFormatter 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  LiveRegionMode 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  ProgressBarRangeInfo 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  Role 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  
SilencingMode 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  ToggleableState 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  buildString 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  contentDescription 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  createInteractiveSemantics 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  createNavigationSemantics 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  createProgressSemantics 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  createToggleSemantics 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  disabled 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  heading 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  invoke 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  let 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  
liveRegion 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  onClick 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  progressBarRangeInfo 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  rangeTo 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  role 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  selected 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  text 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  toggleableState 7androidx.compose.ui.semantics.SemanticsPropertyReceiver  ToggleableState androidx.compose.ui.state  AnnotatedString androidx.compose.ui.text  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  	ExtraBold (androidx.compose.ui.text.font.FontWeight  Light (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	ExtraBold 2androidx.compose.ui.text.font.FontWeight.Companion  Light 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  times androidx.compose.ui.unit.Dp  times !androidx.compose.ui.unit.TextUnit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  MainApp #androidx.core.app.ComponentActivity  PermissionManager #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  
AccessTime com.app.notiminimalist  AccessibilityHelper com.app.notiminimalist  	Alignment com.app.notiminimalist  	Analytics com.app.notiminimalist  AppInfo com.app.notiminimalist  Apps com.app.notiminimalist  Arrangement com.app.notiminimalist  BitmapDrawable com.app.notiminimalist  Boolean com.app.notiminimalist  Box com.app.notiminimalist  Build com.app.notiminimalist  Bundle com.app.notiminimalist  Cancel com.app.notiminimalist  CardDefaults com.app.notiminimalist  CheckCircle com.app.notiminimalist  Checkbox com.app.notiminimalist  CheckboxDefaults com.app.notiminimalist  ChevronRight com.app.notiminimalist  ChooseAppsScreen com.app.notiminimalist  CircleShape com.app.notiminimalist  CircularProgressIndicator com.app.notiminimalist  Clear com.app.notiminimalist  Color com.app.notiminimalist  Column com.app.notiminimalist  ColumnScope com.app.notiminimalist  ComponentActivity com.app.notiminimalist  
Composable com.app.notiminimalist  Context com.app.notiminimalist  DarkMode com.app.notiminimalist  DateTimeFormatter com.app.notiminimalist  DoNotDisturb com.app.notiminimalist  EaseInOutCubic com.app.notiminimalist  ExperimentalMaterial3Api com.app.notiminimalist  
FontWeight com.app.notiminimalist  Home com.app.notiminimalist  
HomeScreen com.app.notiminimalist  HomeScreenPreview com.app.notiminimalist  Icon com.app.notiminimalist  
IconButton com.app.notiminimalist  Icons com.app.notiminimalist  Info com.app.notiminimalist  Int com.app.notiminimalist  	LightMode com.app.notiminimalist  List com.app.notiminimalist  	LocalDate com.app.notiminimalist  	LocalTime com.app.notiminimalist  MainActivity com.app.notiminimalist  MainApp com.app.notiminimalist  
MaterialTheme com.app.notiminimalist  
ModernAppIcon com.app.notiminimalist  ModernAppIconFallback com.app.notiminimalist  
ModernAppItem com.app.notiminimalist  ModernAppsHeader com.app.notiminimalist  ModernEmptyState com.app.notiminimalist  ModernHeroSection com.app.notiminimalist  ModernLoadingState com.app.notiminimalist  ModernScheduleCard com.app.notiminimalist  ModernSearchSection com.app.notiminimalist  ModernSelectionControl com.app.notiminimalist  ModernSettingsHeader com.app.notiminimalist  ModernSettingsItem com.app.notiminimalist  ModernSettingsSection com.app.notiminimalist  ModernSilencingToggle com.app.notiminimalist  ModernToggleButton com.app.notiminimalist  Modifier com.app.notiminimalist  NavigationBarItemDefaults com.app.notiminimalist  NotiBlue com.app.notiminimalist  NotiGray com.app.notiminimalist  
Notifications com.app.notiminimalist  NotificationsOff com.app.notiminimalist  OptIn com.app.notiminimalist  OutlinedButton com.app.notiminimalist  OutlinedTextField com.app.notiminimalist  OutlinedTextFieldDefaults com.app.notiminimalist  PermissionDialog com.app.notiminimalist  PermissionManager com.app.notiminimalist  PermissionStates com.app.notiminimalist  PermissionWarningCard com.app.notiminimalist  PhoneAndroid com.app.notiminimalist  Preview com.app.notiminimalist  
QuickStatItem com.app.notiminimalist  QuickStatsCard com.app.notiminimalist  R com.app.notiminimalist  Role com.app.notiminimalist  RoundedCornerShape com.app.notiminimalist  Row com.app.notiminimalist  Schedule com.app.notiminimalist  Search com.app.notiminimalist  	SearchOff com.app.notiminimalist  Security com.app.notiminimalist  Settings com.app.notiminimalist  SettingsScreen com.app.notiminimalist  Shield com.app.notiminimalist  
SilencingMode com.app.notiminimalist  Spacer com.app.notiminimalist  String com.app.notiminimalist  Surface com.app.notiminimalist  Text com.app.notiminimalist  	TextAlign com.app.notiminimalist  TextOverflow com.app.notiminimalist  TimeSelectionCard com.app.notiminimalist  Timer com.app.notiminimalist  TopAppBarDefaults com.app.notiminimalist  Triple com.app.notiminimalist  Unit com.app.notiminimalist  Warning com.app.notiminimalist  WindowCompat com.app.notiminimalist  align com.app.notiminimalist  android com.app.notiminimalist  androidx com.app.notiminimalist  animateDpAsState com.app.notiminimalist  apply com.app.notiminimalist  
background com.app.notiminimalist  calculateDuration com.app.notiminimalist  
cardColors com.app.notiminimalist  
cardElevation com.app.notiminimalist  	clickable com.app.notiminimalist  clip com.app.notiminimalist  colors com.app.notiminimalist  contains com.app.notiminimalist  count com.app.notiminimalist  createNavigationSemantics com.app.notiminimalist  createToggleSemantics com.app.notiminimalist  	emptyList com.app.notiminimalist  fillMaxSize com.app.notiminimalist  fillMaxWidth com.app.notiminimalist  filter com.app.notiminimalist  forEachIndexed com.app.notiminimalist  getValue com.app.notiminimalist  height com.app.notiminimalist  isBlank com.app.notiminimalist  
isNotBlank com.app.notiminimalist  
isNotEmpty com.app.notiminimalist  kotlinx com.app.notiminimalist  launch com.app.notiminimalist  let com.app.notiminimalist  listOf com.app.notiminimalist  map com.app.notiminimalist  offset com.app.notiminimalist  outlinedButtonColors com.app.notiminimalist  padding com.app.notiminimalist  painterResource com.app.notiminimalist  provideDelegate com.app.notiminimalist  radialGradient com.app.notiminimalist  run com.app.notiminimalist  	semantics com.app.notiminimalist  size com.app.notiminimalist  spacedBy com.app.notiminimalist  take com.app.notiminimalist  topAppBarColors com.app.notiminimalist  tween com.app.notiminimalist  	uppercase com.app.notiminimalist  weight com.app.notiminimalist  width com.app.notiminimalist  MainApp #com.app.notiminimalist.MainActivity  PermissionManager #com.app.notiminimalist.MainActivity  WindowCompat #com.app.notiminimalist.MainActivity  enableEdgeToEdge #com.app.notiminimalist.MainActivity  let #com.app.notiminimalist.MainActivity  permissionManager #com.app.notiminimalist.MainActivity  
setContent #com.app.notiminimalist.MainActivity  window #com.app.notiminimalist.MainActivity  dnd 'com.app.notiminimalist.PermissionStates  
exactAlarm 'com.app.notiminimalist.PermissionStates  notificationListener 'com.app.notiminimalist.PermissionStates  app_logo !com.app.notiminimalist.R.drawable  graphics com.app.notiminimalist.android  drawable 'com.app.notiminimalist.android.graphics  Drawable 0com.app.notiminimalist.android.graphics.drawable  compose com.app.notiminimalist.androidx  ui 'com.app.notiminimalist.androidx.compose  graphics *com.app.notiminimalist.androidx.compose.ui  vector 3com.app.notiminimalist.androidx.compose.ui.graphics  ImageVector :com.app.notiminimalist.androidx.compose.ui.graphics.vector  AppInfo com.app.notiminimalist.data  AppUsageInfo com.app.notiminimalist.data  Boolean com.app.notiminimalist.data  Context com.app.notiminimalist.data  DEFAULT_END_HOUR com.app.notiminimalist.data  DEFAULT_END_MINUTE com.app.notiminimalist.data  DEFAULT_START_HOUR com.app.notiminimalist.data  DEFAULT_START_MINUTE com.app.notiminimalist.data  Drawable com.app.notiminimalist.data  Int com.app.notiminimalist.data  KEY_ALWAYS_ALLOWED_APPS com.app.notiminimalist.data  KEY_END_HOUR com.app.notiminimalist.data  KEY_END_MINUTE com.app.notiminimalist.data  
KEY_FIRST_RUN com.app.notiminimalist.data  KEY_SELECTED_APPS com.app.notiminimalist.data  KEY_SELECT_ALL_APPS com.app.notiminimalist.data  KEY_SILENCED_APPS com.app.notiminimalist.data  KEY_SILENCING_ENABLED com.app.notiminimalist.data  KEY_START_HOUR com.app.notiminimalist.data  KEY_START_MINUTE com.app.notiminimalist.data  	LocalTime com.app.notiminimalist.data  Long com.app.notiminimalist.data  
PREFS_NAME com.app.notiminimalist.data  SchedulePreferences com.app.notiminimalist.data  Set com.app.notiminimalist.data  SharedPreferences com.app.notiminimalist.data  
SilencingMode com.app.notiminimalist.data  String com.app.notiminimalist.data  System com.app.notiminimalist.data  contains com.app.notiminimalist.data  emptySet com.app.notiminimalist.data  format com.app.notiminimalist.data  let com.app.notiminimalist.data  	lowercase com.app.notiminimalist.data  
startsWith com.app.notiminimalist.data  take com.app.notiminimalist.data  toMutableSet com.app.notiminimalist.data  
SilencingMode #com.app.notiminimalist.data.AppInfo  appName #com.app.notiminimalist.data.AppInfo  contains #com.app.notiminimalist.data.AppInfo  copy #com.app.notiminimalist.data.AppInfo  getDisplayName #com.app.notiminimalist.data.AppInfo  getSilencingModeDescription #com.app.notiminimalist.data.AppInfo  icon #com.app.notiminimalist.data.AppInfo  isSystemApp #com.app.notiminimalist.data.AppInfo  let #com.app.notiminimalist.data.AppInfo  	lowercase #com.app.notiminimalist.data.AppInfo  packageName #com.app.notiminimalist.data.AppInfo  
shouldShow #com.app.notiminimalist.data.AppInfo  
silencingMode #com.app.notiminimalist.data.AppInfo  
startsWith #com.app.notiminimalist.data.AppInfo  take #com.app.notiminimalist.data.AppInfo  
SilencingMode (com.app.notiminimalist.data.AppUsageInfo  System (com.app.notiminimalist.data.AppUsageInfo  lastNotificationTime (com.app.notiminimalist.data.AppUsageInfo  
silencingMode (com.app.notiminimalist.data.AppUsageInfo  Boolean /com.app.notiminimalist.data.SchedulePreferences  Context /com.app.notiminimalist.data.SchedulePreferences  DEFAULT_END_HOUR /com.app.notiminimalist.data.SchedulePreferences  DEFAULT_END_MINUTE /com.app.notiminimalist.data.SchedulePreferences  DEFAULT_START_HOUR /com.app.notiminimalist.data.SchedulePreferences  DEFAULT_START_MINUTE /com.app.notiminimalist.data.SchedulePreferences  KEY_ALWAYS_ALLOWED_APPS /com.app.notiminimalist.data.SchedulePreferences  KEY_END_HOUR /com.app.notiminimalist.data.SchedulePreferences  KEY_END_MINUTE /com.app.notiminimalist.data.SchedulePreferences  
KEY_FIRST_RUN /com.app.notiminimalist.data.SchedulePreferences  KEY_SELECTED_APPS /com.app.notiminimalist.data.SchedulePreferences  KEY_SELECT_ALL_APPS /com.app.notiminimalist.data.SchedulePreferences  KEY_SILENCED_APPS /com.app.notiminimalist.data.SchedulePreferences  KEY_SILENCING_ENABLED /com.app.notiminimalist.data.SchedulePreferences  KEY_START_HOUR /com.app.notiminimalist.data.SchedulePreferences  KEY_START_MINUTE /com.app.notiminimalist.data.SchedulePreferences  	LocalTime /com.app.notiminimalist.data.SchedulePreferences  
PREFS_NAME /com.app.notiminimalist.data.SchedulePreferences  Set /com.app.notiminimalist.data.SchedulePreferences  SharedPreferences /com.app.notiminimalist.data.SchedulePreferences  
SilencingMode /com.app.notiminimalist.data.SchedulePreferences  String /com.app.notiminimalist.data.SchedulePreferences  addSelectedApp /com.app.notiminimalist.data.SchedulePreferences  alwaysAllowedApps /com.app.notiminimalist.data.SchedulePreferences  clearSelectedApps /com.app.notiminimalist.data.SchedulePreferences  emptySet /com.app.notiminimalist.data.SchedulePreferences  endTime /com.app.notiminimalist.data.SchedulePreferences  format /com.app.notiminimalist.data.SchedulePreferences  getAppSilencingMode /com.app.notiminimalist.data.SchedulePreferences  getFormattedSchedule /com.app.notiminimalist.data.SchedulePreferences  getFormattedTime /com.app.notiminimalist.data.SchedulePreferences  
isAppSelected /com.app.notiminimalist.data.SchedulePreferences  
isFirstRun /com.app.notiminimalist.data.SchedulePreferences  isSelectAllAppsEnabled /com.app.notiminimalist.data.SchedulePreferences  isSilencingEnabled /com.app.notiminimalist.data.SchedulePreferences  preferences /com.app.notiminimalist.data.SchedulePreferences  removeFromAllSilencingSets /com.app.notiminimalist.data.SchedulePreferences  removeSelectedApp /com.app.notiminimalist.data.SchedulePreferences  selectedApps /com.app.notiminimalist.data.SchedulePreferences  setAppSilencingMode /com.app.notiminimalist.data.SchedulePreferences  silencedApps /com.app.notiminimalist.data.SchedulePreferences  	startTime /com.app.notiminimalist.data.SchedulePreferences  toMutableSet /com.app.notiminimalist.data.SchedulePreferences  Context 9com.app.notiminimalist.data.SchedulePreferences.Companion  DEFAULT_END_HOUR 9com.app.notiminimalist.data.SchedulePreferences.Companion  DEFAULT_END_MINUTE 9com.app.notiminimalist.data.SchedulePreferences.Companion  DEFAULT_START_HOUR 9com.app.notiminimalist.data.SchedulePreferences.Companion  DEFAULT_START_MINUTE 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_ALWAYS_ALLOWED_APPS 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_END_HOUR 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_END_MINUTE 9com.app.notiminimalist.data.SchedulePreferences.Companion  
KEY_FIRST_RUN 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_SELECTED_APPS 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_SELECT_ALL_APPS 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_SILENCED_APPS 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_SILENCING_ENABLED 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_START_HOUR 9com.app.notiminimalist.data.SchedulePreferences.Companion  KEY_START_MINUTE 9com.app.notiminimalist.data.SchedulePreferences.Companion  	LocalTime 9com.app.notiminimalist.data.SchedulePreferences.Companion  
PREFS_NAME 9com.app.notiminimalist.data.SchedulePreferences.Companion  
SilencingMode 9com.app.notiminimalist.data.SchedulePreferences.Companion  String 9com.app.notiminimalist.data.SchedulePreferences.Companion  emptySet 9com.app.notiminimalist.data.SchedulePreferences.Companion  format 9com.app.notiminimalist.data.SchedulePreferences.Companion  toMutableSet 9com.app.notiminimalist.data.SchedulePreferences.Companion  ALWAYS_ALLOWED )com.app.notiminimalist.data.SilencingMode  SILENCED )com.app.notiminimalist.data.SilencingMode  AlarmScheduler com.app.notiminimalist.receiver  BroadcastReceiver com.app.notiminimalist.receiver  Context com.app.notiminimalist.receiver  CoroutineScope com.app.notiminimalist.receiver  Dispatchers com.app.notiminimalist.receiver  
DndManager com.app.notiminimalist.receiver  	Exception com.app.notiminimalist.receiver  Intent com.app.notiminimalist.receiver  Log com.app.notiminimalist.receiver  SchedulePreferences com.app.notiminimalist.receiver  SilencingReceiver com.app.notiminimalist.receiver  TAG com.app.notiminimalist.receiver  launch com.app.notiminimalist.receiver  AlarmScheduler 1com.app.notiminimalist.receiver.SilencingReceiver  	Companion 1com.app.notiminimalist.receiver.SilencingReceiver  Context 1com.app.notiminimalist.receiver.SilencingReceiver  CoroutineScope 1com.app.notiminimalist.receiver.SilencingReceiver  Dispatchers 1com.app.notiminimalist.receiver.SilencingReceiver  
DndManager 1com.app.notiminimalist.receiver.SilencingReceiver  	Exception 1com.app.notiminimalist.receiver.SilencingReceiver  Intent 1com.app.notiminimalist.receiver.SilencingReceiver  Log 1com.app.notiminimalist.receiver.SilencingReceiver  SchedulePreferences 1com.app.notiminimalist.receiver.SilencingReceiver  TAG 1com.app.notiminimalist.receiver.SilencingReceiver  handleBootCompleted 1com.app.notiminimalist.receiver.SilencingReceiver  handleEndSilencing 1com.app.notiminimalist.receiver.SilencingReceiver  handleStartSilencing 1com.app.notiminimalist.receiver.SilencingReceiver  handleTimeChanged 1com.app.notiminimalist.receiver.SilencingReceiver  handleTimeZoneChanged 1com.app.notiminimalist.receiver.SilencingReceiver  launch 1com.app.notiminimalist.receiver.SilencingReceiver  AlarmScheduler ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  CoroutineScope ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  Dispatchers ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  
DndManager ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  Intent ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  Log ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  SchedulePreferences ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  TAG ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  launch ;com.app.notiminimalist.receiver.SilencingReceiver.Companion  ACTION_END_SILENCING com.app.notiminimalist.service  ACTION_START_SILENCING com.app.notiminimalist.service  AlarmManager com.app.notiminimalist.service  AlarmScheduler com.app.notiminimalist.service  AudioManager com.app.notiminimalist.service  Boolean com.app.notiminimalist.service  Build com.app.notiminimalist.service  CHANNEL_PREFIX com.app.notiminimalist.service  Calendar com.app.notiminimalist.service  
ComponentName com.app.notiminimalist.service  Context com.app.notiminimalist.service  Date com.app.notiminimalist.service  
DndManager com.app.notiminimalist.service  END_ALARM_REQUEST_CODE com.app.notiminimalist.service  	Exception com.app.notiminimalist.service  Handler com.app.notiminimalist.service  IllegalArgumentException com.app.notiminimalist.service  Int com.app.notiminimalist.service  Intent com.app.notiminimalist.service  List com.app.notiminimalist.service  
LocalDateTime com.app.notiminimalist.service  	LocalTime com.app.notiminimalist.service  Log com.app.notiminimalist.service  Long com.app.notiminimalist.service  Looper com.app.notiminimalist.service  Map com.app.notiminimalist.service  Notification com.app.notiminimalist.service  NotificationChannel com.app.notiminimalist.service  NotificationChannelManager com.app.notiminimalist.service  NotificationListenerService com.app.notiminimalist.service  NotificationManager com.app.notiminimalist.service  "NotiminimalistNotificationListener com.app.notiminimalist.service  "PREFS_ORIGINAL_NOTIFICATION_VOLUME com.app.notiminimalist.service  PackageManager com.app.notiminimalist.service  
PendingIntent com.app.notiminimalist.service  RequiresApi com.app.notiminimalist.service  START_ALARM_REQUEST_CODE com.app.notiminimalist.service  SchedulePreferences com.app.notiminimalist.service  SecurityException com.app.notiminimalist.service  
SilencingMode com.app.notiminimalist.service  SilencingReceiver com.app.notiminimalist.service  StatusBarNotification com.app.notiminimalist.service  String com.app.notiminimalist.service  Suppress com.app.notiminimalist.service  TAG com.app.notiminimalist.service  UnsupportedOperationException com.app.notiminimalist.service  ZoneId com.app.notiminimalist.service  android com.app.notiminimalist.service  apply com.app.notiminimalist.service  
component1 com.app.notiminimalist.service  
component2 com.app.notiminimalist.service  count com.app.notiminimalist.service  emptyMap com.app.notiminimalist.service  forEach com.app.notiminimalist.service  groupBy com.app.notiminimalist.service  
isNotEmpty com.app.notiminimalist.service  java com.app.notiminimalist.service  listOf com.app.notiminimalist.service  maxOf com.app.notiminimalist.service  minOf com.app.notiminimalist.service  
mutableListOf com.app.notiminimalist.service  mutableMapOf com.app.notiminimalist.service  plus com.app.notiminimalist.service  removePrefix com.app.notiminimalist.service  
requestRebind com.app.notiminimalist.service  set com.app.notiminimalist.service  
startsWith com.app.notiminimalist.service  ACTION_END_SILENCING -com.app.notiminimalist.service.AlarmScheduler  ACTION_START_SILENCING -com.app.notiminimalist.service.AlarmScheduler  AlarmManager -com.app.notiminimalist.service.AlarmScheduler  Boolean -com.app.notiminimalist.service.AlarmScheduler  Build -com.app.notiminimalist.service.AlarmScheduler  Calendar -com.app.notiminimalist.service.AlarmScheduler  	Companion -com.app.notiminimalist.service.AlarmScheduler  Context -com.app.notiminimalist.service.AlarmScheduler  Date -com.app.notiminimalist.service.AlarmScheduler  END_ALARM_REQUEST_CODE -com.app.notiminimalist.service.AlarmScheduler  	Exception -com.app.notiminimalist.service.AlarmScheduler  Int -com.app.notiminimalist.service.AlarmScheduler  Intent -com.app.notiminimalist.service.AlarmScheduler  
LocalDateTime -com.app.notiminimalist.service.AlarmScheduler  	LocalTime -com.app.notiminimalist.service.AlarmScheduler  Log -com.app.notiminimalist.service.AlarmScheduler  Long -com.app.notiminimalist.service.AlarmScheduler  
PendingIntent -com.app.notiminimalist.service.AlarmScheduler  START_ALARM_REQUEST_CODE -com.app.notiminimalist.service.AlarmScheduler  SchedulePreferences -com.app.notiminimalist.service.AlarmScheduler  SecurityException -com.app.notiminimalist.service.AlarmScheduler  SilencingReceiver -com.app.notiminimalist.service.AlarmScheduler  String -com.app.notiminimalist.service.AlarmScheduler  TAG -com.app.notiminimalist.service.AlarmScheduler  ZoneId -com.app.notiminimalist.service.AlarmScheduler  alarmManager -com.app.notiminimalist.service.AlarmScheduler  android -com.app.notiminimalist.service.AlarmScheduler  apply -com.app.notiminimalist.service.AlarmScheduler  calculateNextTriggerTime -com.app.notiminimalist.service.AlarmScheduler  canScheduleExactAlarms -com.app.notiminimalist.service.AlarmScheduler  cancelAlarms -com.app.notiminimalist.service.AlarmScheduler  cancelEndAlarm -com.app.notiminimalist.service.AlarmScheduler  cancelStartAlarm -com.app.notiminimalist.service.AlarmScheduler  context -com.app.notiminimalist.service.AlarmScheduler  getSimpleAlarmDescription -com.app.notiminimalist.service.AlarmScheduler  java -com.app.notiminimalist.service.AlarmScheduler  minOf -com.app.notiminimalist.service.AlarmScheduler  rescheduleAlarms -com.app.notiminimalist.service.AlarmScheduler  scheduleAlarms -com.app.notiminimalist.service.AlarmScheduler  scheduleEndAlarm -com.app.notiminimalist.service.AlarmScheduler  scheduleExactAlarm -com.app.notiminimalist.service.AlarmScheduler  schedulePreferences -com.app.notiminimalist.service.AlarmScheduler  scheduleSimpleAlarms -com.app.notiminimalist.service.AlarmScheduler  scheduleStartAlarm -com.app.notiminimalist.service.AlarmScheduler  ACTION_END_SILENCING 7com.app.notiminimalist.service.AlarmScheduler.Companion  ACTION_START_SILENCING 7com.app.notiminimalist.service.AlarmScheduler.Companion  AlarmManager 7com.app.notiminimalist.service.AlarmScheduler.Companion  Build 7com.app.notiminimalist.service.AlarmScheduler.Companion  Calendar 7com.app.notiminimalist.service.AlarmScheduler.Companion  Context 7com.app.notiminimalist.service.AlarmScheduler.Companion  Date 7com.app.notiminimalist.service.AlarmScheduler.Companion  END_ALARM_REQUEST_CODE 7com.app.notiminimalist.service.AlarmScheduler.Companion  Intent 7com.app.notiminimalist.service.AlarmScheduler.Companion  
LocalDateTime 7com.app.notiminimalist.service.AlarmScheduler.Companion  Log 7com.app.notiminimalist.service.AlarmScheduler.Companion  
PendingIntent 7com.app.notiminimalist.service.AlarmScheduler.Companion  START_ALARM_REQUEST_CODE 7com.app.notiminimalist.service.AlarmScheduler.Companion  SchedulePreferences 7com.app.notiminimalist.service.AlarmScheduler.Companion  SilencingReceiver 7com.app.notiminimalist.service.AlarmScheduler.Companion  TAG 7com.app.notiminimalist.service.AlarmScheduler.Companion  ZoneId 7com.app.notiminimalist.service.AlarmScheduler.Companion  android 7com.app.notiminimalist.service.AlarmScheduler.Companion  apply 7com.app.notiminimalist.service.AlarmScheduler.Companion  java 7com.app.notiminimalist.service.AlarmScheduler.Companion  minOf 7com.app.notiminimalist.service.AlarmScheduler.Companion  AudioManager )com.app.notiminimalist.service.DndManager  Boolean )com.app.notiminimalist.service.DndManager  Build )com.app.notiminimalist.service.DndManager  Context )com.app.notiminimalist.service.DndManager  	Exception )com.app.notiminimalist.service.DndManager  List )com.app.notiminimalist.service.DndManager  Log )com.app.notiminimalist.service.DndManager  NotificationChannelManager )com.app.notiminimalist.service.DndManager  NotificationManager )com.app.notiminimalist.service.DndManager  "PREFS_ORIGINAL_NOTIFICATION_VOLUME )com.app.notiminimalist.service.DndManager  SchedulePreferences )com.app.notiminimalist.service.DndManager  String )com.app.notiminimalist.service.DndManager  TAG )com.app.notiminimalist.service.DndManager  UnsupportedOperationException )com.app.notiminimalist.service.DndManager  applyAggressiveAudioSuppression )com.app.notiminimalist.service.DndManager  !applyConservativeAudioSuppression )com.app.notiminimalist.service.DndManager  applyEnhancedChannelMuting )com.app.notiminimalist.service.DndManager  audioManager )com.app.notiminimalist.service.DndManager  channelManager )com.app.notiminimalist.service.DndManager  context )com.app.notiminimalist.service.DndManager  
disableDnd )com.app.notiminimalist.service.DndManager  	enableDnd )com.app.notiminimalist.service.DndManager  getDndStatusDescription )com.app.notiminimalist.service.DndManager  
hasPermission )com.app.notiminimalist.service.DndManager  isCurrentlyInSilencingHours )com.app.notiminimalist.service.DndManager  isDndActive )com.app.notiminimalist.service.DndManager  
isNotEmpty )com.app.notiminimalist.service.DndManager  java )com.app.notiminimalist.service.DndManager  maxOf )com.app.notiminimalist.service.DndManager  
mutableListOf )com.app.notiminimalist.service.DndManager  notificationManager )com.app.notiminimalist.service.DndManager  preferences )com.app.notiminimalist.service.DndManager  #restoreConservativeAudioSuppression )com.app.notiminimalist.service.DndManager  restoreEnhancedChannelMuting )com.app.notiminimalist.service.DndManager  schedulePreferences )com.app.notiminimalist.service.DndManager  updateNotificationChannels )com.app.notiminimalist.service.DndManager  AudioManager 3com.app.notiminimalist.service.DndManager.Companion  Build 3com.app.notiminimalist.service.DndManager.Companion  Context 3com.app.notiminimalist.service.DndManager.Companion  Log 3com.app.notiminimalist.service.DndManager.Companion  NotificationChannelManager 3com.app.notiminimalist.service.DndManager.Companion  NotificationManager 3com.app.notiminimalist.service.DndManager.Companion  "PREFS_ORIGINAL_NOTIFICATION_VOLUME 3com.app.notiminimalist.service.DndManager.Companion  SchedulePreferences 3com.app.notiminimalist.service.DndManager.Companion  TAG 3com.app.notiminimalist.service.DndManager.Companion  UnsupportedOperationException 3com.app.notiminimalist.service.DndManager.Companion  
isNotEmpty 3com.app.notiminimalist.service.DndManager.Companion  java 3com.app.notiminimalist.service.DndManager.Companion  maxOf 3com.app.notiminimalist.service.DndManager.Companion  
mutableListOf 3com.app.notiminimalist.service.DndManager.Companion  Policy =com.app.notiminimalist.service.DndManager.NotificationManager  Boolean 9com.app.notiminimalist.service.NotificationChannelManager  Build 9com.app.notiminimalist.service.NotificationChannelManager  CHANNEL_PREFIX 9com.app.notiminimalist.service.NotificationChannelManager  Context 9com.app.notiminimalist.service.NotificationChannelManager  	Exception 9com.app.notiminimalist.service.NotificationChannelManager  Log 9com.app.notiminimalist.service.NotificationChannelManager  NotificationChannel 9com.app.notiminimalist.service.NotificationChannelManager  NotificationManager 9com.app.notiminimalist.service.NotificationChannelManager  PackageManager 9com.app.notiminimalist.service.NotificationChannelManager  RequiresApi 9com.app.notiminimalist.service.NotificationChannelManager  SchedulePreferences 9com.app.notiminimalist.service.NotificationChannelManager  
SilencingMode 9com.app.notiminimalist.service.NotificationChannelManager  String 9com.app.notiminimalist.service.NotificationChannelManager  TAG 9com.app.notiminimalist.service.NotificationChannelManager  apply 9com.app.notiminimalist.service.NotificationChannelManager  areChannelsProperlyConfigured 9com.app.notiminimalist.service.NotificationChannelManager   areNotificationChannelsSupported 9com.app.notiminimalist.service.NotificationChannelManager  cleanupUnusedChannels 9com.app.notiminimalist.service.NotificationChannelManager  context 9com.app.notiminimalist.service.NotificationChannelManager  count 9com.app.notiminimalist.service.NotificationChannelManager  
getAppName 9com.app.notiminimalist.service.NotificationChannelManager  getChannelStatusDescription 9com.app.notiminimalist.service.NotificationChannelManager  listOf 9com.app.notiminimalist.service.NotificationChannelManager  muteExistingChannelsTemporarily 9com.app.notiminimalist.service.NotificationChannelManager  notificationManager 9com.app.notiminimalist.service.NotificationChannelManager  packageManager 9com.app.notiminimalist.service.NotificationChannelManager  plus 9com.app.notiminimalist.service.NotificationChannelManager  removePrefix 9com.app.notiminimalist.service.NotificationChannelManager  restoreOriginalChannels 9com.app.notiminimalist.service.NotificationChannelManager  schedulePreferences 9com.app.notiminimalist.service.NotificationChannelManager  silenceExistingAppChannels 9com.app.notiminimalist.service.NotificationChannelManager  
startsWith 9com.app.notiminimalist.service.NotificationChannelManager  updateChannelForApp 9com.app.notiminimalist.service.NotificationChannelManager  updateNotificationChannels 9com.app.notiminimalist.service.NotificationChannelManager  Build Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  CHANNEL_PREFIX Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  Context Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  Log Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  NotificationChannel Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  NotificationManager Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  SchedulePreferences Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  
SilencingMode Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  TAG Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  apply Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  count Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  listOf Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  plus Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  removePrefix Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  
startsWith Ccom.app.notiminimalist.service.NotificationChannelManager.Companion  NameNotFoundException Hcom.app.notiminimalist.service.NotificationChannelManager.PackageManager  Policy 2com.app.notiminimalist.service.NotificationManager  AudioManager Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Boolean Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Build Acom.app.notiminimalist.service.NotiminimalistNotificationListener  	Companion Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
ComponentName Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Context Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
DndManager Acom.app.notiminimalist.service.NotiminimalistNotificationListener  	Exception Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Handler Acom.app.notiminimalist.service.NotiminimalistNotificationListener  IllegalArgumentException Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Int Acom.app.notiminimalist.service.NotiminimalistNotificationListener  	LocalTime Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Log Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Looper Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Map Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Notification Acom.app.notiminimalist.service.NotiminimalistNotificationListener  NotificationManager Acom.app.notiminimalist.service.NotiminimalistNotificationListener  "NotiminimalistNotificationListener Acom.app.notiminimalist.service.NotiminimalistNotificationListener  SchedulePreferences Acom.app.notiminimalist.service.NotiminimalistNotificationListener  SecurityException Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
SilencingMode Acom.app.notiminimalist.service.NotiminimalistNotificationListener  StatusBarNotification Acom.app.notiminimalist.service.NotiminimalistNotificationListener  String Acom.app.notiminimalist.service.NotiminimalistNotificationListener  Suppress Acom.app.notiminimalist.service.NotiminimalistNotificationListener  TAG Acom.app.notiminimalist.service.NotiminimalistNotificationListener  activeNotifications Acom.app.notiminimalist.service.NotiminimalistNotificationListener  android Acom.app.notiminimalist.service.NotiminimalistNotificationListener  audioManager Acom.app.notiminimalist.service.NotiminimalistNotificationListener  cancelNotification Acom.app.notiminimalist.service.NotiminimalistNotificationListener  "clearExistingSilencedNotifications Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
component1 Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
component2 Acom.app.notiminimalist.service.NotiminimalistNotificationListener  dismissNotification Acom.app.notiminimalist.service.NotiminimalistNotificationListener  dismissNotificationAggressively Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
dndManager Acom.app.notiminimalist.service.NotiminimalistNotificationListener  emptyMap Acom.app.notiminimalist.service.NotiminimalistNotificationListener  forEach Acom.app.notiminimalist.service.NotiminimalistNotificationListener  getSystemService Acom.app.notiminimalist.service.NotiminimalistNotificationListener  groupBy Acom.app.notiminimalist.service.NotiminimalistNotificationListener  isCurrentlyInSilencingHours Acom.app.notiminimalist.service.NotiminimalistNotificationListener  isSystemOrCriticalNotification Acom.app.notiminimalist.service.NotiminimalistNotificationListener  java Acom.app.notiminimalist.service.NotiminimalistNotificationListener  mutableMapOf Acom.app.notiminimalist.service.NotiminimalistNotificationListener  originalNotificationVolume Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
requestRebind Acom.app.notiminimalist.service.NotiminimalistNotificationListener  restoreNotificationAudio Acom.app.notiminimalist.service.NotiminimalistNotificationListener  restoreNotificationAudioDelayed Acom.app.notiminimalist.service.NotiminimalistNotificationListener  schedulePreferences Acom.app.notiminimalist.service.NotiminimalistNotificationListener  set Acom.app.notiminimalist.service.NotiminimalistNotificationListener  
startsWith Acom.app.notiminimalist.service.NotiminimalistNotificationListener  %suppressNotificationAudioPreemptively Acom.app.notiminimalist.service.NotiminimalistNotificationListener  AudioManager Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Build Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
ComponentName Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Context Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
DndManager Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Handler Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  	LocalTime Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Log Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Looper Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  Notification Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  NotificationManager Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  "NotiminimalistNotificationListener Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  SchedulePreferences Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
SilencingMode Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  TAG Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  android Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
component1 Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
component2 Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  emptyMap Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  forEach Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  groupBy Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  java Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  mutableMapOf Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
requestRebind Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  set Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  
startsWith Kcom.app.notiminimalist.service.NotiminimalistNotificationListener.Companion  NameNotFoundException -com.app.notiminimalist.service.PackageManager  AccessibilityHelper com.app.notiminimalist.ui  	Alignment com.app.notiminimalist.ui  Arrangement com.app.notiminimalist.ui  	ArrowBack com.app.notiminimalist.ui  ArrowForward com.app.notiminimalist.ui  Boolean com.app.notiminimalist.ui  Box com.app.notiminimalist.ui  Button com.app.notiminimalist.ui  ButtonDefaults com.app.notiminimalist.ui  Card com.app.notiminimalist.ui  CardDefaults com.app.notiminimalist.ui  CheckCircle com.app.notiminimalist.ui  Close com.app.notiminimalist.ui  Color com.app.notiminimalist.ui  Column com.app.notiminimalist.ui  
Composable com.app.notiminimalist.ui  	DateRange com.app.notiminimalist.ui  ExperimentalMaterial3Api com.app.notiminimalist.ui  
FontWeight com.app.notiminimalist.ui  Icon com.app.notiminimalist.ui  
IconButton com.app.notiminimalist.ui  Icons com.app.notiminimalist.ui  ImageVector com.app.notiminimalist.ui  Info com.app.notiminimalist.ui  Int com.app.notiminimalist.ui  LaunchedEffect com.app.notiminimalist.ui  LinearProgressIndicator com.app.notiminimalist.ui  List com.app.notiminimalist.ui  LiveRegionMode com.app.notiminimalist.ui  
MaterialTheme com.app.notiminimalist.ui  Modifier com.app.notiminimalist.ui  NotiBlue com.app.notiminimalist.ui  NotiGray com.app.notiminimalist.ui  
Notifications com.app.notiminimalist.ui  OptIn com.app.notiminimalist.ui  OutlinedButton com.app.notiminimalist.ui  PermissionManager com.app.notiminimalist.ui  PermissionSetupWizard com.app.notiminimalist.ui  PermissionStates com.app.notiminimalist.ui  PermissionStatusIndicator com.app.notiminimalist.ui  PermissionStep com.app.notiminimalist.ui  Role com.app.notiminimalist.ui  RoundedCornerShape com.app.notiminimalist.ui  Row com.app.notiminimalist.ui  Settings com.app.notiminimalist.ui  Spacer com.app.notiminimalist.ui  String com.app.notiminimalist.ui  Text com.app.notiminimalist.ui  	TextAlign com.app.notiminimalist.ui  
TextButton com.app.notiminimalist.ui  Unit com.app.notiminimalist.ui  Warning com.app.notiminimalist.ui  
WizardContent com.app.notiminimalist.ui  WizardHeader com.app.notiminimalist.ui  WizardNavigation com.app.notiminimalist.ui  buttonColors com.app.notiminimalist.ui  canProceedToNext com.app.notiminimalist.ui  
cardColors com.app.notiminimalist.ui  contentDescription com.app.notiminimalist.ui  createProgressSemantics com.app.notiminimalist.ui  
fillMaxHeight com.app.notiminimalist.ui  fillMaxSize com.app.notiminimalist.ui  fillMaxWidth com.app.notiminimalist.ui  getValue com.app.notiminimalist.ui  heading com.app.notiminimalist.ui  height com.app.notiminimalist.ui  kotlinx com.app.notiminimalist.ui  let com.app.notiminimalist.ui  listOf com.app.notiminimalist.ui  
liveRegion com.app.notiminimalist.ui  mutableIntStateOf com.app.notiminimalist.ui  mutableStateOf com.app.notiminimalist.ui  padding com.app.notiminimalist.ui  provideDelegate com.app.notiminimalist.ui  remember com.app.notiminimalist.ui  role com.app.notiminimalist.ui  	semantics com.app.notiminimalist.ui  setValue com.app.notiminimalist.ui  size com.app.notiminimalist.ui  weight com.app.notiminimalist.ui  width com.app.notiminimalist.ui  dnd *com.app.notiminimalist.ui.PermissionStates  
exactAlarm *com.app.notiminimalist.ui.PermissionStates  notificationListener *com.app.notiminimalist.ui.PermissionStates  actionLabel (com.app.notiminimalist.ui.PermissionStep  additionalInfo (com.app.notiminimalist.ui.PermissionStep  description (com.app.notiminimalist.ui.PermissionStep  icon (com.app.notiminimalist.ui.PermissionStep  	isGranted (com.app.notiminimalist.ui.PermissionStep  
isRequired (com.app.notiminimalist.ui.PermissionStep  onAction (com.app.notiminimalist.ui.PermissionStep  title (com.app.notiminimalist.ui.PermissionStep  Activity com.app.notiminimalist.ui.theme  
Background com.app.notiminimalist.ui.theme  BackgroundDark com.app.notiminimalist.ui.theme  Boolean com.app.notiminimalist.ui.theme  Build com.app.notiminimalist.ui.theme  
Composable com.app.notiminimalist.ui.theme  DarkColorScheme com.app.notiminimalist.ui.theme  Error com.app.notiminimalist.ui.theme  
FontFamily com.app.notiminimalist.ui.theme  
FontWeight com.app.notiminimalist.ui.theme  Info com.app.notiminimalist.ui.theme  LightColorScheme com.app.notiminimalist.ui.theme  	NotiBlack com.app.notiminimalist.ui.theme  NotiBlue com.app.notiminimalist.ui.theme  NotiBlueDark com.app.notiminimalist.ui.theme  
NotiBlueLight com.app.notiminimalist.ui.theme  NotiGray com.app.notiminimalist.ui.theme  NotiGrayDark com.app.notiminimalist.ui.theme  	NotiWhite com.app.notiminimalist.ui.theme  NotiminimalistTheme com.app.notiminimalist.ui.theme  OnBackground com.app.notiminimalist.ui.theme  OnBackgroundDark com.app.notiminimalist.ui.theme  	OnPrimary com.app.notiminimalist.ui.theme  OnPrimaryContainer com.app.notiminimalist.ui.theme  OnPrimaryContainerDark com.app.notiminimalist.ui.theme  OnSecondary com.app.notiminimalist.ui.theme  OnSecondaryContainer com.app.notiminimalist.ui.theme  	OnSurface com.app.notiminimalist.ui.theme  
OnSurfaceDark com.app.notiminimalist.ui.theme  OnSurfaceMuted com.app.notiminimalist.ui.theme  OnSurfaceVariant com.app.notiminimalist.ui.theme  OnSurfaceVariantDark com.app.notiminimalist.ui.theme  OutlineVariantDark com.app.notiminimalist.ui.theme  Pink40 com.app.notiminimalist.ui.theme  Pink80 com.app.notiminimalist.ui.theme  Primary com.app.notiminimalist.ui.theme  PrimaryContainer com.app.notiminimalist.ui.theme  PrimaryContainerDark com.app.notiminimalist.ui.theme  PrimaryDark com.app.notiminimalist.ui.theme  PrimaryDarkMode com.app.notiminimalist.ui.theme  PrimaryLight com.app.notiminimalist.ui.theme  Purple40 com.app.notiminimalist.ui.theme  Purple80 com.app.notiminimalist.ui.theme  PurpleGrey40 com.app.notiminimalist.ui.theme  PurpleGrey80 com.app.notiminimalist.ui.theme  Scrim com.app.notiminimalist.ui.theme  	Secondary com.app.notiminimalist.ui.theme  SecondaryContainer com.app.notiminimalist.ui.theme  
SecondaryDark com.app.notiminimalist.ui.theme  SecondaryLight com.app.notiminimalist.ui.theme  Success com.app.notiminimalist.ui.theme  Surface com.app.notiminimalist.ui.theme  SurfaceContainer com.app.notiminimalist.ui.theme  SurfaceContainerHigh com.app.notiminimalist.ui.theme  SurfaceContainerLow com.app.notiminimalist.ui.theme  SurfaceDark com.app.notiminimalist.ui.theme  SurfaceVariant com.app.notiminimalist.ui.theme  SurfaceVariantDark com.app.notiminimalist.ui.theme  
Typography com.app.notiminimalist.ui.theme  Unit com.app.notiminimalist.ui.theme  Warning com.app.notiminimalist.ui.theme  WindowCompat com.app.notiminimalist.ui.theme  AccessibilityHelper com.app.notiminimalist.utils  AccessibilityManager com.app.notiminimalist.utils  AccessibleColorScheme com.app.notiminimalist.utils  AccessibleSpacer com.app.notiminimalist.utils  ActivityResultContracts com.app.notiminimalist.utils  ActivityResultLauncher com.app.notiminimalist.utils  AnnotatedString com.app.notiminimalist.utils  AppInfo com.app.notiminimalist.utils  
AppManager com.app.notiminimalist.utils  ApplicationInfo com.app.notiminimalist.utils  BatteryOptimizer com.app.notiminimalist.utils  Boolean com.app.notiminimalist.utils  Build com.app.notiminimalist.utils  Color com.app.notiminimalist.utils  ComponentActivity com.app.notiminimalist.utils  
ComponentName com.app.notiminimalist.utils  
Composable com.app.notiminimalist.utils  Context com.app.notiminimalist.utils  Dispatchers com.app.notiminimalist.utils  Dp com.app.notiminimalist.utils  	Exception com.app.notiminimalist.utils  Float com.app.notiminimalist.utils  Int com.app.notiminimalist.utils  Intent com.app.notiminimalist.utils  List com.app.notiminimalist.utils  LiveRegionMode com.app.notiminimalist.utils  Log com.app.notiminimalist.utils  
MaterialTheme com.app.notiminimalist.utils  Modifier com.app.notiminimalist.utils  NotificationManager com.app.notiminimalist.utils  PackageManager com.app.notiminimalist.utils  PermissionManager com.app.notiminimalist.utils  PowerManager com.app.notiminimalist.utils  ProgressBarRangeInfo com.app.notiminimalist.utils  Role com.app.notiminimalist.utils  SchedulePreferences com.app.notiminimalist.utils  SemanticsPropertyReceiver com.app.notiminimalist.utils  Settings com.app.notiminimalist.utils  
SilencingMode com.app.notiminimalist.utils  String com.app.notiminimalist.utils  TAG com.app.notiminimalist.utils  TextUnit com.app.notiminimalist.utils  ToggleableState com.app.notiminimalist.utils  Unit com.app.notiminimalist.utils  Uri com.app.notiminimalist.utils  accessibleClickable com.app.notiminimalist.utils  accessibleToggle com.app.notiminimalist.utils  activity com.app.notiminimalist.utils  all com.app.notiminimalist.utils  also com.app.notiminimalist.utils  android com.app.notiminimalist.utils  apply com.app.notiminimalist.utils  buildString com.app.notiminimalist.utils  com com.app.notiminimalist.utils  contains com.app.notiminimalist.utils  contentDescription com.app.notiminimalist.utils  context com.app.notiminimalist.utils  count com.app.notiminimalist.utils  createInteractiveSemantics com.app.notiminimalist.utils  createToggleSemantics com.app.notiminimalist.utils  disabled com.app.notiminimalist.utils  dropLast com.app.notiminimalist.utils  	emptyList com.app.notiminimalist.utils  filter com.app.notiminimalist.utils  forEach com.app.notiminimalist.utils  getAccessibleColors com.app.notiminimalist.utils  getAccessibleSpacing com.app.notiminimalist.utils  getInstalledApps com.app.notiminimalist.utils  heading com.app.notiminimalist.utils  isScreenReaderEnabled com.app.notiminimalist.utils  java com.app.notiminimalist.utils  joinToString com.app.notiminimalist.utils  last com.app.notiminimalist.utils  let com.app.notiminimalist.utils  listOf com.app.notiminimalist.utils  
liveRegion com.app.notiminimalist.utils  	lowercase com.app.notiminimalist.utils  map com.app.notiminimalist.utils  
mapNotNull com.app.notiminimalist.utils  
mutableListOf com.app.notiminimalist.utils  onClick com.app.notiminimalist.utils  packageManager com.app.notiminimalist.utils  progressBarRangeInfo com.app.notiminimalist.utils  rangeTo com.app.notiminimalist.utils  remember com.app.notiminimalist.utils  role com.app.notiminimalist.utils  schedulePreferences com.app.notiminimalist.utils  selected com.app.notiminimalist.utils  	semantics com.app.notiminimalist.utils  setAppsSilencingModeBatch com.app.notiminimalist.utils  size com.app.notiminimalist.utils  sortedBy com.app.notiminimalist.utils  text com.app.notiminimalist.utils  toSet com.app.notiminimalist.utils  toggleableState com.app.notiminimalist.utils  withContext com.app.notiminimalist.utils  AnnotatedString 0com.app.notiminimalist.utils.AccessibilityHelper  Context 0com.app.notiminimalist.utils.AccessibilityHelper  LiveRegionMode 0com.app.notiminimalist.utils.AccessibilityHelper  ProgressBarRangeInfo 0com.app.notiminimalist.utils.AccessibilityHelper  Role 0com.app.notiminimalist.utils.AccessibilityHelper  ToggleableState 0com.app.notiminimalist.utils.AccessibilityHelper  buildString 0com.app.notiminimalist.utils.AccessibilityHelper  contentDescription 0com.app.notiminimalist.utils.AccessibilityHelper  createInteractiveSemantics 0com.app.notiminimalist.utils.AccessibilityHelper  createNavigationSemantics 0com.app.notiminimalist.utils.AccessibilityHelper  createProgressSemantics 0com.app.notiminimalist.utils.AccessibilityHelper  createToggleSemantics 0com.app.notiminimalist.utils.AccessibilityHelper  disabled 0com.app.notiminimalist.utils.AccessibilityHelper  dp 0com.app.notiminimalist.utils.AccessibilityHelper  getAccessibleFontScale 0com.app.notiminimalist.utils.AccessibilityHelper  getAccessibleSpacing 0com.app.notiminimalist.utils.AccessibilityHelper  heading 0com.app.notiminimalist.utils.AccessibilityHelper  isAccessibilityEnabled 0com.app.notiminimalist.utils.AccessibilityHelper  isScreenReaderEnabled 0com.app.notiminimalist.utils.AccessibilityHelper  let 0com.app.notiminimalist.utils.AccessibilityHelper  
liveRegion 0com.app.notiminimalist.utils.AccessibilityHelper  onClick 0com.app.notiminimalist.utils.AccessibilityHelper  progressBarRangeInfo 0com.app.notiminimalist.utils.AccessibilityHelper  rangeTo 0com.app.notiminimalist.utils.AccessibilityHelper  role 0com.app.notiminimalist.utils.AccessibilityHelper  selected 0com.app.notiminimalist.utils.AccessibilityHelper  text 0com.app.notiminimalist.utils.AccessibilityHelper  toggleableState 0com.app.notiminimalist.utils.AccessibilityHelper  AppInfo 'com.app.notiminimalist.utils.AppManager  ApplicationInfo 'com.app.notiminimalist.utils.AppManager  Boolean 'com.app.notiminimalist.utils.AppManager  Context 'com.app.notiminimalist.utils.AppManager  Dispatchers 'com.app.notiminimalist.utils.AppManager  	Exception 'com.app.notiminimalist.utils.AppManager  Int 'com.app.notiminimalist.utils.AppManager  List 'com.app.notiminimalist.utils.AppManager  Log 'com.app.notiminimalist.utils.AppManager  PackageManager 'com.app.notiminimalist.utils.AppManager  SchedulePreferences 'com.app.notiminimalist.utils.AppManager  
SilencingMode 'com.app.notiminimalist.utils.AppManager  String 'com.app.notiminimalist.utils.AppManager  TAG 'com.app.notiminimalist.utils.AppManager  all 'com.app.notiminimalist.utils.AppManager  also 'com.app.notiminimalist.utils.AppManager  android 'com.app.notiminimalist.utils.AppManager  com 'com.app.notiminimalist.utils.AppManager  context 'com.app.notiminimalist.utils.AppManager  count 'com.app.notiminimalist.utils.AppManager  	emptyList 'com.app.notiminimalist.utils.AppManager  filter 'com.app.notiminimalist.utils.AppManager  getAppsCountByMode 'com.app.notiminimalist.utils.AppManager  getEnhancedSelectionDescription 'com.app.notiminimalist.utils.AppManager  getInstalledApps 'com.app.notiminimalist.utils.AppManager  getSelectedAppCount 'com.app.notiminimalist.utils.AppManager  	lowercase 'com.app.notiminimalist.utils.AppManager  map 'com.app.notiminimalist.utils.AppManager  
mapNotNull 'com.app.notiminimalist.utils.AppManager  packageManager 'com.app.notiminimalist.utils.AppManager  schedulePreferences 'com.app.notiminimalist.utils.AppManager  setAllAppsSilencingMode 'com.app.notiminimalist.utils.AppManager  setAppSilencingMode 'com.app.notiminimalist.utils.AppManager  setAppsSilencingModeBatch 'com.app.notiminimalist.utils.AppManager  sortedBy 'com.app.notiminimalist.utils.AppManager  toSet 'com.app.notiminimalist.utils.AppManager  withContext 'com.app.notiminimalist.utils.AppManager  AppInfo 1com.app.notiminimalist.utils.AppManager.Companion  ApplicationInfo 1com.app.notiminimalist.utils.AppManager.Companion  Dispatchers 1com.app.notiminimalist.utils.AppManager.Companion  Log 1com.app.notiminimalist.utils.AppManager.Companion  PackageManager 1com.app.notiminimalist.utils.AppManager.Companion  SchedulePreferences 1com.app.notiminimalist.utils.AppManager.Companion  
SilencingMode 1com.app.notiminimalist.utils.AppManager.Companion  TAG 1com.app.notiminimalist.utils.AppManager.Companion  all 1com.app.notiminimalist.utils.AppManager.Companion  also 1com.app.notiminimalist.utils.AppManager.Companion  android 1com.app.notiminimalist.utils.AppManager.Companion  com 1com.app.notiminimalist.utils.AppManager.Companion  count 1com.app.notiminimalist.utils.AppManager.Companion  	emptyList 1com.app.notiminimalist.utils.AppManager.Companion  filter 1com.app.notiminimalist.utils.AppManager.Companion  getInstalledApps 1com.app.notiminimalist.utils.AppManager.Companion  	lowercase 1com.app.notiminimalist.utils.AppManager.Companion  map 1com.app.notiminimalist.utils.AppManager.Companion  
mapNotNull 1com.app.notiminimalist.utils.AppManager.Companion  packageManager 1com.app.notiminimalist.utils.AppManager.Companion  schedulePreferences 1com.app.notiminimalist.utils.AppManager.Companion  setAppsSilencingModeBatch 1com.app.notiminimalist.utils.AppManager.Companion  sortedBy 1com.app.notiminimalist.utils.AppManager.Companion  toSet 1com.app.notiminimalist.utils.AppManager.Companion  withContext 1com.app.notiminimalist.utils.AppManager.Companion  Boolean -com.app.notiminimalist.utils.BatteryOptimizer  Build -com.app.notiminimalist.utils.BatteryOptimizer  Context -com.app.notiminimalist.utils.BatteryOptimizer  	Exception -com.app.notiminimalist.utils.BatteryOptimizer  Intent -com.app.notiminimalist.utils.BatteryOptimizer  List -com.app.notiminimalist.utils.BatteryOptimizer  Log -com.app.notiminimalist.utils.BatteryOptimizer  PowerManager -com.app.notiminimalist.utils.BatteryOptimizer  Settings -com.app.notiminimalist.utils.BatteryOptimizer  String -com.app.notiminimalist.utils.BatteryOptimizer  TAG -com.app.notiminimalist.utils.BatteryOptimizer  Uri -com.app.notiminimalist.utils.BatteryOptimizer  apply -com.app.notiminimalist.utils.BatteryOptimizer  contains -com.app.notiminimalist.utils.BatteryOptimizer  context -com.app.notiminimalist.utils.BatteryOptimizer  getBatteryOptimizationIntent -com.app.notiminimalist.utils.BatteryOptimizer  isDeviceInDozeMode -com.app.notiminimalist.utils.BatteryOptimizer  isIgnoringBatteryOptimizations -com.app.notiminimalist.utils.BatteryOptimizer  let -com.app.notiminimalist.utils.BatteryOptimizer  listOf -com.app.notiminimalist.utils.BatteryOptimizer  	lowercase -com.app.notiminimalist.utils.BatteryOptimizer  
mutableListOf -com.app.notiminimalist.utils.BatteryOptimizer  powerManager -com.app.notiminimalist.utils.BatteryOptimizer  Build 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  Context 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  Intent 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  Log 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  Settings 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  TAG 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  Uri 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  apply 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  contains 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  context 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  let 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  listOf 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  	lowercase 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  
mutableListOf 7com.app.notiminimalist.utils.BatteryOptimizer.Companion  ActivityResultContracts .com.app.notiminimalist.utils.PermissionManager  ActivityResultLauncher .com.app.notiminimalist.utils.PermissionManager  BatteryOptimizer .com.app.notiminimalist.utils.PermissionManager  Boolean .com.app.notiminimalist.utils.PermissionManager  Build .com.app.notiminimalist.utils.PermissionManager  ComponentActivity .com.app.notiminimalist.utils.PermissionManager  
ComponentName .com.app.notiminimalist.utils.PermissionManager  Context .com.app.notiminimalist.utils.PermissionManager  Intent .com.app.notiminimalist.utils.PermissionManager  NotificationManager .com.app.notiminimalist.utils.PermissionManager  Settings .com.app.notiminimalist.utils.PermissionManager  String .com.app.notiminimalist.utils.PermissionManager  Unit .com.app.notiminimalist.utils.PermissionManager  Uri .com.app.notiminimalist.utils.PermissionManager  activity .com.app.notiminimalist.utils.PermissionManager  android .com.app.notiminimalist.utils.PermissionManager  apply .com.app.notiminimalist.utils.PermissionManager  batteryOptimizer .com.app.notiminimalist.utils.PermissionManager  com .com.app.notiminimalist.utils.PermissionManager  contains .com.app.notiminimalist.utils.PermissionManager  dndPermissionLauncher .com.app.notiminimalist.utils.PermissionManager  dropLast .com.app.notiminimalist.utils.PermissionManager  exactAlarmPermissionLauncher .com.app.notiminimalist.utils.PermissionManager   getMissingPermissionsDescription .com.app.notiminimalist.utils.PermissionManager  hasAllRequiredPermissions .com.app.notiminimalist.utils.PermissionManager  hasDndPermission .com.app.notiminimalist.utils.PermissionManager  hasExactAlarmPermission .com.app.notiminimalist.utils.PermissionManager  !hasNotificationListenerPermission .com.app.notiminimalist.utils.PermissionManager  
initialize .com.app.notiminimalist.utils.PermissionManager  java .com.app.notiminimalist.utils.PermissionManager  joinToString .com.app.notiminimalist.utils.PermissionManager  last .com.app.notiminimalist.utils.PermissionManager  
mutableListOf .com.app.notiminimalist.utils.PermissionManager  &notificationListenerPermissionLauncher .com.app.notiminimalist.utils.PermissionManager  requestAllMissingPermissions .com.app.notiminimalist.utils.PermissionManager  requestDndPermission .com.app.notiminimalist.utils.PermissionManager  requestExactAlarmPermission .com.app.notiminimalist.utils.PermissionManager  %requestNotificationListenerPermission .com.app.notiminimalist.utils.PermissionManager  ActivityResultContracts 8com.app.notiminimalist.utils.PermissionManager.Companion  BatteryOptimizer 8com.app.notiminimalist.utils.PermissionManager.Companion  Build 8com.app.notiminimalist.utils.PermissionManager.Companion  
ComponentName 8com.app.notiminimalist.utils.PermissionManager.Companion  Context 8com.app.notiminimalist.utils.PermissionManager.Companion  Intent 8com.app.notiminimalist.utils.PermissionManager.Companion  Settings 8com.app.notiminimalist.utils.PermissionManager.Companion  Uri 8com.app.notiminimalist.utils.PermissionManager.Companion  activity 8com.app.notiminimalist.utils.PermissionManager.Companion  apply 8com.app.notiminimalist.utils.PermissionManager.Companion  com 8com.app.notiminimalist.utils.PermissionManager.Companion  contains 8com.app.notiminimalist.utils.PermissionManager.Companion  dropLast 8com.app.notiminimalist.utils.PermissionManager.Companion  java 8com.app.notiminimalist.utils.PermissionManager.Companion  joinToString 8com.app.notiminimalist.utils.PermissionManager.Companion  last 8com.app.notiminimalist.utils.PermissionManager.Companion  
mutableListOf 8com.app.notiminimalist.utils.PermissionManager.Companion  app 6com.app.notiminimalist.utils.PermissionManager.android  AlarmManager :com.app.notiminimalist.utils.PermissionManager.android.app  app $com.app.notiminimalist.utils.android  AlarmManager (com.app.notiminimalist.utils.android.app  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  Runnable 	java.lang  SecurityException 	java.lang  
StringBuilder 	java.lang  UnsupportedOperationException 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	LocalDate 	java.time  
LocalDateTime 	java.time  	LocalTime 	java.time  ZoneId 	java.time  toEpochMilli java.time.Instant  atTime java.time.LocalDate  
dayOfMonth java.time.LocalDate  
monthValue java.time.LocalDate  now java.time.LocalDate  of java.time.LocalDate  year java.time.LocalDate  atZone java.time.LocalDateTime  isBefore java.time.LocalDateTime  now java.time.LocalDateTime  plusDays java.time.LocalDateTime  toLocalDate java.time.LocalDateTime  format java.time.LocalTime  hour java.time.LocalTime  isBefore java.time.LocalTime  minute java.time.LocalTime  now java.time.LocalTime  of java.time.LocalTime  
systemDefault java.time.ZoneId  	toInstant java.time.ZonedDateTime  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  ACTION_END_SILENCING 	java.util  ACTION_START_SILENCING 	java.util  AlarmManager 	java.util  Boolean 	java.util  Build 	java.util  Calendar 	java.util  Context 	java.util  Date 	java.util  END_ALARM_REQUEST_CODE 	java.util  	Exception 	java.util  Int 	java.util  Intent 	java.util  
LocalDateTime 	java.util  	LocalTime 	java.util  Log 	java.util  Long 	java.util  
PendingIntent 	java.util  START_ALARM_REQUEST_CODE 	java.util  SchedulePreferences 	java.util  SecurityException 	java.util  SilencingReceiver 	java.util  String 	java.util  TAG 	java.util  ZoneId 	java.util  android 	java.util  apply 	java.util  java 	java.util  minOf 	java.util  getInstance java.util.Calendar  timeInMillis java.util.Calendar  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  	Function4 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Result kotlin  
ShortArray kotlin  String kotlin  Suppress kotlin  Triple kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  let kotlin  map kotlin  plus kotlin  run kotlin  toString 
kotlin.Any  invoke kotlin.Boolean  let kotlin.Boolean  not kotlin.Boolean  toString kotlin.CharSequence  dp 
kotlin.Double  sp 
kotlin.Double  toInt 
kotlin.Double  
unaryMinus 
kotlin.Double  	compareTo kotlin.Float  div kotlin.Float  let kotlin.Float  rangeTo kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  let kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  and 
kotlin.Int  	compareTo 
kotlin.Int  dec 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  	uppercase 
kotlin.String  format kotlin.String.Companion  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  all kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  dropLast kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  emptySet kotlin.collections  filter kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  groupBy kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  last kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  set kotlin.collections  sortedBy kotlin.collections  take kotlin.collections  toMutableSet kotlin.collections  toSet kotlin.collections  all kotlin.collections.List  also kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sortedBy kotlin.collections.List  toSet kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  dropLast kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  last kotlin.collections.MutableList  size kotlin.collections.MutableList  set kotlin.collections.MutableMap  add kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  contains kotlin.collections.Set  isEmpty kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  plus kotlin.collections.Set  size kotlin.collections.Set  toMutableSet kotlin.collections.Set  maxOf kotlin.comparisons  minOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  
startsWith 	kotlin.io  java 
kotlin.jvm  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  contains 
kotlin.ranges  last 
kotlin.ranges  rangeTo 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  groupBy kotlin.sequences  joinToString kotlin.sequences  last kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  plus kotlin.sequences  sortedBy kotlin.sequences  take kotlin.sequences  toMutableSet kotlin.sequences  toSet kotlin.sequences  all kotlin.text  buildString kotlin.text  contains kotlin.text  count kotlin.text  dropLast kotlin.text  filter kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  groupBy kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  last kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapNotNull kotlin.text  maxOf kotlin.text  minOf kotlin.text  plus kotlin.text  removePrefix kotlin.text  set kotlin.text  
startsWith kotlin.text  take kotlin.text  toSet kotlin.text  	uppercase kotlin.text  AlarmScheduler kotlinx.coroutines  BroadcastReceiver kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  
DndManager kotlinx.coroutines  	Exception kotlinx.coroutines  Intent kotlinx.coroutines  Job kotlinx.coroutines  Log kotlinx.coroutines  SchedulePreferences kotlinx.coroutines  TAG kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  AppInfo !kotlinx.coroutines.CoroutineScope  ApplicationInfo !kotlinx.coroutines.CoroutineScope  Build !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  PackageManager !kotlinx.coroutines.CoroutineScope  PermissionStates !kotlinx.coroutines.CoroutineScope  
SilencingMode !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  WindowCompat !kotlinx.coroutines.CoroutineScope  also !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  count !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  getInstalledApps !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  
isNotBlank !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  	lowercase !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  
mapNotNull !kotlinx.coroutines.CoroutineScope  packageManager !kotlinx.coroutines.CoroutineScope  schedulePreferences !kotlinx.coroutines.CoroutineScope  setAppsSilencingModeBatch !kotlinx.coroutines.CoroutineScope  sortedBy !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       