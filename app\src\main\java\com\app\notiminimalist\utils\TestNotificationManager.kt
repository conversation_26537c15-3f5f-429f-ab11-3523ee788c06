package com.app.notiminimalist.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.app.notiminimalist.MainActivity
import com.app.notiminimalist.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Manages test notifications for users to verify app functionality.
 */
class TestNotificationManager(private val context: Context) {
    
    companion object {
        private const val CHANNEL_ID = "notiminimalist_test_channel"
        private const val CHANNEL_NAME = "Test Notifications"
        private const val CHANNEL_DESCRIPTION = "Test notifications to verify silencing functionality"
        private const val NOTIFICATION_ID = 12345
    }
    
    init {
        createNotificationChannel()
    }
    
    /**
     * Create notification channel for test notifications (API 26+).
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableVibration(true)
                enableLights(true)
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * Send a test notification to verify app functionality.
     */
    fun sendTestNotification() {
        val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        val currentTime = timeFormat.format(Date())
        
        // Create intent to open the app when notification is tapped
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or 
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        // Build the notification
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // You'll need to add this icon
            .setContentTitle("Notiminimalist Test")
            .setContentText("Test notification sent at $currentTime")
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("This is a test notification from Notiminimalist. If you have set this app to 'SILENCED' mode and are currently in silencing hours, this notification should be automatically dismissed. Time: $currentTime")
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .build()
        
        // Send the notification
        try {
            NotificationManagerCompat.from(context).notify(NOTIFICATION_ID, notification)
        } catch (e: SecurityException) {
            // Handle case where notification permission is not granted (API 33+)
            throw SecurityException("Notification permission not granted. Please enable notifications for this app in system settings.")
        }
    }
    
    /**
     * Check if notifications are enabled for this app.
     */
    fun areNotificationsEnabled(): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
    
    /**
     * Get a description of what the test notification will do.
     */
    fun getTestDescription(): String {
        return """
            This will send a test notification from Notiminimalist.
            
            To test the silencing functionality:
            1. Set Notiminimalist to 'SILENCED' mode in the Apps tab
            2. Make sure you're currently in silencing hours
            3. Send this test notification
            4. If working correctly, the notification should be automatically dismissed
            
            If Notiminimalist is set to 'ALWAYS_ALLOWED' or you're outside silencing hours, the notification will appear normally.
        """.trimIndent()
    }
}
