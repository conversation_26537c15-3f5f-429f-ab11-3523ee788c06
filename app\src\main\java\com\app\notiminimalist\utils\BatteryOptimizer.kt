package com.app.notiminimalist.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log

/**
 * Utility class for battery optimization and power management.
 * Helps ensure the app can function reliably while minimizing battery usage.
 */
class BatteryOptimizer(private val context: Context) {
    
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
    
    companion object {
        private const val TAG = "BatteryOptimizer"
    }
    
    /**
     * Check if the app is whitelisted from battery optimization.
     * This is important for reliable alarm scheduling.
     */
    fun isIgnoringBatteryOptimizations(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true // No battery optimization on older versions
        }
    }
    
    /**
     * Get an intent to request battery optimization whitelist.
     */
    fun getBatteryOptimizationIntent(): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:${context.packageName}")
            }
        } else {
            null
        }
    }
    
    /**
     * Automatically request battery optimization whitelist if not already granted.
     * Returns true if request was initiated, false if already whitelisted or not supported.
     */
    fun requestBatteryOptimizationWhitelist(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !isIgnoringBatteryOptimizations()) {
            try {
                val intent = getBatteryOptimizationIntent()
                intent?.let {
                    it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(it)
                    Log.i(TAG, "Requesting battery optimization whitelist")
                    true
                } ?: false
            } catch (e: Exception) {
                Log.e(TAG, "Failed to request battery optimization whitelist", e)
                false
            }
        } else {
            false
        }
    }
    
    /**
     * Check if device is in doze mode.
     */
    fun isDeviceInDozeMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isDeviceIdleMode
        } else {
            false
        }
    }
    
    /**
     * Get battery optimization recommendations.
     */
    fun getBatteryOptimizationRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (!isIgnoringBatteryOptimizations()) {
            recommendations.add("Whitelist app from battery optimization for reliable scheduling")
        }
        
        recommendations.addAll(listOf(
            "App uses minimal background processing",
            "Alarms are scheduled efficiently using AlarmManager",
            "No unnecessary wake locks or background services",
            "Settings are stored locally without network requests",
            "UI is optimized for smooth performance"
        ))
        
        return recommendations
    }
    
    /**
     * Check if the device manufacturer has aggressive battery optimization.
     * Some manufacturers like Xiaomi, Huawei, OnePlus have aggressive power management.
     */
    fun hasAggressiveBatteryOptimization(): Boolean {
        val manufacturer = Build.MANUFACTURER.lowercase()
        return manufacturer in listOf(
            "xiaomi", "huawei", "oneplus", "oppo", "vivo", 
            "samsung", "asus", "letv", "honor"
        )
    }
    
    /**
     * Get manufacturer-specific battery optimization guidance.
     */
    fun getManufacturerSpecificGuidance(): String? {
        val manufacturer = Build.MANUFACTURER.lowercase()
        return when {
            manufacturer.contains("xiaomi") -> 
                "For MIUI: Go to Security > Battery > Manage apps' battery usage > Choose apps > Select this app > No restrictions"
            manufacturer.contains("huawei") -> 
                "For EMUI: Go to Battery > App launch > Find this app > Enable 'Manage manually' and turn on Auto-launch, Secondary launch, and Run in background"
            manufacturer.contains("oneplus") -> 
                "For OnePlus: Go to Battery > Battery optimization > All apps > Find this app > Don't optimize"
            manufacturer.contains("oppo") -> 
                "For ColorOS: Go to Battery > Power saving mode > App freeze > Find this app > Turn off"
            manufacturer.contains("samsung") -> 
                "For Samsung: Go to Device care > Battery > More battery settings > Optimize settings > Turn off 'Put unused apps to sleep'"
            else -> null
        }
    }
    
    /**
     * Log current power state for debugging.
     */
    fun logPowerState() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Log.d(TAG, "Battery optimization ignored: ${isIgnoringBatteryOptimizations()}")
            Log.d(TAG, "Device in doze mode: ${isDeviceInDozeMode()}")
            Log.d(TAG, "Power save mode: ${powerManager.isPowerSaveMode}")
        }
    }
}
