<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.app.notiminimalist.AppFilteringTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-09-18T18:03:31.728Z" hostname="DESKTOP-TN8UKJ9" time="0.081">
  <properties/>
  <testcase name="testUserAppsAreAlwaysShown" classname="com.app.notiminimalist.AppFilteringTest" time="0.016"/>
  <testcase name="testSettingsAppsAreHidden" classname="com.app.notiminimalist.AppFilteringTest" time="0.059"/>
  <testcase name="testPopularAppsAreShown" classname="com.app.notiminimalist.AppFilteringTest" time="0.001"/>
  <testcase name="testCriticalSystemAppsAreHidden" classname="com.app.notiminimalist.AppFilteringTest" time="0.001"/>
  <testcase name="testManufacturerAppsAreShown" classname="com.app.notiminimalist.AppFilteringTest" time="0.001"/>
  <testcase name="testSystemAppsWithSettingsInNameAreHidden" classname="com.app.notiminimalist.AppFilteringTest" time="0.001"/>
  <testcase name="testNotiminimalistAppIsHidden" classname="com.app.notiminimalist.AppFilteringTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
