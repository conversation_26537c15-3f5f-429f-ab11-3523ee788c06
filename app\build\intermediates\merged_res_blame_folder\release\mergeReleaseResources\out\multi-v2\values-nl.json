{"logs": [{"outputFile": "com.app.notiminimalist.app-mergeReleaseResources-41:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\18b22e515b4201f34167a2d64b698d76\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,990,1073,1150,1225,1297,1368,1452,1522", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,985,1068,1145,1220,1292,1363,1447,1517,1637"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,931,1014,1111,1210,1295,1371,7753,7840,7929,8010,8093,8170,8245,8317,8489,8573,8643", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,74,71,70,83,69,119", "endOffsets": "926,1009,1106,1205,1290,1366,1462,7835,7924,8005,8088,8165,8240,8312,8383,8568,8638,8758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a87ea0a2ae3b302fa8b2e67fc66b56aa\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8763,8849", "endColumns": "85,85", "endOffsets": "8844,8930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac5730e1294228dab72f8d7b7acde09d\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1589,1706,1827,1945,2045,2143,2258,2410,2531,2673,2758,2857,2953,3056,3174,3295,3399,3530,3658,3794,3972,4103,4223,4344,4479,4576,4676,4796,4925,5025,5132,5235,5372,5512,5618,5722,5806,5906,6003,6114,6201,6288,6393,6473,6556,6655,6759,6854,6953,7041,7151,7252,7357,7477,7557,7658", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "1584,1701,1822,1940,2040,2138,2253,2405,2526,2668,2753,2852,2948,3051,3169,3290,3394,3525,3653,3789,3967,4098,4218,4339,4474,4571,4671,4791,4920,5020,5127,5230,5367,5507,5613,5717,5801,5901,5998,6109,6196,6283,6388,6468,6551,6650,6754,6849,6948,7036,7146,7247,7352,7472,7552,7653,7748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\38ea0471cd817173b4203329613a319d\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,409,509,616,720,8388", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "202,304,404,504,611,715,834,8484"}}]}]}