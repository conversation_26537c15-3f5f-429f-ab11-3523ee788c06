  ExampleUnitTest com.app.notiminimalist  Test com.app.notiminimalist  assertEquals com.app.notiminimalist  assertEquals &com.app.notiminimalist.ExampleUnitTest  plus 
kotlin.Int  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  AppFilteringTest com.app.notiminimalist  AppInfo com.app.notiminimalist  assertFalse com.app.notiminimalist  
assertTrue com.app.notiminimalist  contains com.app.notiminimalist  forEach com.app.notiminimalist  listOf com.app.notiminimalist  	lowercase com.app.notiminimalist  to com.app.notiminimalist  AppInfo 'com.app.notiminimalist.AppFilteringTest  assertFalse 'com.app.notiminimalist.AppFilteringTest  
assertTrue 'com.app.notiminimalist.AppFilteringTest  contains 'com.app.notiminimalist.AppFilteringTest  listOf 'com.app.notiminimalist.AppFilteringTest  	lowercase 'com.app.notiminimalist.AppFilteringTest  to 'com.app.notiminimalist.AppFilteringTest  AppInfo com.app.notiminimalist.data  
SilencingMode com.app.notiminimalist.data  
shouldShow #com.app.notiminimalist.data.AppInfo  	Function1 kotlin  Pair kotlin  to kotlin  not kotlin.Boolean  
component1 kotlin.Pair  
component2 kotlin.Pair  contains 
kotlin.String  	lowercase 
kotlin.String  to 
kotlin.String  List kotlin.collections  contains kotlin.collections  forEach kotlin.collections  listOf kotlin.collections  contains 
kotlin.ranges  contains kotlin.sequences  forEach kotlin.sequences  contains kotlin.text  forEach kotlin.text  	lowercase kotlin.text  AppInfo 	org.junit  assertFalse 	org.junit  
assertTrue 	org.junit  contains 	org.junit  forEach 	org.junit  listOf 	org.junit  	lowercase 	org.junit  to 	org.junit  assertFalse org.junit.Assert  
assertTrue org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             