<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 91 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Wed Sep 03 12:46:09 PDT 2025 by AGP (8.12.2)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (1)</a>
      <a class="mdl-navigation__link" href="#InlinedApi"><i class="material-icons warning-icon">warning</i>Using inlined constants on older versions (17)</a>
      <a class="mdl-navigation__link" href="#BatteryLife"><i class="material-icons warning-icon">warning</i>Battery Life Issues (1)</a>
      <a class="mdl-navigation__link" href="#QueryPermissionsNeeded"><i class="material-icons warning-icon">warning</i>Using APIs affected by query permissions (1)</a>
      <a class="mdl-navigation__link" href="#RedundantLabel"><i class="material-icons warning-icon">warning</i>Redundant label on activity (1)</a>
      <a class="mdl-navigation__link" href="#AndroidGradlePluginVersion"><i class="material-icons warning-icon">warning</i>Obsolete Android Gradle Plugin Version (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (8)</a>
      <a class="mdl-navigation__link" href="#NewerVersionAvailable"><i class="material-icons warning-icon">warning</i>Newer Library Versions Available (2)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (24)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (8)</a>
      <a class="mdl-navigation__link" href="#MonochromeLauncherIcon"><i class="material-icons warning-icon">warning</i>Monochrome icon is not defined (2)</a>
      <a class="mdl-navigation__link" href="#IconLocation"><i class="material-icons warning-icon">warning</i>Image defined in density-independent drawable folder (1)</a>
      <a class="mdl-navigation__link" href="#UseKtx"><i class="material-icons warning-icon">warning</i>Use KTX extension function (22)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (2)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">17</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InlinedApi">InlinedApi</a>: Using inlined constants on older versions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#BatteryLife">BatteryLife</a>: Battery Life Issues</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#QueryPermissionsNeeded">QueryPermissionsNeeded</a>: Using APIs affected by query permissions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantLabel">RedundantLabel</a>: Redundant label on activity</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AndroidGradlePluginVersion">AndroidGradlePluginVersion</a>: Obsolete Android Gradle Plugin Version</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#NewerVersionAvailable">NewerVersionAvailable</a>: Newer Library Versions Available</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">24</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#MonochromeLauncherIcon">MonochromeLauncherIcon</a>: Monochrome icon is not defined</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLocation">IconLocation</a>: Image defined in density-independent drawable folder</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">22</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseKtx">UseKtx</a>: Use KTX extension function</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (52)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (42)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:218</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 215 </span><span class="javadoc">   */</span>
<span class="lineno"> 216 </span>  <span class="keyword">fun</span> getFormattedTime(time: LocalTime): String {
<span class="lineno"> 217 </span>      <span class="keyword">val</span> hour = <span class="keyword">if</span> (time.hour == <span class="number">0</span>) <span class="number">12</span> <span class="keyword">else</span> <span class="keyword">if</span> (time.hour > <span class="number">12</span>) time.hour - <span class="number">12</span> <span class="keyword">else</span> time.hour
<span class="caretline"><span class="lineno"> 218 </span>      <span class="keyword">val</span> minute = <span class="warning">String.format(<span class="string">"%02d"</span>, time.minute)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 219 </span>      <span class="keyword">val</span> amPm = <span class="keyword">if</span> (time.hour &lt; <span class="number">12</span>) <span class="string">"AM"</span> <span class="keyword">else</span> <span class="string">"PM"</span>
<span class="lineno"> 220 </span>      <span class="keyword">return</span> <span class="string">"$hour:$minute $amPm"</span>
<span class="lineno"> 221 </span>  }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InlinedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InlinedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using inlined constants on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:173</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS</code></span><br /><pre class="errorlines">
<span class="lineno"> 170 </span>         
<span class="lineno"> 171 </span>         <span class="comment">// Create a permissive policy that allows app notifications through</span>
<span class="lineno"> 172 </span>         <span class="comment">// The actual silencing will be handled by notification channels</span>
<span class="caretline"><span class="lineno"> 173 </span>         <span class="keyword">val</span> policyCategories = <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 174 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
<span class="lineno"> 175 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_CALLS or
<span class="lineno"> 176 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_MESSAGES or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:174</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM</code></span><br /><pre class="errorlines">
<span class="lineno"> 171 </span>         <span class="comment">// Create a permissive policy that allows app notifications through</span>
<span class="lineno"> 172 </span>         <span class="comment">// The actual silencing will be handled by notification channels</span>
<span class="lineno"> 173 </span>         <span class="keyword">val</span> policyCategories = NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
<span class="caretline"><span class="lineno"> 174 </span>                              <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 175 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_CALLS or
<span class="lineno"> 176 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_MESSAGES or
<span class="lineno"> 177 </span>                              NotificationManager.Policy.PRIORITY_CATEGORY_EVENTS or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:187</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT</code></span><br /><pre class="errorlines">
<span class="lineno"> 184 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE
<span class="lineno"> 185 </span>            } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="lineno"> 186 </span>                <span class="comment">// For API 26-27, minimal suppression</span>
<span class="caretline"><span class="lineno"> 187 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 188 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE
<span class="lineno"> 189 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 190 </span>                <span class="number">0</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:188</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE</code></span><br /><pre class="errorlines">
<span class="lineno"> 185 </span>            } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="lineno"> 186 </span>                <span class="comment">// For API 26-27, minimal suppression</span>
<span class="lineno"> 187 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
<span class="caretline"><span class="lineno"> 188 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 189 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 190 </span>                <span class="number">0</span>
<span class="lineno"> 191 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:225</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS</code></span><br /><pre class="errorlines">
<span class="lineno"> 222 </span>          <span class="keyword">val</span> policyCategories = <span class="keyword">when</span> {
<span class="lineno"> 223 </span>              <span class="comment">// Always allow critical system functions</span>
<span class="lineno"> 224 </span>              alwaysAllowedApps.isNotEmpty() -> {
<span class="caretline"><span class="lineno"> 225 </span>                  <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 226 </span>                  NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
<span class="lineno"> 227 </span>                  NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 228 </span>                  <span class="comment">// Note: Removed MESSAGES to block most app notifications and sounds</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="InlinedApiDivLink" onclick="reveal('InlinedApiDiv');" />+ 12 More Occurrences...</button>
<div id="InlinedApiDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:226</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM</code></span><br /><pre class="errorlines">
<span class="lineno"> 223 </span>          <span class="comment">// Always allow critical system functions</span>
<span class="lineno"> 224 </span>          alwaysAllowedApps.isNotEmpty() -> {
<span class="lineno"> 225 </span>              NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
<span class="caretline"><span class="lineno"> 226 </span>              <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 227 </span>              NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 228 </span>              <span class="comment">// Note: Removed MESSAGES to block most app notifications and sounds</span>
<span class="lineno"> 229 </span>          }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:232</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS</code></span><br /><pre class="errorlines">
<span class="lineno"> 229 </span>                }
<span class="lineno"> 230 </span>                <span class="comment">// If we only have silenced apps, be very restrictive</span>
<span class="lineno"> 231 </span>                silencedApps.isNotEmpty() -> {
<span class="caretline"><span class="lineno"> 232 </span>                    <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 233 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
<span class="lineno"> 234 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 235 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:233</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM</code></span><br /><pre class="errorlines">
<span class="lineno"> 230 </span>                <span class="comment">// If we only have silenced apps, be very restrictive</span>
<span class="lineno"> 231 </span>                silencedApps.isNotEmpty() -> {
<span class="lineno"> 232 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
<span class="caretline"><span class="lineno"> 233 </span>                    <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 234 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 235 </span>                }
<span class="lineno"> 236 </span>                <span class="comment">// If no apps are configured, use default restrictive policy</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:238</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS</code></span><br /><pre class="errorlines">
<span class="lineno"> 235 </span>                }
<span class="lineno"> 236 </span>                <span class="comment">// If no apps are configured, use default restrictive policy</span>
<span class="lineno"> 237 </span>                <span class="keyword">else</span> -> {
<span class="caretline"><span class="lineno"> 238 </span>                    <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 239 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
<span class="lineno"> 240 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 241 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:239</span>: <span class="message">Field requires API level 28 (current min is 23): <code>android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM</code></span><br /><pre class="errorlines">
<span class="lineno"> 236 </span>                <span class="comment">// If no apps are configured, use default restrictive policy</span>
<span class="lineno"> 237 </span>                <span class="keyword">else</span> -> {
<span class="lineno"> 238 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
<span class="caretline"><span class="lineno"> 239 </span>                    <span class="warning">NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 240 </span>                    NotificationManager.Policy.PRIORITY_CATEGORY_CALLS
<span class="lineno"> 241 </span>                }
<span class="lineno"> 242 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:256</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT</code></span><br /><pre class="errorlines">
<span class="lineno"> 253 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
<span class="lineno"> 254 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
<span class="lineno"> 255 </span>            } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="caretline"><span class="lineno"> 256 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 257 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
<span class="lineno"> 258 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
<span class="lineno"> 259 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:257</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE</code></span><br /><pre class="errorlines">
<span class="lineno"> 254 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
<span class="lineno"> 255 </span>            } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="lineno"> 256 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
<span class="caretline"><span class="lineno"> 257 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 258 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
<span class="lineno"> 259 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
<span class="lineno"> 260 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:258</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_FULL_SCREEN_INTENT</code></span><br /><pre class="errorlines">
<span class="lineno"> 255 </span>            } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="lineno"> 256 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
<span class="lineno"> 257 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
<span class="caretline"><span class="lineno"> 258 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 259 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
<span class="lineno"> 260 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
<span class="lineno"> 261 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:259</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_LIGHTS</code></span><br /><pre class="errorlines">
<span class="lineno"> 256 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
<span class="lineno"> 257 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
<span class="lineno"> 258 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
<span class="caretline"><span class="lineno"> 259 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 260 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
<span class="lineno"> 261 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
<span class="lineno"> 262 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:260</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_NOTIFICATION_LIST</code></span><br /><pre class="errorlines">
<span class="lineno"> 257 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
<span class="lineno"> 258 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
<span class="lineno"> 259 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
<span class="caretline"><span class="lineno"> 260 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 261 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
<span class="lineno"> 262 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
<span class="lineno"> 263 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:261</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_PEEK</code></span><br /><pre class="errorlines">
<span class="lineno"> 258 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
<span class="lineno"> 259 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
<span class="lineno"> 260 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
<span class="caretline"><span class="lineno"> 261 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK</span> or&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 262 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
<span class="lineno"> 263 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
<span class="lineno"> 264 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:264</span>: <span class="message">Field requires API level 28 (current min is 26): <code>android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_STATUS_BAR</code></span><br /><pre class="errorlines">
<span class="lineno"> 261 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
<span class="lineno"> 262 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_OFF or
<span class="lineno"> 263 </span>                NotificationManager.Policy.SUPPRESSED_EFFECT_SCREEN_ON or
<span class="caretline"><span class="lineno"> 264 </span>                <span class="warning">NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 265 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 266 </span>                <span class="number">0</span>
<span class="lineno"> 267 </span>            }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationInlinedApi" style="display: none;">
This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that's fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it's safe and can be suppressed or whether the code needs to be guarded.<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InlinedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InlinedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInlinedApiLink" onclick="reveal('explanationInlinedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InlinedApiCardLink" onclick="hideid('InlinedApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="BatteryLife"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="BatteryLifeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Battery Life Issues</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:40</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  37 </span><span class="javadoc">     */</span>
<span class="lineno">  38 </span>    <span class="keyword">fun</span> getBatteryOptimizationIntent(): Intent? {
<span class="lineno">  39 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="caretline"><span class="lineno">  40 </span>            Intent(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>).apply {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  41 </span>                data = Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)
<span class="lineno">  42 </span>            }
<span class="lineno">  43 </span>        } <span class="keyword">else</span> {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationBatteryLife" style="display: none;">
This issue flags code that either<br/>
* negatively affects battery life, or<br/>
* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.<br/>
<br/>
Generally, you should be using <code>WorkManager</code> instead.<br/>
<br/>
For more details on how to update your code, please see <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a>
</div>To suppress this error, use the issue id "BatteryLife" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">BatteryLife</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationBatteryLifeLink" onclick="reveal('explanationBatteryLife');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="BatteryLifeCardLink" onclick="hideid('BatteryLifeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="QueryPermissionsNeeded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="QueryPermissionsNeededCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using APIs affected by query permissions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/AppManager.kt">../../src/main/java/com/app/notiminimalist/utils/AppManager.kt</a>:37</span>: <span class="message">As of Android 11, this method no longer returns information about all apps; see <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a> for details</span><br /><pre class="errorlines">
<span class="lineno">  34 </span>  suspend <span class="keyword">fun</span> getInstalledApps(): List&lt;AppInfo> = withContext(Dispatchers.IO) {
<span class="lineno">  35 </span>      <span class="keyword">try</span> {
<span class="lineno">  36 </span>          <span class="comment">// Get ALL installed applications, not just those with launcher activities</span>
<span class="caretline"><span class="lineno">  37 </span>          <span class="keyword">val</span> installedPackages = packageManager.<span class="warning">getInstalledApplications</span>(PackageManager.GET_META_DATA)</span>
<span class="lineno">  38 </span>          <span class="keyword">val</span> selectedApps = schedulePreferences.selectedApps
<span class="lineno">  39 </span>          
<span class="lineno">  40 </span>          <span class="keyword">val</span> appList = installedPackages.mapNotNull { applicationInfo ->
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationQueryPermissionsNeeded" style="display: none;">
Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a <code>&lt;queries></code> declaration in your manifest.<br/>
<br/>
As a corollary, the methods <code>PackageManager#getInstalledPackages</code> and <code>PackageManager#getInstalledApplications</code> will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like <code>PackageManager#getPackageInfo</code> or <code>PackageManager#queryIntentActivities</code>.<br/><div class="moreinfo">More info: <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a>
</div>To suppress this error, use the issue id "QueryPermissionsNeeded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">QueryPermissionsNeeded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationQueryPermissionsNeededLink" onclick="reveal('explanationQueryPermissionsNeeded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="QueryPermissionsNeededCardLink" onclick="hideid('QueryPermissionsNeededCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantLabel"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantLabelCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant label on activity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:22</span>: <span class="message">Redundant label can be removed</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="lineno"> 20 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".MainActivity"</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 22 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.Notiminimalist"</span>>
<span class="lineno"> 24 </span>            <span class="tag">&lt;intent-filter></span>
<span class="lineno"> 25 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.MAIN"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantLabel" style="display: none;">
When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted.<br/>To suppress this error, use the issue id "RedundantLabel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantLabel</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantLabelLink" onclick="reveal('explanationRedundantLabel');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantLabelCardLink" onclick="hideid('RedundantLabelCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AndroidGradlePluginVersion"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AndroidGradlePluginVersionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Android Gradle Plugin Version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.12.2 is available: 8.13.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.12.2"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAndroidGradlePluginVersion" style="display: none;">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AndroidGradlePluginVersion</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAndroidGradlePluginVersionLink" onclick="reveal('explanationAndroidGradlePluginVersion');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AndroidGradlePluginVersionCardLink" onclick="hideid('AndroidGradlePluginVersionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:56</span>: <span class="message">A newer version of androidx.compose.material:material-icons-extended than 1.5.4 is available: 1.7.8</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>    implementation(libs.androidx.material3)
<span class="lineno"> 54 </span>    
<span class="lineno"> 55 </span>    // Material Icons Extended - provides all Material Design icons
<span class="caretline"><span class="lineno"> 56 </span>    implementation(<span class="warning">"androidx.compose.material:material-icons-extended:1.5.4"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>    
<span class="lineno"> 58 </span>    // Core library desugaring for Java 8+ time APIs
<span class="lineno"> 59 </span>    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:59</span>: <span class="message">A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    implementation("androidx.compose.material:material-icons-extended:1.5.4")
<span class="lineno"> 57 </span>    
<span class="lineno"> 58 </span>    // Core library desugaring for Java 8+ time APIs
<span class="caretline"><span class="lineno"> 59 </span>    coreLibraryDesugaring(<span class="warning">"com.android.tools:desugar_jdk_libs:2.0.4"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>    
<span class="lineno"> 61 </span>    testImplementation(libs.junit)
<span class="lineno"> 62 </span>    androidTestImplementation(libs.androidx.junit)
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:4</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.12.2"
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="caretline"><span class="lineno">  4 </span>coreKtx = <span class="warning">"1.10.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>activityCompose = "1.8.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>activityCompose = "1.8.0"
<span class="lineno"> 10 </span>composeBom = "2024.09.00"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.3</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>lifecycleRuntimeKtx = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>activityCompose = "1.8.0"
<span class="lineno"> 10 </span>composeBom = "2024.09.00"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="caretline"><span class="lineno">  9 </span>activityCompose = <span class="warning">"1.8.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>composeBom = "2024.09.00"
<span class="lineno"> 11 </span>
<span class="lineno"> 12 </span>[libraries]
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.08.01</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>activityCompose = "1.8.0"
<span class="caretline"><span class="lineno"> 10 </span>composeBom = <span class="warning">"2024.09.00"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>
<span class="lineno"> 12 </span>[libraries]
<span class="lineno"> 13 </span>androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NewerVersionAvailable"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NewerVersionAvailableCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Newer Library Versions Available</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:3</span>: <span class="message">A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.12.2"
<span class="caretline"><span class="lineno">  3 </span>kotlin = <span class="warning">"2.0.21"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:3</span>: <span class="message">A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.12.2"
<span class="caretline"><span class="lineno">  3 </span>kotlin = <span class="warning">"2.0.21"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNewerVersionAvailable" style="display: none;">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewerVersionAvailable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NewerVersionAvailable</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNewerVersionAvailableLink" onclick="reveal('explanationNewerVersionAvailable');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NewerVersionAvailableCardLink" onclick="hideid('NewerVersionAvailableCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:96</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  93 </span>      START_ALARM_REQUEST_CODE,
<span class="lineno">  94 </span>      intent,
<span class="lineno">  95 </span>      PendingIntent.FLAG_UPDATE_CURRENT or 
<span class="caretline"><span class="lineno">  96 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) PendingIntent.FLAG_IMMUTABLE <span class="keyword">else</span> <span class="number">0</span>&nbsp;</span>
<span class="lineno">  97 </span>  )
<span class="lineno">  98 </span>  
<span class="lineno">  99 </span>  scheduleExactAlarm(triggerTime, pendingIntent)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:119</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>      END_ALARM_REQUEST_CODE,
<span class="lineno"> 117 </span>      intent,
<span class="lineno"> 118 </span>      PendingIntent.FLAG_UPDATE_CURRENT or 
<span class="caretline"><span class="lineno"> 119 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) PendingIntent.FLAG_IMMUTABLE <span class="keyword">else</span> <span class="number">0</span>&nbsp;</span>
<span class="lineno"> 120 </span>  )
<span class="lineno"> 121 </span>  
<span class="lineno"> 122 </span>  scheduleExactAlarm(triggerTime, pendingIntent)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:139</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 136 </span>      START_ALARM_REQUEST_CODE,
<span class="lineno"> 137 </span>      intent,
<span class="lineno"> 138 </span>      PendingIntent.FLAG_UPDATE_CURRENT or 
<span class="caretline"><span class="lineno"> 139 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) PendingIntent.FLAG_IMMUTABLE <span class="keyword">else</span> <span class="number">0</span>&nbsp;</span>
<span class="lineno"> 140 </span>  )
<span class="lineno"> 141 </span>  
<span class="lineno"> 142 </span>  alarmManager.cancel(pendingIntent)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:158</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 155 </span>      END_ALARM_REQUEST_CODE,
<span class="lineno"> 156 </span>      intent,
<span class="lineno"> 157 </span>      PendingIntent.FLAG_UPDATE_CURRENT or 
<span class="caretline"><span class="lineno"> 158 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) PendingIntent.FLAG_IMMUTABLE <span class="keyword">else</span> <span class="number">0</span>&nbsp;</span>
<span class="lineno"> 159 </span>  )
<span class="lineno"> 160 </span>  
<span class="lineno"> 161 </span>  alarmManager.cancel(pendingIntent)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:186</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 183 </span>    private <span class="keyword">fun</span> scheduleExactAlarm(triggerTime: Long, pendingIntent: PendingIntent) {
<span class="lineno"> 184 </span>        <span class="keyword">try</span> {
<span class="lineno"> 185 </span>            <span class="keyword">when</span> {
<span class="caretline"><span class="lineno"> 186 </span>                <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 187 </span>                    <span class="comment">// Use setExactAndAllowWhileIdle for API 23+ to work in Doze mode</span>
<span class="lineno"> 188 </span>                    alarmManager.setExactAndAllowWhileIdle(
<span class="lineno"> 189 </span>                        AlarmManager.RTC_WAKEUP,
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ObsoleteSdkIntDivLink" onclick="reveal('ObsoleteSdkIntDiv');" />+ 19 More Occurrences...</button>
<div id="ObsoleteSdkIntDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:194</span>: <span class="message">Unnecessary; <code>Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</code> is never true here</span><br /><pre class="errorlines">
<span class="lineno"> 191 </span>                        pendingIntent
<span class="lineno"> 192 </span>                    )
<span class="lineno"> 193 </span>                }
<span class="caretline"><span class="lineno"> 194 </span>                <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 195 </span>                    <span class="comment">// Use setExact for API 19-22</span>
<span class="lineno"> 196 </span>                    alarmManager.setExact(
<span class="lineno"> 197 </span>                        AlarmManager.RTC_WAKEUP,
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt">../../src/main/java/com/app/notiminimalist/service/AlarmScheduler.kt</a>:243</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 240 </span>      requestCode,
<span class="lineno"> 241 </span>      intent,
<span class="lineno"> 242 </span>      PendingIntent.FLAG_UPDATE_CURRENT or 
<span class="caretline"><span class="lineno"> 243 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) PendingIntent.FLAG_IMMUTABLE <span class="keyword">else</span> <span class="number">0</span>&nbsp;</span>
<span class="lineno"> 244 </span>  )
<span class="lineno"> 245 </span>  
<span class="lineno"> 246 </span>  scheduleExactAlarm(triggerTime, pendingIntent)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:28</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  25 </span><span class="javadoc">     * This is important for reliable alarm scheduling.
</span><span class="lineno">  26 </span><span class="javadoc">     */</span>
<span class="lineno">  27 </span>    <span class="keyword">fun</span> isIgnoringBatteryOptimizations(): Boolean {
<span class="caretline"><span class="lineno">  28 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  29 </span>            powerManager.isIgnoringBatteryOptimizations(context.packageName)
<span class="lineno">  30 </span>        } <span class="keyword">else</span> {
<span class="lineno">  31 </span>            <span class="keyword">true</span> <span class="comment">// No battery optimization on older versions</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:39</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  36 </span><span class="javadoc">     * Get an intent to request battery optimization whitelist.
</span><span class="lineno">  37 </span><span class="javadoc">     */</span>
<span class="lineno">  38 </span>    <span class="keyword">fun</span> getBatteryOptimizationIntent(): Intent? {
<span class="caretline"><span class="lineno">  39 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  40 </span>            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
<span class="lineno">  41 </span>                data = Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)
<span class="lineno">  42 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:53</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  50 </span><span class="javadoc">   * Returns true if request was initiated, false if already whitelisted or not supported.
</span><span class="lineno">  51 </span><span class="javadoc">   */</span>
<span class="lineno">  52 </span>  <span class="keyword">fun</span> requestBatteryOptimizationWhitelist(): Boolean {
<span class="caretline"><span class="lineno">  53 </span>      <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> &amp;&amp; !isIgnoringBatteryOptimizations()) {</span>
<span class="lineno">  54 </span>          <span class="keyword">try</span> {
<span class="lineno">  55 </span>              <span class="keyword">val</span> intent = getBatteryOptimizationIntent()
<span class="lineno">  56 </span>              intent?.let {
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:75</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  72 </span><span class="javadoc">     * Check if device is in doze mode.
</span><span class="lineno">  73 </span><span class="javadoc">     */</span>
<span class="lineno">  74 </span>    <span class="keyword">fun</span> isDeviceInDozeMode(): Boolean {
<span class="caretline"><span class="lineno">  75 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  76 </span>            powerManager.isDeviceIdleMode
<span class="lineno">  77 </span>        } <span class="keyword">else</span> {
<span class="lineno">  78 </span>            <span class="keyword">false</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:139</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 136 </span><span class="javadoc">     * Log current power state for debugging.
</span><span class="lineno"> 137 </span><span class="javadoc">     */</span>
<span class="lineno"> 138 </span>    <span class="keyword">fun</span> logPowerState() {
<span class="caretline"><span class="lineno"> 139 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 140 </span>            Log.d(TAG, <span class="string">"Battery optimization ignored: ${</span>isIgnoringBatteryOptimizations()<span class="string">}"</span>)
<span class="lineno"> 141 </span>            Log.d(TAG, <span class="string">"Device in doze mode: ${</span>isDeviceInDozeMode()<span class="string">}"</span>)
<span class="lineno"> 142 </span>            Log.d(TAG, <span class="string">"Power save mode: ${</span>powerManager.isPowerSaveMode<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:46</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  43 </span><span class="javadoc">     * The app works fine with just NotificationListenerService permission.
</span><span class="lineno">  44 </span><span class="javadoc">     */</span>
<span class="lineno">  45 </span>    <span class="keyword">fun</span> hasPermission(): Boolean {
<span class="caretline"><span class="lineno">  46 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  47 </span>            notificationManager.isNotificationPolicyAccessGranted
<span class="lineno">  48 </span>        } <span class="keyword">else</span> {
<span class="lineno">  49 </span>            <span class="keyword">true</span> <span class="comment">// No permission needed for API &lt; 23</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:74</span>: <span class="message">Unnecessary; <code>Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</code> is always true here (<code>SDK_INT</code> &#8805; 23 and &lt; 26)</span><br /><pre class="errorlines">
<span class="lineno">  71 </span>         }
<span class="lineno">  72 </span>         <span class="keyword">true</span>
<span class="lineno">  73 </span>     }
<span class="caretline"><span class="lineno">  74 </span>     <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>         <span class="comment">// For API 23-25, rely on NotificationListenerService + audio suppression</span>
<span class="lineno">  76 </span>         applyConservativeAudioSuppression()
<span class="lineno">  77 </span>         
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:113</span>: <span class="message">Unnecessary; <code>Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</code> is always true here (<code>SDK_INT</code> &#8805; 23 and &lt; 26)</span><br /><pre class="errorlines">
<span class="lineno"> 110 </span>                    }
<span class="lineno"> 111 </span>                    <span class="keyword">true</span>
<span class="lineno"> 112 </span>                }
<span class="caretline"><span class="lineno"> 113 </span>                <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 114 </span>                    <span class="comment">// For API 23-25, restore audio</span>
<span class="lineno"> 115 </span>                    restoreConservativeAudioSuppression()
<span class="lineno"> 116 </span>                    
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:167</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 164 </span><span class="javadoc">     * This enables per-app control rather than blocking all apps globally.
</span><span class="lineno"> 165 </span><span class="javadoc">     */</span>
<span class="lineno"> 166 </span>    private <span class="keyword">fun</span> createSelectiveNotificationPolicy(): NotificationManager.Policy {
<span class="caretline"><span class="lineno"> 167 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 168 </span>            <span class="keyword">val</span> silencedApps = schedulePreferences.silencedApps
<span class="lineno"> 169 </span>            <span class="keyword">val</span> alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
<span class="lineno"> 170 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:217</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 214 </span><span class="javadoc">     * This allows certain apps to bypass DND while silencing others.
</span><span class="lineno"> 215 </span><span class="javadoc">     */</span>
<span class="lineno"> 216 </span>    private <span class="keyword">fun</span> createNotificationPolicy(): NotificationManager.Policy {
<span class="caretline"><span class="lineno"> 217 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 218 </span>            <span class="keyword">val</span> silencedApps = schedulePreferences.silencedApps
<span class="lineno"> 219 </span>            <span class="keyword">val</span> alwaysAllowedApps = schedulePreferences.alwaysAllowedApps
<span class="lineno"> 220 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:371</span>: <span class="message">Unnecessary; <code>Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</code> is always true here (<code>SDK_INT</code> &#8805; 23 and &lt; 26)</span><br /><pre class="errorlines">
<span class="lineno"> 368 </span>  Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> channelManager.areChannelsProperlyConfigured()
<span class="lineno"> 369 </span>  
<span class="lineno"> 370 </span>  <span class="comment">// For older APIs, having at least some configuration is good</span>
<span class="caretline"><span class="lineno"> 371 </span>  <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> -> (silencedApps.isNotEmpty() || alwaysAllowedApps.isNotEmpty())</span>
<span class="lineno"> 372 </span>  
<span class="lineno"> 373 </span>  <span class="comment">// For very old APIs, any configuration is suboptimal</span>
<span class="lineno"> 374 </span>  <span class="keyword">else</span> -> <span class="keyword">false</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:396</span>: <span class="message">Unnecessary; <code>Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</code> is always true here (<code>SDK_INT</code> &#8805; 23 and &lt; 26)</span><br /><pre class="errorlines">
<span class="lineno"> 393 </span>      }
<span class="lineno"> 394 </span>  }
<span class="lineno"> 395 </span>  
<span class="caretline"><span class="lineno"> 396 </span>  <span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span> -> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 397 </span>      recommendations.add(<span class="string">"Consider updating to Android 8.0+ for better notification control"</span>)
<span class="lineno"> 398 </span>      <span class="keyword">if</span> (silencedApps.isEmpty() &amp;&amp; alwaysAllowedApps.isEmpty()) {
<span class="lineno"> 399 </span>          recommendations.add(<span class="string">"Configure apps for silencing to benefit from DND features"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:176</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  173 </span>            WindowCompat.setDecorFitsSystemWindows(it.window, <span class="keyword">false</span>)
<span class="lineno">  174 </span>            
<span class="lineno">  175 </span>            <span class="comment">// Dynamic status bar color based on theme</span>
<span class="caretline"><span class="lineno">  176 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  177 </span>                it.window.statusBarColor = <span class="keyword">if</span> (isDarkTheme) {
<span class="lineno">  178 </span>                    android.graphics.Color.parseColor(<span class="string">"#1C1C1E"</span>) <span class="comment">// Dark surface color</span>
<span class="lineno">  179 </span>                } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/NotificationListenerService.kt">../../src/main/java/com/app/notiminimalist/service/NotificationListenerService.kt</a>:136</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 133 </span><span class="javadoc">     */</span>
<span class="lineno"> 134 </span>    private <span class="keyword">fun</span> dismissNotification(sbn: StatusBarNotification) {
<span class="lineno"> 135 </span>        <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 136 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 137 </span>                cancelNotification(sbn.key)
<span class="lineno"> 138 </span>                Log.v(TAG, <span class="string">"Dismissed notification: ${</span>sbn.packageName<span class="string">} via key"</span>)
<span class="lineno"> 139 </span>            } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/NotificationListenerService.kt">../../src/main/java/com/app/notiminimalist/service/NotificationListenerService.kt</a>:162</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 159 </span>            dismissNotification(sbn)
<span class="lineno"> 160 </span>            
<span class="lineno"> 161 </span>            <span class="comment">// Second attempt - try alternative methods</span>
<span class="caretline"><span class="lineno"> 162 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 163 </span>                <span class="comment">// Try to cancel by key again with a slight delay</span>
<span class="lineno"> 164 </span>                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
<span class="lineno"> 165 </span>                    <span class="keyword">try</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt">../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt</a>:63</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno">  60 </span><span class="javadoc">   */</span>
<span class="lineno">  61 </span>  <span class="keyword">fun</span> hasDndPermission(): Boolean {
<span class="lineno">  62 </span>      <span class="keyword">val</span> notificationManager = activity.getSystemService(Context.NOTIFICATION_SERVICE) <span class="keyword">as</span> NotificationManager
<span class="caretline"><span class="lineno">  63 </span>      <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  64 </span>          notificationManager.isNotificationPolicyAccessGranted
<span class="lineno">  65 </span>      } <span class="keyword">else</span> {
<span class="lineno">  66 </span>          <span class="keyword">true</span> <span class="comment">// No permission needed for API &lt; 23</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt">../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt</a>:107</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 23</span><br /><pre class="errorlines">
<span class="lineno"> 104 </span>    <span class="keyword">fun</span> requestDndPermission() {
<span class="lineno"> 105 </span>        <span class="keyword">if</span> (hasDndPermission()) <span class="keyword">return</span>
<span class="lineno"> 106 </span>        
<span class="caretline"><span class="lineno"> 107 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 108 </span>            <span class="keyword">val</span> intent = Intent(Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS)
<span class="lineno"> 109 </span>            dndPermissionLauncher?.launch(intent)
<span class="lineno"> 110 </span>        }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:3</span>: <span class="message">The resource <code>R.color.purple_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_200"</span></span>>#FFBB86FC<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:4</span>: <span class="message">The resource <code>R.color.purple_500</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_500"</span></span>>#FF6200EE<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:5</span>: <span class="message">The resource <code>R.color.purple_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_700"</span></span>>#FF3700B3<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:6</span>: <span class="message">The resource <code>R.color.teal_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_200"</span></span>>#FF03DAC5<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:7</span>: <span class="message">The resource <code>R.color.teal_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_700"</span></span>>#FF018786<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:8</span>: <span class="message">The resource <code>R.color.black</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"black"</span></span>>#FF000000<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:9</span>: <span class="message">The resource <code>R.color.white</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"white"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/drawable-v24/ic_launcher_foreground.xml">../../src/main/res/drawable-v24/ic_launcher_foreground.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_launcher_foreground</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">xmlns:</span><span class="attribute">aapt</span>=<span class="value">"http://schemas.android.com/aapt"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"108dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"108dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>skip-libraries</b> (default is true):<br/>
Whether the unused resource check should skip reporting unused resources in libraries.<br/>
<br/>
Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with <code>checkDependencies=true</code>).<br/>
<br/>
However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnusedResources"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"skip-libraries"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="MonochromeLauncherIcon"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MonochromeLauncherIconCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Monochrome icon is not defined</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/mipmap-anydpi-v26/ic_launcher.xml">../../src/main/res/mipmap-anydpi-v26/ic_launcher.xml</a>:2</span>: <span class="message">The application adaptive icon is missing a monochrome tag</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;adaptive-icon</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;background</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_background"</span>/>
<span class="lineno"> 4 </span>    <span class="tag">&lt;foreground</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@mipmap/ic_launcher_foreground"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/adaptive-icon></span></pre>

<span class="location"><a href="../../src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml">../../src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml</a>:2</span>: <span class="message">The application adaptive roundIcon is missing a monochrome tag</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno"> 2 </span><span class="warning"><span class="tag">&lt;adaptive-icon</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span>    <span class="tag">&lt;background</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@drawable/ic_launcher_background"</span>/>
<span class="lineno"> 4 </span>    <span class="tag">&lt;foreground</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">drawable</span>=<span class="value">"@mipmap/ic_launcher_foreground"</span>/>
<span class="lineno"> 5 </span><span class="tag">&lt;/adaptive-icon></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMonochromeLauncherIcon" style="display: none;">
If <code>android:roundIcon</code> and <code>android:icon</code> are both in your manifest, you must either remove the reference to <code>android:roundIcon</code> if it is not needed; or, supply the monochrome icon in the drawable defined by the <code>android:roundIcon</code> and <code>android:icon</code> attribute.<br/>
<br/>
For example, if <code>android:roundIcon</code> and <code>android:icon</code> are both in the manifest, a launcher might choose to use <code>android:roundIcon</code> over <code>android:icon</code> to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in <code>android:roundIcon</code>.<br/>To suppress this error, use the issue id "MonochromeLauncherIcon" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MonochromeLauncherIcon</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMonochromeLauncherIconLink" onclick="reveal('explanationMonochromeLauncherIcon');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MonochromeLauncherIconCardLink" onclick="hideid('MonochromeLauncherIconCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconLocation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLocationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image defined in density-independent drawable folder</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/app_logo.png">../../src/main/res/drawable/app_logo.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/app_logo.png" /><span class="message">Found bitmap drawable <code>res/drawable/app_logo.png</code> in densityless folder</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLocation" style="display: none;">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLocation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLocationLink" onclick="reveal('explanationIconLocation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLocationCardLink" onclick="hideid('IconLocationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseKtx"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseKtxCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use KTX extension function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:41</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>    <span class="keyword">fun</span> getBatteryOptimizationIntent(): Intent? {
<span class="lineno">  39 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="lineno">  40 </span>            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
<span class="caretline"><span class="lineno">  41 </span>                data = <span class="warning">Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>            }
<span class="lineno">  43 </span>        } <span class="keyword">else</span> {
<span class="lineno">  44 </span>            <span class="keyword">null</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt">../../src/main/java/com/app/notiminimalist/utils/BatteryOptimizer.kt</a>:41</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>    <span class="keyword">fun</span> getBatteryOptimizationIntent(): Intent? {
<span class="lineno">  39 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="lineno">  40 </span>            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
<span class="caretline"><span class="lineno">  41 </span>                data = <span class="warning">Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>            }
<span class="lineno">  43 </span>        } <span class="keyword">else</span> {
<span class="lineno">  44 </span>            <span class="keyword">null</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:419</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 416 </span>  <span class="keyword">try</span> {
<span class="lineno"> 417 </span>      <span class="comment">// Store original volume</span>
<span class="lineno"> 418 </span>      <span class="keyword">val</span> originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
<span class="caretline"><span class="lineno"> 419 </span>      <span class="warning">preferences.edit()</span>.putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 420 </span>      
<span class="lineno"> 421 </span>      <span class="comment">// Set notification stream volume to 0</span>
<span class="lineno"> 422 </span>      audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, <span class="number">0</span>, <span class="number">0</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:437</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 434 </span>  <span class="keyword">try</span> {
<span class="lineno"> 435 </span>      <span class="comment">// Store original volume</span>
<span class="lineno"> 436 </span>      <span class="keyword">val</span> originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
<span class="caretline"><span class="lineno"> 437 </span>      <span class="warning">preferences.edit()</span>.putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 438 </span>      
<span class="lineno"> 439 </span>      <span class="comment">// Reduce notification stream volume to 20% of original (not completely silent)</span>
<span class="lineno"> 440 </span>      <span class="keyword">val</span> reducedVolume = maxOf(<span class="number">1</span>, (originalVolume * <span class="number">0.2</span>).toInt())
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:482</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 479 </span>  <span class="keyword">try</span> {
<span class="lineno"> 480 </span>      <span class="comment">// Store original volume</span>
<span class="lineno"> 481 </span>      <span class="keyword">val</span> originalVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
<span class="caretline"><span class="lineno"> 482 </span>      <span class="warning">preferences.edit()</span>.putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 483 </span>      
<span class="lineno"> 484 </span>      <span class="comment">// Set notification stream volume to 0 (completely silent)</span>
<span class="lineno"> 485 </span>      audioManager.setStreamVolume(AudioManager.STREAM_NOTIFICATION, <span class="number">0</span>, <span class="number">0</span>)
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UseKtxDivLink" onclick="reveal('UseKtxDiv');" />+ 17 More Occurrences...</button>
<div id="UseKtxDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/app/notiminimalist/service/DndManager.kt">../../src/main/java/com/app/notiminimalist/service/DndManager.kt</a>:504</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 501 </span>          Log.d(TAG, <span class="string">"Restored notification audio from conservative suppression (volume: -> $originalVolume)"</span>)
<span class="lineno"> 502 </span>          
<span class="lineno"> 503 </span>          <span class="comment">// Clear the stored volume</span>
<span class="caretline"><span class="lineno"> 504 </span>          <span class="warning">preferences.edit()</span>.remove(PREFS_ORIGINAL_NOTIFICATION_VOLUME).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 505 </span>      }
<span class="lineno"> 506 </span>  } catch (e: Exception) {
<span class="lineno"> 507 </span>      Log.e(TAG, <span class="string">"Failed to restore notification audio from conservative suppression"</span>, e)
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:166</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  163 </span>    
<span class="lineno">  164 </span>    <span class="comment">// Save theme preference when changed</span>
<span class="lineno">  165 </span>    LaunchedEffect(isDarkTheme) {
<span class="caretline"><span class="lineno">  166 </span>        <span class="warning">themePrefs.edit()</span>.putBoolean(<span class="string">"dark_theme"</span>, isDarkTheme).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  167 </span>    }
<span class="lineno">  168 </span>    
<span class="lineno">  169 </span>    <span class="comment">// Configure system UI to match theme dynamically</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:178</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  175 </span>            <span class="comment">// Dynamic status bar color based on theme</span>
<span class="lineno">  176 </span>            <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
<span class="lineno">  177 </span>                it.window.statusBarColor = <span class="keyword">if</span> (isDarkTheme) {
<span class="caretline"><span class="lineno">  178 </span>                    <span class="warning">android.graphics.Color.parseColor(<span class="string">"#1C1C1E"</span>)</span> <span class="comment">// Dark surface color</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  179 </span>                } <span class="keyword">else</span> {
<span class="lineno">  180 </span>                    android.graphics.Color.parseColor(<span class="string">"#2B9EDA"</span>) <span class="comment">// Primary blue</span>
<span class="lineno">  181 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:178</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  175 </span>            <span class="comment">// Dynamic status bar color based on theme</span>
<span class="lineno">  176 </span>            <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
<span class="lineno">  177 </span>                it.window.statusBarColor = <span class="keyword">if</span> (isDarkTheme) {
<span class="caretline"><span class="lineno">  178 </span>                    <span class="warning">android.graphics.Color.parseColor(<span class="string">"#1C1C1E"</span>)</span> <span class="comment">// Dark surface color</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  179 </span>                } <span class="keyword">else</span> {
<span class="lineno">  180 </span>                    android.graphics.Color.parseColor(<span class="string">"#2B9EDA"</span>) <span class="comment">// Primary blue</span>
<span class="lineno">  181 </span>                }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:180</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  177 </span>                it.window.statusBarColor = <span class="keyword">if</span> (isDarkTheme) {
<span class="lineno">  178 </span>                    android.graphics.Color.parseColor(<span class="string">"#1C1C1E"</span>) <span class="comment">// Dark surface color</span>
<span class="lineno">  179 </span>                } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno">  180 </span>                    <span class="warning">android.graphics.Color.parseColor(<span class="string">"#2B9EDA"</span>)</span> <span class="comment">// Primary blue</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  181 </span>                }
<span class="lineno">  182 </span>            }
<span class="lineno">  183 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/MainActivity.kt">../../src/main/java/com/app/notiminimalist/MainActivity.kt</a>:180</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  177 </span>                it.window.statusBarColor = <span class="keyword">if</span> (isDarkTheme) {
<span class="lineno">  178 </span>                    android.graphics.Color.parseColor(<span class="string">"#1C1C1E"</span>) <span class="comment">// Dark surface color</span>
<span class="lineno">  179 </span>                } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno">  180 </span>                    <span class="warning">android.graphics.Color.parseColor(<span class="string">"#2B9EDA"</span>)</span> <span class="comment">// Primary blue</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  181 </span>                }
<span class="lineno">  182 </span>            }
<span class="lineno">  183 </span>            
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt">../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt</a>:121</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>        
<span class="lineno"> 119 </span>        <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
<span class="lineno"> 120 </span>            <span class="keyword">val</span> intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
<span class="caretline"><span class="lineno"> 121 </span>                data = <span class="warning">Uri.parse(<span class="string">"package:${</span>activity.packageName<span class="string">}"</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>            }
<span class="lineno"> 123 </span>            exactAlarmPermissionLauncher?.launch(intent)
<span class="lineno"> 124 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt">../../src/main/java/com/app/notiminimalist/utils/PermissionManager.kt</a>:121</span>: <span class="message">Use the KTX extension function <code>String.toUri</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>        
<span class="lineno"> 119 </span>        <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
<span class="lineno"> 120 </span>            <span class="keyword">val</span> intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
<span class="caretline"><span class="lineno"> 121 </span>                data = <span class="warning">Uri.parse(<span class="string">"package:${</span>activity.packageName<span class="string">}"</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>            }
<span class="lineno"> 123 </span>            exactAlarmPermissionLauncher?.launch(intent)
<span class="lineno"> 124 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:44</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  41 </span><span class="javadoc">     */</span>
<span class="lineno">  42 </span>    <span class="keyword">var</span> isSilencingEnabled: Boolean
<span class="lineno">  43 </span>        get() = preferences.getBoolean(KEY_SILENCING_ENABLED, <span class="keyword">false</span>)
<span class="caretline"><span class="lineno">  44 </span>        set(value) = <span class="warning">preferences.edit()</span>.putBoolean(KEY_SILENCING_ENABLED, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  45 </span>    
<span class="lineno">  46 </span>    <span class="javadoc">/**
</span><span class="lineno">  47 </span><span class="javadoc">     * Start time for scheduled silencing.
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:54</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  51 </span>            preferences.getInt(KEY_START_HOUR, DEFAULT_START_HOUR),
<span class="lineno">  52 </span>            preferences.getInt(KEY_START_MINUTE, DEFAULT_START_MINUTE)
<span class="lineno">  53 </span>        )
<span class="caretline"><span class="lineno">  54 </span>        set(value) = <span class="warning">preferences.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  55 </span>            .putInt(KEY_START_HOUR, value.hour)
<span class="lineno">  56 </span>            .putInt(KEY_START_MINUTE, value.minute)
<span class="lineno">  57 </span>            .apply()
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:67</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  64 </span>            preferences.getInt(KEY_END_HOUR, DEFAULT_END_HOUR),
<span class="lineno">  65 </span>            preferences.getInt(KEY_END_MINUTE, DEFAULT_END_MINUTE)
<span class="lineno">  66 </span>        )
<span class="caretline"><span class="lineno">  67 </span>        set(value) = <span class="warning">preferences.edit()</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  68 </span>            .putInt(KEY_END_HOUR, value.hour)
<span class="lineno">  69 </span>            .putInt(KEY_END_MINUTE, value.minute)
<span class="lineno">  70 </span>            .apply()
</pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:78</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  75 </span><span class="javadoc">     */</span>
<span class="lineno">  76 </span>    <span class="keyword">var</span> selectedApps: Set&lt;String>
<span class="lineno">  77 </span>        get() = preferences.getStringSet(KEY_SELECTED_APPS, emptySet()) ?: emptySet()
<span class="caretline"><span class="lineno">  78 </span>        set(value) = <span class="warning">preferences.edit()</span>.putStringSet(KEY_SELECTED_APPS, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>    
<span class="lineno">  80 </span>    <span class="javadoc">/**
</span><span class="lineno">  81 </span><span class="javadoc">     * Set of package names for apps that are silenced during scheduled hours.
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:85</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  82 </span><span class="javadoc">     */</span>
<span class="lineno">  83 </span>    <span class="keyword">var</span> silencedApps: Set&lt;String>
<span class="lineno">  84 </span>        get() = preferences.getStringSet(KEY_SILENCED_APPS, emptySet()) ?: emptySet()
<span class="caretline"><span class="lineno">  85 </span>        set(value) = <span class="warning">preferences.edit()</span>.putStringSet(KEY_SILENCED_APPS, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  86 </span>    
<span class="lineno">  87 </span>    <span class="javadoc">/**
</span><span class="lineno">  88 </span><span class="javadoc">     * Set of package names for apps that are always allowed (bypass silencing).
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:92</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  89 </span><span class="javadoc">     */</span>
<span class="lineno">  90 </span>    <span class="keyword">var</span> alwaysAllowedApps: Set&lt;String>
<span class="lineno">  91 </span>        get() = preferences.getStringSet(KEY_ALWAYS_ALLOWED_APPS, emptySet()) ?: emptySet()
<span class="caretline"><span class="lineno">  92 </span>        set(value) = <span class="warning">preferences.edit()</span>.putStringSet(KEY_ALWAYS_ALLOWED_APPS, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  93 </span>    
<span class="lineno">  94 </span>    <span class="javadoc">/**
</span><span class="lineno">  95 </span><span class="javadoc">     * Whether "Select All" is enabled for app selection.
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:99</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  96 </span><span class="javadoc">     */</span>
<span class="lineno">  97 </span>    <span class="keyword">var</span> isSelectAllAppsEnabled: Boolean
<span class="lineno">  98 </span>        get() = preferences.getBoolean(KEY_SELECT_ALL_APPS, <span class="keyword">true</span>)
<span class="caretline"><span class="lineno">  99 </span>        set(value) = <span class="warning">preferences.edit()</span>.putBoolean(KEY_SELECT_ALL_APPS, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 100 </span>    
<span class="lineno"> 101 </span>    <span class="javadoc">/**
</span><span class="lineno"> 102 </span><span class="javadoc">     * Whether this is the first run of the app.
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:106</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 103 </span><span class="javadoc">     */</span>
<span class="lineno"> 104 </span>    <span class="keyword">var</span> isFirstRun: Boolean
<span class="lineno"> 105 </span>        get() = preferences.getBoolean(KEY_FIRST_RUN, <span class="keyword">true</span>)
<span class="caretline"><span class="lineno"> 106 </span>        set(value) = <span class="warning">preferences.edit()</span>.putBoolean(KEY_FIRST_RUN, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 107 </span>    
<span class="lineno"> 108 </span>    <span class="javadoc">/**
</span><span class="lineno"> 109 </span><span class="javadoc">     * Add an app package name to the selected apps set.
</span></pre>

<span class="location"><a href="../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt">../../src/main/java/com/app/notiminimalist/data/SchedulePreferences.kt</a>:234</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 231 </span><span class="javadoc">     * Reset all preferences to default values.
</span><span class="lineno"> 232 </span><span class="javadoc">     */</span>
<span class="lineno"> 233 </span>    <span class="keyword">fun</span> resetToDefaults() {
<span class="caretline"><span class="lineno"> 234 </span>        <span class="warning">preferences.edit()</span>.clear().apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 235 </span>    }
<span class="lineno"> 236 </span>}
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUseKtx" style="display: none;">
The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>remove-defaults</b> (default is true):<br/>
Whether to skip arguments that match the defaults provided by the extension.<br/>
<br/>
Extensions often provide default values for some of the parameters. For example:
<pre>
fun Path.readLines(charset: Charset = Charsets.UTF_8): List&lt;String> { return Files.readAllLines(this, charset) }
</pre>
This lint check will by default automatically omit parameters that match the default, so if your code was calling<br/>

<pre>
Files.readAllLines(file, Charset.UTF_8)
</pre>
lint would replace this with
<pre>
file.readLines()
</pre>
rather than<br/>

<pre>
file.readLines(Charset.UTF_8
</pre>
You can turn this behavior off using this option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"remove-defaults"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
<b>require-present</b> (default is true):<br/>
Whether to only offer extensions already available.<br/>
<br/>
This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"require-present"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>To suppress this error, use the issue id "UseKtx" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseKtx</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseKtxLink" onclick="reveal('explanationUseKtx');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseKtxCardLink" onclick="hideid('UseKtxCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:56</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>    implementation(libs.androidx.material3)
<span class="lineno"> 54 </span>    
<span class="lineno"> 55 </span>    // Material Icons Extended - provides all Material Design icons
<span class="caretline"><span class="lineno"> 56 </span>    implementation(<span class="warning">"androidx.compose.material:material-icons-extended:1.5.4"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>    
<span class="lineno"> 58 </span>    // Core library desugaring for Java 8+ time APIs
<span class="lineno"> 59 </span>    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
</pre>

<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:59</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    implementation("androidx.compose.material:material-icons-extended:1.5.4")
<span class="lineno"> 57 </span>    
<span class="lineno"> 58 </span>    // Core library desugaring for Java 8+ time APIs
<span class="caretline"><span class="lineno"> 59 </span>    coreLibraryDesugaring(<span class="warning">"com.android.tools:desugar_jdk_libs:2.0.4"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>    
<span class="lineno"> 61 </span>    testImplementation(libs.junit)
<span class="lineno"> 62 </span>    androidTestImplementation(libs.androidx.junit)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">ArcAnimationSpecTypeIssue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ArcAnimationSpec is designed for 2D values. Particularly, for positional values such as Offset.<br/>
Trying to use it for values of different dimensions (Float, Size, Color, etc.) will result in unpredictable animation behavior.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateCreation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>. See <code>mutableIntStateOf()</code>, <code>mutableLongStateOf()</code>, <code>mutableFloatStateOf()</code>, and <code>mutableDoubleStateOf()</code> for more information.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateValueProperty<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Avoid using the generic <code>value</code> property when using a specialized State type. Reading or writing to the state's generic <code>value</code> property will result in an unnecessary autoboxing operation. Prefer the specialized value property (e.g. <code>intValue</code> for <code>MutableIntState</code>), or use property delegation to avoid unnecessary allocations.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
@Composable functions without a return type should use similar naming to classes, starting with an uppercase letter and ending with a noun. @Composable functions with a return type should be treated as normal Kotlin functions, starting with a lowercase letter.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CompositionLocalNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
CompositionLocal properties should be prefixed with <code>Local</code>. This helps make it clear at their use site that these values are local to the current composition. Typically the full name will be <code>Local</code> + the type of the CompositionLocal, for example val LocalFoo = compositionLocalOf { Foo() }.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConflictingOnColor<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In the Material color system background colors have a corresponding 'on' color which is used for the content color inside a component. For example, a button colored <code>primary</code> will have <code>onPrimary</code> text. Because of this, it is important that there is only one possible <code>onColor</code> for a given color value, otherwise there is no way to know which 'on' color should be used inside a component. To fix this either use the same 'on' color for identical background colors, or use a different background color for each 'on' color.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CoroutineCreationDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a coroutine with <code>async</code> or <code>launch</code> during composition is often incorrect - this means that a coroutine will be created even if the composition fails / is rolled back, and it also means that multiple coroutines could end up mutating the same state, causing inconsistent results. Instead, use <code>LaunchedEffect</code> and create coroutines inside the suspending block. The block will only run after a successful composition, and will cancel existing coroutines when <code>key</code> changes, allowing correct cleanup.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FlowOperatorInvokedInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Flow operator function within composition will result in a new Flow being created every recomposition, which will reset collectAsState() and cause other related problems. Instead Flow operators should be called inside <code>remember</code>, or a side effect such as LaunchedEffect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FrequentlyChangedStateReadInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This property is observable and is updated after every scroll or remeasure. If you use it in the composable function directly, it will be recomposed on every change, causing potential performance issues including infinity recomposition loops. Prefer wrapping it with derivedStateOf to use calculation based on this property in composition or collect changes inside LaunchedEffect instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidColorHexValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined / incorrect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidLanguageTagDelimiter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A language tag must be compliant with IETF BCP47, specifically a sequence of subtags must be separated by hyphens (-) instead of underscores (_)<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.text<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=779818">https://issuetracker.google.com/issues/new?component=779818</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LaunchDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>launch</code> during composition is incorrect. Doing so will cause launch to be called multiple times resulting in a RuntimeException. Instead, use <code>SideEffect</code> and <code>launch</code> inside of the suspending block. The block will only run after a successful composition.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingColorAlphaChannel<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined. For example, passing 0xFF0000 will result in a missing alpha channel, so the color will not appear visible.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryExtensionFunction<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should return Modifier as their type, and not a subtype of Modifier (such as Modifier.Element).<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryUnreferencedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions are fluently chained to construct a chain of Modifier objects that will be applied to a layout. As a result, each factory function <i>must</i> use the receiver <code>Modifier</code> parameter, to ensure that the function is returning a chain that includes previous items in the chain. Make sure the returned chain either explicitly includes <code>this</code>, such as <code>return this.then(MyModifier)</code> or implicitly by returning a chain that starts with an implicit call to another factory function, such as <code>return myModifier()</code>, where <code>myModifier</code> is defined as <code>fun Modifier.myModifier(): Modifier</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierNodeInspectableProperties<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ModifierNodeElements may override inspectableProperties() to provide information about the modifier in the layout inspector. The default implementation attempts to read all of the properties on the class reflectively, which may not comprehensively or effectively describe the modifier.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MultipleAwaitPointerEventScopes<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. Multiple calls to awaitPointerEventScope may exit the scope. During this time there is no guarantee that the events will be queued and some events may be dropped. It is recommended to use a single top-level block and perform the pointer events processing within such block.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MutableCollectionMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoCollectCallFound<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You must call collect on the progress in the onBack function. The collect call is what properly splits the callback so it knows what to do when the back gestures is started vs when it is completed. Failing to call collect will cause all code in the block to run when the gesture is started.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OpaqueUnitKey<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Certain Compose functions including <code>remember</code>, <code>LaunchedEffect</code>, and <code>DisposableEffect</code> declare (and sometimes require) one or more key parameters. When a key parameter changes, it is a signal that the previous invocation is now invalid. In certain cases, it may be required to pass <code>Unit</code> as a key to one of these functions, indicating that the invocation never becomes invalid. Using <code>Unit</code> as a key should be done infrequently, and should always be done explicitly by passing the <code>Unit</code> literal. This inspection checks for invocations where <code>Unit</code> is being passed as a key argument in any form other than the <code>Unit</code> literal. This is usually done by mistake, and can harm readability. If a Unit expression is being passed as a key, it is always equivalent to move the expression before the function invocation and pass the <code>Unit</code> literal instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProduceStateDoesNotAssignValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
produceState returns an observable State using values assigned inside the producer lambda. If the lambda never assigns (i.e <code>value = foo</code>), then the State will never change. Make sure to assign a value when the source you are producing values from changes / emits a new value. For sample usage see the produceState documentation.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A call to <code>remember</code> that returns <code>Unit</code> is always an error. This typically happens when using <code>remember</code> to mutate variables on an object. <code>remember</code> is executed during the composition, which means that if the composition fails or is happening on a separate thread, the mutated variables may not reflect the true state of the composition. Instead, use <code>SideEffect</code> to make deferred changes once the composition succeeds, or mutate <code>MutableState</code> backed variables directly, as these will handle composition failure for you.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberSaveableSaverParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first parameter to <code>rememberSaveable</code> is a vararg parameter for inputs that when changed will cause the state to reset. Passing a <code>Saver</code> object to this parameter is an error, as the intention is to pass the <code>Saver</code> object to the saver parameter. Since the saver parameter is not the first parameter, it must be explicitly named.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime.saveable<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ReturnFromAwaitPointerEventScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. By using the return value of awaitPointerEventScope one might unexpectedly lose events. If another awaitPointerEventScope is restarted there is no guarantee that the events will persist between those calls. In this case you should keep all events inside the awaitPointerEventScope block<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StateFlowValueCalledInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling StateFlow.value within composition will not observe changes to the StateFlow, so changes might not be reflected within the composition. Instead you should use stateFlow.collectAsState() to observe changes to the StateFlow, and recompose when it changes.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousCompositionLocalModifierRead<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Jetpack Compose is unable to send updated values of a CompositionLocal when it's read in a Modifier.Node's initializer and onAttach() or onDetach() callbacks. Modifier.Node's callbacks are not aware of snapshot reads, and their lifecycle callbacks are not invoked on every recomposition. If you read a CompositionLocal in onAttach() or onDetach(), you will only get the CompositionLocal's value once at the moment of the read, which may lead to unexpected behaviors. We recommend instead reading CompositionLocals at time-of-use in callbacks that apply your Modifier's behavior, like measure() for LayoutModifierNode, draw() for DrawModifierNode, and so on. To observe the value of the CompositionLocal manually, extend from the ObserverNode interface and place the read inside an observeReads {} block within the onObservedReadsChanged() callback.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousModifierThen<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Modifier factory function with an implicit receiver inside Modifier.then will result in the receiver (<code>this</code>) being added twice to the chain. For example, fun Modifier.myModifier() = this.then(otherModifier()) - the implementation of factory functions such as Modifier.otherModifier() will internally call this.then(...) to chain the provided modifier with their implementation. When you expand this.then(otherModifier()), it becomes: this.then(this.then(OtherModifierImplementation)) - so you can see that <code>this</code> is included twice in the chain, which results in modifiers such as padding being applied twice, for example. Instead, you should either remove the then() and directly chain the factory function on the receiver, this.otherModifier(), or add the empty Modifier as the receiver for the factory, such as this.then(Modifier.otherModifier())<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TestManifestGradleConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The androidx.compose.ui:ui-test-manifest dependency is needed for launching a Compose host, such as with createComposeRule. However, it only needs to be present in testing configurations therefore use this dependency with the debugImplementation configuration<br/><div class="moreinfo">More info: <a href="https://developer.android.com/jetpack/compose/testing#setup">https://developer.android.com/jetpack/compose/testing#setup</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.compose.ui.test.manifest<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=741505">https://issuetracker.google.com/issues/new?component=741505</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnnecessaryComposedModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.composed</code> allows invoking @Composable functions when creating a <code>Modifier</code> instance - for example, using <code>remember</code> to have instance-specific state, allowing the same <code>Modifier</code> object to be safely used in multiple places. Using <code>Modifier.composed</code> without calling any @Composable functions inside is unnecessary, and since the Modifier is no longer skippable, this can cause a lot of extra work inside the composed body, leading to worse performance.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedAnimatable<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Animatable instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the Animatable to an object that is not created during composition, or wrap the Animatable in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableInteractionSource<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
MutableInteractionSource instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the MutableInteractionSource to an object that is not created during composition, or wrap the MutableInteractionSource in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
State objects created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedBoxWithConstraintsScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in BoxWithConstraints has a scope which will include the incoming constraints. If this scope is ignored, then the cost of subcomposition is being wasted and this BoxWithConstraints should be replaced with a Box.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedContentLambdaTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in AnimatedContent works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly animated. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous transition between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedCrossfadeTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in Crossfade works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly crossfaded. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous crossfade between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterial3ScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterialScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedTransitionTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Transition.animate* functions provide a target state parameter in the lambda that will be used to calculate the value for a given state. This target state parameter in the lambda may or may not be the same as the actual state, as the animation system occasionally needs to look up target values for other states to do proper seeking/tooling preview. Relying on other state than the provided <code>targetState</code> could also result in unnecessary recompositions. Therefore, it is generally considered an error if this <code>targetState</code> parameter is not used.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseOfNonLambdaOffsetOverload<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.offset()</code> is recommended to be used with static arguments only to avoid unnecessary recompositions. <code>Modifier.offset{ }</code> is preferred in the cases where the arguments are backed by a <code>State</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingMaterialAndMaterial3Libraries<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
material and material3 are separate design system libraries that are incompatible with each other, as they have their own distinct theming systems. Using components from both libraries concurrently can cause issues: for example material components will not pick up the correct content color from a material3 container, and vice versa.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterNaming<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should use the name <code>content</code> for the parameter.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterPosition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should place the parameter at the end of the parameter list, so it can be used as a trailing lambda.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MemberExtensionConflict<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When both member and extension declarations are applicable, the resolution takes the member. This also implies that, if an extension existed first, but then a member is added later, the same call-site may end up with different call resolutions depending on target environment. This results in a potential runtime exception if the generated binary (library or app) targets earlier environment (i.e., without the new member, but only extension). More concrete example is found at: <a href="https://issuetracker.google.com/issues/350432371">https://issuetracker.google.com/issues/350432371</a><br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ThreadConstraint<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that a method which expects to be called on a specific thread, is actually called from that thread. For example, calls on methods in widgets should always be made on the UI thread.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>