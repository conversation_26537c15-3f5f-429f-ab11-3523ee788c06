[{"key": "androidx/core/app/ComponentActivity$ExtraData.class", "name": "androidx/core/app/ComponentActivity$ExtraData.class", "size": 1065, "crc": -**********}, {"key": "androidx/core/app/ComponentActivity.class", "name": "androidx/core/app/ComponentActivity.class", "size": 6563, "crc": 852165625}, {"key": "androidx/core/app/MultiWindowModeChangedInfo.class", "name": "androidx/core/app/MultiWindowModeChangedInfo.class", "size": 2068, "crc": **********}, {"key": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "name": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "size": 955, "crc": 44087049}, {"key": "androidx/core/app/OnNewIntentProvider.class", "name": "androidx/core/app/OnNewIntentProvider.class", "size": 846, "crc": -**********}, {"key": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "name": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "size": 990, "crc": -**********}, {"key": "androidx/core/app/OnUserLeaveHintProvider.class", "name": "androidx/core/app/OnUserLeaveHintProvider.class", "size": 713, "crc": 16295445}, {"key": "androidx/core/app/PictureInPictureModeChangedInfo.class", "name": "androidx/core/app/PictureInPictureModeChangedInfo.class", "size": 2098, "crc": 3524212}, {"key": "androidx/core/content/OnConfigurationChangedProvider.class", "name": "androidx/core/content/OnConfigurationChangedProvider.class", "size": 931, "crc": 353118657}, {"key": "androidx/core/content/OnTrimMemoryProvider.class", "name": "androidx/core/content/OnTrimMemoryProvider.class", "size": 827, "crc": **********}, {"key": "androidx/core/os/BuildCompat$Api30Impl.class", "name": "androidx/core/os/BuildCompat$Api30Impl.class", "size": 1073, "crc": **********}, {"key": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "name": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "size": 781, "crc": -**********}, {"key": "androidx/core/os/BuildCompat.class", "name": "androidx/core/os/BuildCompat.class", "size": 7579, "crc": **********}, {"key": "androidx/core/util/Consumer.class", "name": "androidx/core/util/Consumer.class", "size": 559, "crc": -**********}, {"key": "androidx/core/util/Function.class", "name": "androidx/core/util/Function.class", "size": 601, "crc": -**********}, {"key": "androidx/core/util/Pools$Pool.class", "name": "androidx/core/util/Pools$Pool.class", "size": 886, "crc": **********}, {"key": "androidx/core/util/Pools$SimplePool.class", "name": "androidx/core/util/Pools$SimplePool.class", "size": 3066, "crc": 887087881}, {"key": "androidx/core/util/Pools$SynchronizedPool.class", "name": "androidx/core/util/Pools$SynchronizedPool.class", "size": 2341, "crc": -136795928}, {"key": "androidx/core/util/Pools.class", "name": "androidx/core/util/Pools.class", "size": 710, "crc": 1614321609}, {"key": "androidx/core/util/Supplier.class", "name": "androidx/core/util/Supplier.class", "size": 525, "crc": 1418066445}, {"key": "android/support/v4/app/INotificationSideChannel$Default.class", "name": "android/support/v4/app/INotificationSideChannel$Default.class", "size": 1135, "crc": -395248935}, {"key": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "name": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "size": 2463, "crc": -839898487}, {"key": "android/support/v4/app/INotificationSideChannel$Stub.class", "name": "android/support/v4/app/INotificationSideChannel$Stub.class", "size": 2803, "crc": -1214087703}, {"key": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "name": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "size": 1753, "crc": -1278251948}, {"key": "android/support/v4/app/INotificationSideChannel.class", "name": "android/support/v4/app/INotificationSideChannel.class", "size": 1205, "crc": 1388408453}, {"key": "android/support/v4/app/RemoteActionCompatParcelizer.class", "name": "android/support/v4/app/RemoteActionCompatParcelizer.class", "size": 1104, "crc": -587702565}, {"key": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "name": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "size": 1132, "crc": 1368227130}, {"key": "android/support/v4/os/IResultReceiver$Default.class", "name": "android/support/v4/os/IResultReceiver$Default.class", "size": 727, "crc": -1405212393}, {"key": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "size": 1698, "crc": -1328091256}, {"key": "android/support/v4/os/IResultReceiver$Stub.class", "name": "android/support/v4/os/IResultReceiver$Stub.class", "size": 2268, "crc": 649748008}, {"key": "android/support/v4/os/IResultReceiver$_Parcel.class", "name": "android/support/v4/os/IResultReceiver$_Parcel.class", "size": 1714, "crc": 1885126233}, {"key": "android/support/v4/os/IResultReceiver.class", "name": "android/support/v4/os/IResultReceiver.class", "size": 977, "crc": 305690791}, {"key": "android/support/v4/os/IResultReceiver2$Default.class", "name": "android/support/v4/os/IResultReceiver2$Default.class", "size": 731, "crc": -1958681237}, {"key": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "size": 1704, "crc": 876312633}, {"key": "android/support/v4/os/IResultReceiver2$Stub.class", "name": "android/support/v4/os/IResultReceiver2$Stub.class", "size": 2275, "crc": 124292307}, {"key": "android/support/v4/os/IResultReceiver2$_Parcel.class", "name": "android/support/v4/os/IResultReceiver2$_Parcel.class", "size": 1718, "crc": -149676984}, {"key": "android/support/v4/os/IResultReceiver2.class", "name": "android/support/v4/os/IResultReceiver2.class", "size": 983, "crc": 890230104}, {"key": "android/support/v4/os/ResultReceiver$1.class", "name": "android/support/v4/os/ResultReceiver$1.class", "size": 1177, "crc": 1291896286}, {"key": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "name": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "size": 1181, "crc": -989424411}, {"key": "android/support/v4/os/ResultReceiver$MyRunnable.class", "name": "android/support/v4/os/ResultReceiver$MyRunnable.class", "size": 886, "crc": -779159650}, {"key": "android/support/v4/os/ResultReceiver.class", "name": "android/support/v4/os/ResultReceiver.class", "size": 2978, "crc": 2067024131}, {"key": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "name": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "size": 3090, "crc": -12636627}, {"key": "androidx/core/app/ActivityCompat$1.class", "name": "androidx/core/app/ActivityCompat$1.class", "size": 1622, "crc": 1225455941}, {"key": "androidx/core/app/ActivityCompat$Api21Impl.class", "name": "androidx/core/app/ActivityCompat$Api21Impl.class", "size": 1385, "crc": -1567484227}, {"key": "androidx/core/app/ActivityCompat$Api22Impl.class", "name": "androidx/core/app/ActivityCompat$Api22Impl.class", "size": 765, "crc": -783118367}, {"key": "androidx/core/app/ActivityCompat$Api23Impl.class", "name": "androidx/core/app/ActivityCompat$Api23Impl.class", "size": 1442, "crc": -2095613175}, {"key": "androidx/core/app/ActivityCompat$Api28Impl.class", "name": "androidx/core/app/ActivityCompat$Api28Impl.class", "size": 867, "crc": -1216546312}, {"key": "androidx/core/app/ActivityCompat$Api30Impl.class", "name": "androidx/core/app/ActivityCompat$Api30Impl.class", "size": 1549, "crc": 1350452771}, {"key": "androidx/core/app/ActivityCompat$Api31Impl.class", "name": "androidx/core/app/ActivityCompat$Api31Impl.class", "size": 2075, "crc": 1497479999}, {"key": "androidx/core/app/ActivityCompat$Api32Impl.class", "name": "androidx/core/app/ActivityCompat$Api32Impl.class", "size": 839, "crc": 9142853}, {"key": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "name": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "size": 425, "crc": 1567962088}, {"key": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "name": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "size": 621, "crc": -671588751}, {"key": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "name": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "size": 584, "crc": 780710089}, {"key": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "name": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "size": 4428, "crc": -1614036159}, {"key": "androidx/core/app/ActivityCompat.class", "name": "androidx/core/app/ActivityCompat.class", "size": 10691, "crc": -1278311694}, {"key": "androidx/core/app/ActivityManagerCompat.class", "name": "androidx/core/app/ActivityManagerCompat.class", "size": 620, "crc": -1018902847}, {"key": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "name": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "size": 2641, "crc": -1744141227}, {"key": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "size": 1582, "crc": -1814207042}, {"key": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "size": 1301, "crc": -655345617}, {"key": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "size": 1141, "crc": 1739119961}, {"key": "androidx/core/app/ActivityOptionsCompat$Api34Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api34Impl.class", "size": 890, "crc": -378556508}, {"key": "androidx/core/app/ActivityOptionsCompat.class", "name": "androidx/core/app/ActivityOptionsCompat.class", "size": 5510, "crc": 1366438428}, {"key": "androidx/core/app/ActivityRecreator$1.class", "name": "androidx/core/app/ActivityRecreator$1.class", "size": 964, "crc": -1712743558}, {"key": "androidx/core/app/ActivityRecreator$2.class", "name": "androidx/core/app/ActivityRecreator$2.class", "size": 1176, "crc": 1115639159}, {"key": "androidx/core/app/ActivityRecreator$3.class", "name": "androidx/core/app/ActivityRecreator$3.class", "size": 1787, "crc": -1288801421}, {"key": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "name": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "size": 2055, "crc": -1497301040}, {"key": "androidx/core/app/ActivityRecreator.class", "name": "androidx/core/app/ActivityRecreator.class", "size": 6249, "crc": 1351327030}, {"key": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "size": 1309, "crc": 474757778}, {"key": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "size": 1094, "crc": -2051566186}, {"key": "androidx/core/app/AlarmManagerCompat$Api31Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api31Impl.class", "size": 776, "crc": -349117402}, {"key": "androidx/core/app/AlarmManagerCompat.class", "name": "androidx/core/app/AlarmManagerCompat.class", "size": 2388, "crc": 1546920433}, {"key": "androidx/core/app/AppComponentFactory.class", "name": "androidx/core/app/AppComponentFactory.class", "size": 4319, "crc": 941336973}, {"key": "androidx/core/app/AppLaunchChecker.class", "name": "androidx/core/app/AppLaunchChecker.class", "size": 2271, "crc": -405740589}, {"key": "androidx/core/app/AppLocalesStorageHelper.class", "name": "androidx/core/app/AppLocalesStorageHelper.class", "size": 5093, "crc": 1815682277}, {"key": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "size": 1679, "crc": -39919454}, {"key": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "size": 1581, "crc": 1260595091}, {"key": "androidx/core/app/AppOpsManagerCompat.class", "name": "androidx/core/app/AppOpsManagerCompat.class", "size": 3146, "crc": -1004300355}, {"key": "androidx/core/app/BundleCompat.class", "name": "androidx/core/app/BundleCompat.class", "size": 1099, "crc": -1979466448}, {"key": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "name": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "size": 541, "crc": -1240361783}, {"key": "androidx/core/app/CoreComponentFactory.class", "name": "androidx/core/app/CoreComponentFactory.class", "size": 2957, "crc": -1634658571}, {"key": "androidx/core/app/DialogCompat$Api28Impl.class", "name": "androidx/core/app/DialogCompat$Api28Impl.class", "size": 849, "crc": -1446145350}, {"key": "androidx/core/app/DialogCompat.class", "name": "androidx/core/app/DialogCompat.class", "size": 1185, "crc": 1507368120}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "size": 1859, "crc": -1020155968}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "size": 4121, "crc": -1535527374}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "size": 981, "crc": -800692407}, {"key": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "name": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "size": 668, "crc": -1630363674}, {"key": "androidx/core/app/FrameMetricsAggregator.class", "name": "androidx/core/app/FrameMetricsAggregator.class", "size": 2570, "crc": -1426831642}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "size": 1348, "crc": 1546297438}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "size": 705, "crc": -768123921}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "size": 1739, "crc": 1361943853}, {"key": "androidx/core/app/JobIntentService$CommandProcessor.class", "name": "androidx/core/app/JobIntentService$CommandProcessor.class", "size": 1825, "crc": 44568175}, {"key": "androidx/core/app/JobIntentService$CompatJobEngine.class", "name": "androidx/core/app/JobIntentService$CompatJobEngine.class", "size": 448, "crc": 1210592104}, {"key": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "size": 2923, "crc": 373299418}, {"key": "androidx/core/app/JobIntentService$CompatWorkItem.class", "name": "androidx/core/app/JobIntentService$CompatWorkItem.class", "size": 1021, "crc": -287917668}, {"key": "androidx/core/app/JobIntentService$GenericWorkItem.class", "name": "androidx/core/app/JobIntentService$GenericWorkItem.class", "size": 310, "crc": 683365043}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "size": 1478, "crc": 1773720750}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "size": 2592, "crc": 871330440}, {"key": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "size": 1852, "crc": -1663926146}, {"key": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "size": 1443, "crc": -862902624}, {"key": "androidx/core/app/JobIntentService.class", "name": "androidx/core/app/JobIntentService.class", "size": 7130, "crc": -1781495434}, {"key": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "size": 771, "crc": -1095698877}, {"key": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "size": 1056, "crc": -1768607198}, {"key": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "size": 1061, "crc": -1037654519}, {"key": "androidx/core/app/LocaleManagerCompat.class", "name": "androidx/core/app/LocaleManagerCompat.class", "size": 3026, "crc": -1458408911}, {"key": "androidx/core/app/NavUtils.class", "name": "androidx/core/app/NavUtils.class", "size": 5548, "crc": 461358904}, {"key": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "name": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "size": 597, "crc": -1985100633}, {"key": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "size": 4350, "crc": 1487899892}, {"key": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "size": 815, "crc": 1721686020}, {"key": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "size": 1417, "crc": -1496769729}, {"key": "androidx/core/app/NotificationChannelCompat$Builder.class", "name": "androidx/core/app/NotificationChannelCompat$Builder.class", "size": 3517, "crc": -1458413883}, {"key": "androidx/core/app/NotificationChannelCompat.class", "name": "androidx/core/app/NotificationChannelCompat.class", "size": 6989, "crc": -268500644}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "size": 1934, "crc": -347056883}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "size": 1275, "crc": 1629656438}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "size": 1411, "crc": 237511248}, {"key": "androidx/core/app/NotificationChannelGroupCompat.class", "name": "androidx/core/app/NotificationChannelGroupCompat.class", "size": 4884, "crc": -381039034}, {"key": "androidx/core/app/NotificationCompat$1.class", "name": "androidx/core/app/NotificationCompat$1.class", "size": 238, "crc": -598238096}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "size": 1215, "crc": -1553811797}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "size": 1043, "crc": -105329252}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "size": 998, "crc": -1351806817}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "size": 991, "crc": 1971764136}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "size": 986, "crc": 2089502195}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "size": 998, "crc": -930431610}, {"key": "androidx/core/app/NotificationCompat$Action$Builder.class", "name": "androidx/core/app/NotificationCompat$Action$Builder.class", "size": 9182, "crc": 1199783656}, {"key": "androidx/core/app/NotificationCompat$Action$Extender.class", "name": "androidx/core/app/NotificationCompat$Action$Extender.class", "size": 630, "crc": 258657630}, {"key": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "name": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "size": 492, "crc": 271952193}, {"key": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "size": 5051, "crc": 1991372877}, {"key": "androidx/core/app/NotificationCompat$Action.class", "name": "androidx/core/app/NotificationCompat$Action.class", "size": 5711, "crc": -860977567}, {"key": "androidx/core/app/NotificationCompat$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Api20Impl.class", "size": 2205, "crc": 1623103492}, {"key": "androidx/core/app/NotificationCompat$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Api23Impl.class", "size": 881, "crc": -105154841}, {"key": "androidx/core/app/NotificationCompat$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Api24Impl.class", "size": 836, "crc": 2087362200}, {"key": "androidx/core/app/NotificationCompat$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$Api26Impl.class", "size": 1466, "crc": -1784093548}, {"key": "androidx/core/app/NotificationCompat$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Api28Impl.class", "size": 829, "crc": -2064480395}, {"key": "androidx/core/app/NotificationCompat$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Api29Impl.class", "size": 1720, "crc": -810611577}, {"key": "androidx/core/app/NotificationCompat$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Api31Impl.class", "size": 836, "crc": 1371883499}, {"key": "androidx/core/app/NotificationCompat$BadgeIconType.class", "name": "androidx/core/app/NotificationCompat$BadgeIconType.class", "size": 662, "crc": -1600074890}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "size": 1111, "crc": -545779498}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "size": 1668, "crc": -1409543419}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "size": 7392, "crc": 1060212417}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle.class", "size": 3841, "crc": 35028020}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "size": 3541, "crc": 29724948}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "size": 3618, "crc": -1015334329}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "size": 4716, "crc": 1221311312}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "size": 4276, "crc": 1740003656}, {"key": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "size": 1599, "crc": 1606630562}, {"key": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "size": 1006, "crc": 619308074}, {"key": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "size": 1440, "crc": -129429433}, {"key": "androidx/core/app/NotificationCompat$Builder.class", "name": "androidx/core/app/NotificationCompat$Builder.class", "size": 28351, "crc": -1995864438}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "size": 2073, "crc": 271968801}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "size": 1181, "crc": -594887630}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "size": 1759, "crc": 1978332640}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "size": 1133, "crc": -378360914}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "size": 1208, "crc": 933124670}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "size": 3467, "crc": -1624661680}, {"key": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "name": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "size": 734, "crc": 2136238121}, {"key": "androidx/core/app/NotificationCompat$CallStyle.class", "name": "androidx/core/app/NotificationCompat$CallStyle.class", "size": 16289, "crc": -1422591520}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "size": 3348, "crc": 949638961}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "size": 873, "crc": 1097588543}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "size": 2896, "crc": -2031915308}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "size": 2343, "crc": 14226975}, {"key": "androidx/core/app/NotificationCompat$CarExtender.class", "name": "androidx/core/app/NotificationCompat$CarExtender.class", "size": 9177, "crc": 640580976}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "size": 982, "crc": 160216134}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "size": 6895, "crc": -1497583667}, {"key": "androidx/core/app/NotificationCompat$Extender.class", "name": "androidx/core/app/NotificationCompat$Extender.class", "size": 536, "crc": -1922350891}, {"key": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "name": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "size": 672, "crc": 900946520}, {"key": "androidx/core/app/NotificationCompat$InboxStyle.class", "name": "androidx/core/app/NotificationCompat$InboxStyle.class", "size": 4068, "crc": 1516310261}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "size": 1782, "crc": 686713718}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "size": 1287, "crc": 1327479686}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "size": 1318, "crc": -1461609060}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "size": 1723, "crc": -1168865402}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "size": 1414, "crc": 372162559}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "size": 8027, "crc": -2114242576}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle.class", "size": 14136, "crc": -1752017840}, {"key": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "name": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "size": 680, "crc": 1088614023}, {"key": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "name": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "size": 690, "crc": 1857766983}, {"key": "androidx/core/app/NotificationCompat$StreamType.class", "name": "androidx/core/app/NotificationCompat$StreamType.class", "size": 656, "crc": -1750087649}, {"key": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "size": 915, "crc": -1535281602}, {"key": "androidx/core/app/NotificationCompat$Style.class", "name": "androidx/core/app/NotificationCompat$Style.class", "size": 16047, "crc": 1834590369}, {"key": "androidx/core/app/NotificationCompat$TvExtender.class", "name": "androidx/core/app/NotificationCompat$TvExtender.class", "size": 4484, "crc": 444127464}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "size": 2755, "crc": 43986266}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "size": 1278, "crc": -1984401146}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "size": 1157, "crc": -641183678}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "size": 1159, "crc": 879390559}, {"key": "androidx/core/app/NotificationCompat$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$WearableExtender.class", "size": 18030, "crc": 2054152028}, {"key": "androidx/core/app/NotificationCompat.class", "name": "androidx/core/app/NotificationCompat.class", "size": 24034, "crc": 2037328436}, {"key": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "size": 3391, "crc": -1917359535}, {"key": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "size": 2192, "crc": 731501090}, {"key": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "size": 1824, "crc": 1677803318}, {"key": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "size": 2037, "crc": 104739915}, {"key": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "size": 2464, "crc": 636660597}, {"key": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "size": 1428, "crc": -1485297195}, {"key": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "size": 2143, "crc": 130649086}, {"key": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "size": 1408, "crc": 1504746645}, {"key": "androidx/core/app/NotificationCompatBuilder.class", "name": "androidx/core/app/NotificationCompatBuilder.class", "size": 20248, "crc": -1153364976}, {"key": "androidx/core/app/NotificationCompatExtras.class", "name": "androidx/core/app/NotificationCompatExtras.class", "size": 780, "crc": 1230724101}, {"key": "androidx/core/app/NotificationCompatJellybean.class", "name": "androidx/core/app/NotificationCompatJellybean.class", "size": 12786, "crc": 594665231}, {"key": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "name": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "size": 2066, "crc": -508684655}, {"key": "androidx/core/app/NotificationCompatSideChannelService.class", "name": "androidx/core/app/NotificationCompatSideChannelService.class", "size": 2299, "crc": -1251234116}, {"key": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "size": 1430, "crc": 1947225169}, {"key": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "size": 973, "crc": 1366002183}, {"key": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "size": 3604, "crc": 472936966}, {"key": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "size": 992, "crc": 527920353}, {"key": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "size": 1314, "crc": 1790838880}, {"key": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "size": 828, "crc": 1566259666}, {"key": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "name": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "size": 1902, "crc": -151764280}, {"key": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "name": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "size": 693, "crc": 1507578914}, {"key": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "name": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "size": 1039, "crc": -931731844}, {"key": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "name": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "size": 1712, "crc": -1935245986}, {"key": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "name": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "size": 702, "crc": -258989536}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "size": 1146, "crc": -464123942}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "size": 10593, "crc": 1145174528}, {"key": "androidx/core/app/NotificationManagerCompat$Task.class", "name": "androidx/core/app/NotificationManagerCompat$Task.class", "size": 366, "crc": 1719925620}, {"key": "androidx/core/app/NotificationManagerCompat.class", "name": "androidx/core/app/NotificationManagerCompat.class", "size": 18781, "crc": 550168496}, {"key": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "size": 1701, "crc": 854341431}, {"key": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "size": 909, "crc": 1718258872}, {"key": "androidx/core/app/PendingIntentCompat$Flags.class", "name": "androidx/core/app/PendingIntentCompat$Flags.class", "size": 636, "crc": -1100352364}, {"key": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "name": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "size": 2756, "crc": -1234978870}, {"key": "androidx/core/app/PendingIntentCompat.class", "name": "androidx/core/app/PendingIntentCompat.class", "size": 5860, "crc": -325982975}, {"key": "androidx/core/app/Person$Api22Impl.class", "name": "androidx/core/app/Person$Api22Impl.class", "size": 2091, "crc": -911462268}, {"key": "androidx/core/app/Person$Api28Impl.class", "name": "androidx/core/app/Person$Api28Impl.class", "size": 2483, "crc": 1829068246}, {"key": "androidx/core/app/Person$Builder.class", "name": "androidx/core/app/Person$Builder.class", "size": 2157, "crc": 741652667}, {"key": "androidx/core/app/Person.class", "name": "androidx/core/app/Person.class", "size": 6321, "crc": 1677292418}, {"key": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "size": 2126, "crc": -1418873026}, {"key": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "size": 930, "crc": -235726249}, {"key": "androidx/core/app/RemoteActionCompat.class", "name": "androidx/core/app/RemoteActionCompat.class", "size": 4541, "crc": 1001543440}, {"key": "androidx/core/app/RemoteActionCompatParcelizer.class", "name": "androidx/core/app/RemoteActionCompatParcelizer.class", "size": 2304, "crc": -505423669}, {"key": "androidx/core/app/RemoteInput$Api20Impl.class", "name": "androidx/core/app/RemoteInput$Api20Impl.class", "size": 4260, "crc": 1111444981}, {"key": "androidx/core/app/RemoteInput$Api26Impl.class", "name": "androidx/core/app/RemoteInput$Api26Impl.class", "size": 2250, "crc": 1022634827}, {"key": "androidx/core/app/RemoteInput$Api28Impl.class", "name": "androidx/core/app/RemoteInput$Api28Impl.class", "size": 886, "crc": -971979985}, {"key": "androidx/core/app/RemoteInput$Api29Impl.class", "name": "androidx/core/app/RemoteInput$Api29Impl.class", "size": 1105, "crc": 1142187737}, {"key": "androidx/core/app/RemoteInput$Builder.class", "name": "androidx/core/app/RemoteInput$Builder.class", "size": 3002, "crc": 703901060}, {"key": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "name": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "size": 663, "crc": 137218255}, {"key": "androidx/core/app/RemoteInput$Source.class", "name": "androidx/core/app/RemoteInput$Source.class", "size": 627, "crc": 1225634699}, {"key": "androidx/core/app/RemoteInput.class", "name": "androidx/core/app/RemoteInput.class", "size": 10690, "crc": 100448154}, {"key": "androidx/core/app/ServiceCompat$Api24Impl.class", "name": "androidx/core/app/ServiceCompat$Api24Impl.class", "size": 757, "crc": 1941199069}, {"key": "androidx/core/app/ServiceCompat$Api29Impl.class", "name": "androidx/core/app/ServiceCompat$Api29Impl.class", "size": 993, "crc": -1810633865}, {"key": "androidx/core/app/ServiceCompat$Api34Impl.class", "name": "androidx/core/app/ServiceCompat$Api34Impl.class", "size": 997, "crc": -1999402045}, {"key": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "name": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "size": 659, "crc": 655432038}, {"key": "androidx/core/app/ServiceCompat.class", "name": "androidx/core/app/ServiceCompat.class", "size": 1777, "crc": -394635011}, {"key": "androidx/core/app/ShareCompat$IntentBuilder.class", "name": "androidx/core/app/ShareCompat$IntentBuilder.class", "size": 8678, "crc": 1276398534}, {"key": "androidx/core/app/ShareCompat$IntentReader.class", "name": "androidx/core/app/ShareCompat$IntentReader.class", "size": 7016, "crc": -675110014}, {"key": "androidx/core/app/ShareCompat.class", "name": "androidx/core/app/ShareCompat.class", "size": 5904, "crc": 54973738}, {"key": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "name": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "size": 317, "crc": 1217450772}, {"key": "androidx/core/app/SharedElementCallback.class", "name": "androidx/core/app/SharedElementCallback.class", "size": 7188, "crc": 376381531}, {"key": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "name": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "size": 385, "crc": 2113967134}, {"key": "androidx/core/app/TaskStackBuilder.class", "name": "androidx/core/app/TaskStackBuilder.class", "size": 6556, "crc": 1501909556}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "size": 868, "crc": 1227855709}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "size": 1775, "crc": -1351993899}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "size": 2239, "crc": -900432979}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "size": 1116, "crc": 1361675367}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "size": 992, "crc": -957843627}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "size": 1945, "crc": **********}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "size": 2617, "crc": 216898320}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "size": 1181, "crc": -**********}, {"key": "androidx/core/content/ContentProviderCompat.class", "name": "androidx/core/content/ContentProviderCompat.class", "size": 986, "crc": -917373858}, {"key": "androidx/core/content/ContentResolverCompat.class", "name": "androidx/core/content/ContentResolverCompat.class", "size": 2351, "crc": 825943966}, {"key": "androidx/core/content/ContextCompat$Api21Impl.class", "name": "androidx/core/content/ContextCompat$Api21Impl.class", "size": 1106, "crc": 969564630}, {"key": "androidx/core/content/ContextCompat$Api23Impl.class", "name": "androidx/core/content/ContextCompat$Api23Impl.class", "size": 1521, "crc": 817296793}, {"key": "androidx/core/content/ContextCompat$Api24Impl.class", "name": "androidx/core/content/ContextCompat$Api24Impl.class", "size": 1123, "crc": -384917672}, {"key": "androidx/core/content/ContextCompat$Api26Impl.class", "name": "androidx/core/content/ContextCompat$Api26Impl.class", "size": 1971, "crc": 867798444}, {"key": "androidx/core/content/ContextCompat$Api28Impl.class", "name": "androidx/core/content/ContextCompat$Api28Impl.class", "size": 809, "crc": -241986563}, {"key": "androidx/core/content/ContextCompat$Api30Impl.class", "name": "androidx/core/content/ContextCompat$Api30Impl.class", "size": 2153, "crc": -1332801839}, {"key": "androidx/core/content/ContextCompat$Api33Impl.class", "name": "androidx/core/content/ContextCompat$Api33Impl.class", "size": 1337, "crc": 132777324}, {"key": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "name": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "size": 4358, "crc": **********}, {"key": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "name": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "size": 658, "crc": 794825751}, {"key": "androidx/core/content/ContextCompat.class", "name": "androidx/core/content/ContextCompat.class", "size": 12930, "crc": **********}, {"key": "androidx/core/content/FileProvider$Api21Impl.class", "name": "androidx/core/content/FileProvider$Api21Impl.class", "size": 782, "crc": **********}, {"key": "androidx/core/content/FileProvider$PathStrategy.class", "name": "androidx/core/content/FileProvider$PathStrategy.class", "size": 346, "crc": 770878075}, {"key": "androidx/core/content/FileProvider$SimplePathStrategy.class", "name": "androidx/core/content/FileProvider$SimplePathStrategy.class", "size": 4719, "crc": -**********}, {"key": "androidx/core/content/FileProvider.class", "name": "androidx/core/content/FileProvider.class", "size": 13579, "crc": 297337883}, {"key": "androidx/core/content/IntentCompat$Api33Impl.class", "name": "androidx/core/content/IntentCompat$Api33Impl.class", "size": 2558, "crc": -**********}, {"key": "androidx/core/content/IntentCompat.class", "name": "androidx/core/content/IntentCompat.class", "size": 5528, "crc": 9042673}, {"key": "androidx/core/content/IntentSanitizer$1.class", "name": "androidx/core/content/IntentSanitizer$1.class", "size": 237, "crc": -61702665}, {"key": "androidx/core/content/IntentSanitizer$Api29Impl.class", "name": "androidx/core/content/IntentSanitizer$Api29Impl.class", "size": 1051, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$Api31Impl.class", "name": "androidx/core/content/IntentSanitizer$Api31Impl.class", "size": 1741, "crc": -**********}, {"key": "androidx/core/content/IntentSanitizer$Builder.class", "name": "androidx/core/content/IntentSanitizer$Builder.class", "size": 13691, "crc": -122430816}, {"key": "androidx/core/content/IntentSanitizer.class", "name": "androidx/core/content/IntentSanitizer.class", "size": 13153, "crc": -1377332162}, {"key": "androidx/core/content/LocusIdCompat$Api29Impl.class", "name": "androidx/core/content/LocusIdCompat$Api29Impl.class", "size": 1024, "crc": 89574739}, {"key": "androidx/core/content/LocusIdCompat.class", "name": "androidx/core/content/LocusIdCompat.class", "size": 2782, "crc": 412886872}, {"key": "androidx/core/content/MimeTypeFilter.class", "name": "androidx/core/content/MimeTypeFilter.class", "size": 2742, "crc": 147689054}, {"key": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "name": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "size": 1002, "crc": -2001595271}, {"key": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "name": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "size": 691, "crc": 2041315114}, {"key": "androidx/core/content/PackageManagerCompat.class", "name": "androidx/core/content/PackageManagerCompat.class", "size": 6194, "crc": 1633769047}, {"key": "androidx/core/content/PermissionChecker$PermissionResult.class", "name": "androidx/core/content/PermissionChecker$PermissionResult.class", "size": 673, "crc": -666488629}, {"key": "androidx/core/content/PermissionChecker.class", "name": "androidx/core/content/PermissionChecker.class", "size": 2862, "crc": 1940901641}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "size": 1116, "crc": 587673952}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "size": 1381, "crc": -494629376}, {"key": "androidx/core/content/SharedPreferencesCompat.class", "name": "androidx/core/content/SharedPreferencesCompat.class", "size": 532, "crc": 1567352910}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "size": 1290, "crc": 1695615859}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "size": 1604, "crc": 843169627}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "size": 1591, "crc": -668859029}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "size": 1862, "crc": 1397470612}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "size": 4184, "crc": -1517503401}, {"key": "androidx/core/content/UnusedAppRestrictionsConstants.class", "name": "androidx/core/content/UnusedAppRestrictionsConstants.class", "size": 588, "crc": -1244214169}, {"key": "androidx/core/content/UriMatcherCompat.class", "name": "androidx/core/content/UriMatcherCompat.class", "size": 1524, "crc": 1164334199}, {"key": "androidx/core/content/pm/ActivityInfoCompat.class", "name": "androidx/core/content/pm/ActivityInfoCompat.class", "size": 493, "crc": -1364477614}, {"key": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "size": 1924, "crc": 1118492697}, {"key": "androidx/core/content/pm/PackageInfoCompat.class", "name": "androidx/core/content/pm/PackageInfoCompat.class", "size": 6094, "crc": -587297962}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "size": 929, "crc": -417971139}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "size": 663, "crc": 1629059509}, {"key": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "name": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "size": 739, "crc": 1522065108}, {"key": "androidx/core/content/pm/PermissionInfoCompat.class", "name": "androidx/core/content/pm/PermissionInfoCompat.class", "size": 1364, "crc": 1297374523}, {"key": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "name": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "size": 1703, "crc": 427897560}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "size": 1000, "crc": 1878927332}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "size": 12666, "crc": -1317622191}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "size": 664, "crc": 282044856}, {"key": "androidx/core/content/pm/ShortcutInfoCompat.class", "name": "androidx/core/content/pm/ShortcutInfoCompat.class", "size": 12893, "crc": -1893822061}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "size": 1718, "crc": 780394003}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "size": 1544, "crc": -976705994}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "size": 1316, "crc": 2121141673}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "size": 1498, "crc": -1798955755}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "size": 695, "crc": 856990973}, {"key": "androidx/core/content/pm/ShortcutManagerCompat.class", "name": "androidx/core/content/pm/ShortcutManagerCompat.class", "size": 20119, "crc": -583484979}, {"key": "androidx/core/content/pm/ShortcutXmlParser.class", "name": "androidx/core/content/pm/ShortcutXmlParser.class", "size": 5957, "crc": 443382465}, {"key": "androidx/core/content/res/CamColor.class", "name": "androidx/core/content/res/CamColor.class", "size": 10209, "crc": 707293299}, {"key": "androidx/core/content/res/CamUtils.class", "name": "androidx/core/content/res/CamUtils.class", "size": 3152, "crc": 1105906618}, {"key": "androidx/core/content/res/ColorStateListInflaterCompat.class", "name": "androidx/core/content/res/ColorStateListInflaterCompat.class", "size": 8167, "crc": 271679968}, {"key": "androidx/core/content/res/ComplexColorCompat.class", "name": "androidx/core/content/res/ComplexColorCompat.class", "size": 5222, "crc": -1398844043}, {"key": "androidx/core/content/res/ConfigurationHelper.class", "name": "androidx/core/content/res/ConfigurationHelper.class", "size": 755, "crc": -999867328}, {"key": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "name": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "size": 854, "crc": -1930670641}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "size": 287, "crc": -39793732}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "size": 454, "crc": 1823731370}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "size": 1265, "crc": -**********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "size": 1652, "crc": **********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "size": 1996, "crc": -537995543}, {"key": "androidx/core/content/res/FontResourcesParserCompat.class", "name": "androidx/core/content/res/FontResourcesParserCompat.class", "size": 8708, "crc": -346635419}, {"key": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "name": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "size": 1735, "crc": 246697236}, {"key": "androidx/core/content/res/GradientColorInflaterCompat.class", "name": "androidx/core/content/res/GradientColorInflaterCompat.class", "size": 7737, "crc": 594254899}, {"key": "androidx/core/content/res/GrowingArrayUtils.class", "name": "androidx/core/content/res/GrowingArrayUtils.class", "size": 2719, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "size": 1412, "crc": 393460819}, {"key": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "size": 1484, "crc": 1408796082}, {"key": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "size": 925, "crc": 1307711375}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "size": 1305, "crc": -1312155530}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "size": 1493, "crc": -231062402}, {"key": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "name": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "size": 2653, "crc": -1835982375}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "size": 2299, "crc": -2080577232}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "size": 1036, "crc": 626888944}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "size": 1131, "crc": 298456629}, {"key": "androidx/core/content/res/ResourcesCompat.class", "name": "androidx/core/content/res/ResourcesCompat.class", "size": 14294, "crc": 1525920558}, {"key": "androidx/core/content/res/TypedArrayUtils.class", "name": "androidx/core/content/res/TypedArrayUtils.class", "size": 8994, "crc": 743892670}, {"key": "androidx/core/content/res/ViewingConditions.class", "name": "androidx/core/content/res/ViewingConditions.class", "size": 3538, "crc": -131663576}, {"key": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "size": 855, "crc": 50352982}, {"key": "androidx/core/database/CursorWindowCompat.class", "name": "androidx/core/database/CursorWindowCompat.class", "size": 1020, "crc": 989448132}, {"key": "androidx/core/database/DatabaseUtilsCompat.class", "name": "androidx/core/database/DatabaseUtilsCompat.class", "size": 1371, "crc": -2044651927}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "size": 889, "crc": -948675496}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "size": 903, "crc": 1059320687}, {"key": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "size": 2569, "crc": 1909692832}, {"key": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "size": 878, "crc": 676631431}, {"key": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "size": 1082, "crc": -1864651270}, {"key": "androidx/core/graphics/BitmapCompat.class", "name": "androidx/core/graphics/BitmapCompat.class", "size": 6017, "crc": -484315252}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "size": 937, "crc": 652344203}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat.class", "size": 1837, "crc": -1703162937}, {"key": "androidx/core/graphics/BlendModeCompat.class", "name": "androidx/core/graphics/BlendModeCompat.class", "size": 2888, "crc": -645227784}, {"key": "androidx/core/graphics/BlendModeUtils$1.class", "name": "androidx/core/graphics/BlendModeUtils$1.class", "size": 2083, "crc": 1721728409}, {"key": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "name": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "size": 2166, "crc": 1973059920}, {"key": "androidx/core/graphics/BlendModeUtils.class", "name": "androidx/core/graphics/BlendModeUtils.class", "size": 1725, "crc": -1150213795}, {"key": "androidx/core/graphics/ColorUtils$Api26Impl.class", "name": "androidx/core/graphics/ColorUtils$Api26Impl.class", "size": 2133, "crc": -1767282957}, {"key": "androidx/core/graphics/ColorUtils.class", "name": "androidx/core/graphics/ColorUtils.class", "size": 11354, "crc": 572223509}, {"key": "androidx/core/graphics/Insets$Api29Impl.class", "name": "androidx/core/graphics/Insets$Api29Impl.class", "size": 741, "crc": -861437350}, {"key": "androidx/core/graphics/Insets.class", "name": "androidx/core/graphics/Insets.class", "size": 3797, "crc": 1611887429}, {"key": "androidx/core/graphics/PaintCompat$Api23Impl.class", "name": "androidx/core/graphics/PaintCompat$Api23Impl.class", "size": 813, "crc": -3322387}, {"key": "androidx/core/graphics/PaintCompat$Api29Impl.class", "name": "androidx/core/graphics/PaintCompat$Api29Impl.class", "size": 869, "crc": 1376448595}, {"key": "androidx/core/graphics/PaintCompat.class", "name": "androidx/core/graphics/PaintCompat.class", "size": 4190, "crc": -1948255028}, {"key": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "name": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "size": 492, "crc": -1663845048}, {"key": "androidx/core/graphics/PathParser$PathDataNode.class", "name": "androidx/core/graphics/PathParser$PathDataNode.class", "size": 9633, "crc": -1245652526}, {"key": "androidx/core/graphics/PathParser.class", "name": "androidx/core/graphics/PathParser.class", "size": 8089, "crc": -1939474256}, {"key": "androidx/core/graphics/PathSegment.class", "name": "androidx/core/graphics/PathSegment.class", "size": 2433, "crc": 1148524092}, {"key": "androidx/core/graphics/PathUtils$Api26Impl.class", "name": "androidx/core/graphics/PathUtils$Api26Impl.class", "size": 764, "crc": -635021751}, {"key": "androidx/core/graphics/PathUtils.class", "name": "androidx/core/graphics/PathUtils.class", "size": 2168, "crc": 1645633006}, {"key": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "name": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "size": 1711, "crc": -323164186}, {"key": "androidx/core/graphics/TypefaceCompat.class", "name": "androidx/core/graphics/TypefaceCompat.class", "size": 10183, "crc": 1966790811}, {"key": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "size": 9920, "crc": 1427569578}, {"key": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "size": 8166, "crc": 541108707}, {"key": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "size": 13353, "crc": -1021628}, {"key": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "size": 3170, "crc": 1765335205}, {"key": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "size": 8266, "crc": -766524581}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "size": 1680, "crc": 2020885920}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "size": 1989, "crc": 992270278}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "size": 1990, "crc": 1646487761}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "size": 446, "crc": 1807309830}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "size": 10576, "crc": 126501558}, {"key": "androidx/core/graphics/TypefaceCompatUtil.class", "name": "androidx/core/graphics/TypefaceCompatUtil.class", "size": 7720, "crc": -590393442}, {"key": "androidx/core/graphics/WeightTypefaceApi14.class", "name": "androidx/core/graphics/WeightTypefaceApi14.class", "size": 5229, "crc": -489331575}, {"key": "androidx/core/graphics/WeightTypefaceApi21.class", "name": "androidx/core/graphics/WeightTypefaceApi21.class", "size": 6056, "crc": 458843757}, {"key": "androidx/core/graphics/WeightTypefaceApi26.class", "name": "androidx/core/graphics/WeightTypefaceApi26.class", "size": 5541, "crc": -1847966844}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "size": 3290, "crc": -189709534}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "size": 1011, "crc": 274517592}, {"key": "androidx/core/graphics/drawable/DrawableCompat.class", "name": "androidx/core/graphics/drawable/DrawableCompat.class", "size": 8181, "crc": 1830796176}, {"key": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "size": 7848, "crc": -1288128777}, {"key": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "size": 1260, "crc": 1316781001}, {"key": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "size": 1176, "crc": 1899525487}, {"key": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "size": 802, "crc": -2007874236}, {"key": "androidx/core/graphics/drawable/IconCompat$IconType.class", "name": "androidx/core/graphics/drawable/IconCompat$IconType.class", "size": 643, "crc": 1843019074}, {"key": "androidx/core/graphics/drawable/IconCompat.class", "name": "androidx/core/graphics/drawable/IconCompat.class", "size": 23766, "crc": -1057666863}, {"key": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "name": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "size": 2391, "crc": -1370437693}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "size": 7892, "crc": -626571484}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "size": 1864, "crc": 468816631}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "size": 1631, "crc": 1462706089}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "size": 2653, "crc": 1800860871}, {"key": "androidx/core/graphics/drawable/TintAwareDrawable.class", "name": "androidx/core/graphics/drawable/TintAwareDrawable.class", "size": 745, "crc": 1348044187}, {"key": "androidx/core/graphics/drawable/WrappedDrawable.class", "name": "androidx/core/graphics/drawable/WrappedDrawable.class", "size": 560, "crc": -747113056}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "size": 8945, "crc": -231972482}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "size": 4146, "crc": -859088974}, {"key": "androidx/core/graphics/drawable/WrappedDrawableState.class", "name": "androidx/core/graphics/drawable/WrappedDrawableState.class", "size": 2151, "crc": 1765756452}, {"key": "androidx/core/hardware/display/DisplayManagerCompat.class", "name": "androidx/core/hardware/display/DisplayManagerCompat.class", "size": 1636, "crc": 989697280}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "size": 2815, "crc": 439340217}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "size": 4151, "crc": -1917590039}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "size": 1435, "crc": -68738431}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "size": 1136, "crc": 2102188991}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "size": 1522, "crc": -1616160736}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "size": 5413, "crc": 36992065}, {"key": "androidx/core/internal/package-info.class", "name": "androidx/core/internal/package-info.class", "size": 404, "crc": -1162714476}, {"key": "androidx/core/internal/view/SupportMenu.class", "name": "androidx/core/internal/view/SupportMenu.class", "size": 765, "crc": 1726273822}, {"key": "androidx/core/internal/view/SupportMenuItem.class", "name": "androidx/core/internal/view/SupportMenuItem.class", "size": 2836, "crc": 1950125442}, {"key": "androidx/core/internal/view/SupportSubMenu.class", "name": "androidx/core/internal/view/SupportSubMenu.class", "size": 488, "crc": 341992331}, {"key": "androidx/core/location/GnssStatusCompat$Callback.class", "name": "androidx/core/location/GnssStatusCompat$Callback.class", "size": 1011, "crc": -106236767}, {"key": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "name": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "size": 661, "crc": 600754332}, {"key": "androidx/core/location/GnssStatusCompat.class", "name": "androidx/core/location/GnssStatusCompat.class", "size": 2791, "crc": 821246616}, {"key": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "size": 988, "crc": -2010772826}, {"key": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "size": 982, "crc": -494001482}, {"key": "androidx/core/location/GnssStatusWrapper.class", "name": "androidx/core/location/GnssStatusWrapper.class", "size": 3112, "crc": -197759893}, {"key": "androidx/core/location/GpsStatusWrapper.class", "name": "androidx/core/location/GpsStatusWrapper.class", "size": 4956, "crc": 1004775428}, {"key": "androidx/core/location/LocationCompat$Api26Impl.class", "name": "androidx/core/location/LocationCompat$Api26Impl.class", "size": 3437, "crc": 420740957}, {"key": "androidx/core/location/LocationCompat$Api28Impl.class", "name": "androidx/core/location/LocationCompat$Api28Impl.class", "size": 4116, "crc": 1597269846}, {"key": "androidx/core/location/LocationCompat$Api29Impl.class", "name": "androidx/core/location/LocationCompat$Api29Impl.class", "size": 1476, "crc": -764363779}, {"key": "androidx/core/location/LocationCompat$Api33Impl.class", "name": "androidx/core/location/LocationCompat$Api33Impl.class", "size": 988, "crc": 1243675708}, {"key": "androidx/core/location/LocationCompat$Api34Impl.class", "name": "androidx/core/location/LocationCompat$Api34Impl.class", "size": 1815, "crc": -122363606}, {"key": "androidx/core/location/LocationCompat.class", "name": "androidx/core/location/LocationCompat.class", "size": 9990, "crc": 1409851656}, {"key": "androidx/core/location/LocationListenerCompat.class", "name": "androidx/core/location/LocationListenerCompat.class", "size": 1553, "crc": 1096933018}, {"key": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "size": 3811, "crc": -1417582786}, {"key": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "size": 4045, "crc": 1373829472}, {"key": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "size": 1168, "crc": -1209055492}, {"key": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "size": 5848, "crc": -1177795251}, {"key": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "size": 2316, "crc": -1081442992}, {"key": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "name": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "size": 4695, "crc": -1081223508}, {"key": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "name": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "size": 1188, "crc": 1084022117}, {"key": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "size": 2936, "crc": 1804067361}, {"key": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "size": 1738, "crc": 1406909359}, {"key": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "size": 4297, "crc": 790129699}, {"key": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "name": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "size": 1595, "crc": 1528455965}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "size": 1396, "crc": 1322207943}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "size": 5175, "crc": 2059105653}, {"key": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "size": 4058, "crc": -1140549583}, {"key": "androidx/core/location/LocationManagerCompat.class", "name": "androidx/core/location/LocationManagerCompat.class", "size": 22671, "crc": -1643195235}, {"key": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "size": 3015, "crc": -408833996}, {"key": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "size": 1654, "crc": 1110351924}, {"key": "androidx/core/location/LocationRequestCompat$Builder.class", "name": "androidx/core/location/LocationRequestCompat$Builder.class", "size": 3768, "crc": 1236629499}, {"key": "androidx/core/location/LocationRequestCompat$Quality.class", "name": "androidx/core/location/LocationRequestCompat$Quality.class", "size": 656, "crc": -1304604809}, {"key": "androidx/core/location/LocationRequestCompat.class", "name": "androidx/core/location/LocationRequestCompat.class", "size": 4921, "crc": 923753816}, {"key": "androidx/core/math/MathUtils.class", "name": "androidx/core/math/MathUtils.class", "size": 2973, "crc": -1763529000}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "size": 837, "crc": 2023157836}, {"key": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "name": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "size": 705, "crc": 716344203}, {"key": "androidx/core/net/ConnectivityManagerCompat.class", "name": "androidx/core/net/ConnectivityManagerCompat.class", "size": 2169, "crc": -1440532548}, {"key": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "size": 2647, "crc": 1217154937}, {"key": "androidx/core/net/DatagramSocketWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper.class", "size": 694, "crc": 1450467363}, {"key": "androidx/core/net/MailTo.class", "name": "androidx/core/net/MailTo.class", "size": 4777, "crc": 716149100}, {"key": "androidx/core/net/ParseException.class", "name": "androidx/core/net/ParseException.class", "size": 531, "crc": -1332245760}, {"key": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "name": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "size": 933, "crc": -2134102274}, {"key": "androidx/core/net/TrafficStatsCompat.class", "name": "androidx/core/net/TrafficStatsCompat.class", "size": 2384, "crc": 2031265214}, {"key": "androidx/core/net/UriCompat.class", "name": "androidx/core/net/UriCompat.class", "size": 1861, "crc": 957459528}, {"key": "androidx/core/os/BundleCompat$Api33Impl.class", "name": "androidx/core/os/BundleCompat$Api33Impl.class", "size": 2924, "crc": 610687349}, {"key": "androidx/core/os/BundleCompat.class", "name": "androidx/core/os/BundleCompat.class", "size": 4237, "crc": 1020338323}, {"key": "androidx/core/os/CancellationSignal$OnCancelListener.class", "name": "androidx/core/os/CancellationSignal$OnCancelListener.class", "size": 267, "crc": 1768443177}, {"key": "androidx/core/os/CancellationSignal.class", "name": "androidx/core/os/CancellationSignal.class", "size": 2560, "crc": -960380168}, {"key": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "name": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "size": 1305, "crc": -537722556}, {"key": "androidx/core/os/ConfigurationCompat.class", "name": "androidx/core/os/ConfigurationCompat.class", "size": 1671, "crc": 885489852}, {"key": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "size": 747, "crc": 2058380209}, {"key": "androidx/core/os/EnvironmentCompat.class", "name": "androidx/core/os/EnvironmentCompat.class", "size": 1053, "crc": 1913180907}, {"key": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "name": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "size": 1394, "crc": 817974452}, {"key": "androidx/core/os/ExecutorCompat.class", "name": "androidx/core/os/ExecutorCompat.class", "size": 732, "crc": 813340522}, {"key": "androidx/core/os/HandlerCompat$Api28Impl.class", "name": "androidx/core/os/HandlerCompat$Api28Impl.class", "size": 1266, "crc": 1188911207}, {"key": "androidx/core/os/HandlerCompat$Api29Impl.class", "name": "androidx/core/os/HandlerCompat$Api29Impl.class", "size": 750, "crc": -97291405}, {"key": "androidx/core/os/HandlerCompat.class", "name": "androidx/core/os/HandlerCompat.class", "size": 4962, "crc": -1897818136}, {"key": "androidx/core/os/LocaleListCompat$Api21Impl.class", "name": "androidx/core/os/LocaleListCompat$Api21Impl.class", "size": 1997, "crc": -1056214764}, {"key": "androidx/core/os/LocaleListCompat$Api24Impl.class", "name": "androidx/core/os/LocaleListCompat$Api24Impl.class", "size": 969, "crc": 2114069988}, {"key": "androidx/core/os/LocaleListCompat.class", "name": "androidx/core/os/LocaleListCompat.class", "size": 5727, "crc": 504905637}, {"key": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "name": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "size": 784, "crc": -803210832}, {"key": "androidx/core/os/LocaleListCompatWrapper.class", "name": "androidx/core/os/LocaleListCompatWrapper.class", "size": 6913, "crc": 1732702063}, {"key": "androidx/core/os/LocaleListInterface.class", "name": "androidx/core/os/LocaleListInterface.class", "size": 673, "crc": -1204215516}, {"key": "androidx/core/os/LocaleListPlatformWrapper.class", "name": "androidx/core/os/LocaleListPlatformWrapper.class", "size": 1937, "crc": 1832161889}, {"key": "androidx/core/os/MessageCompat$Api22Impl.class", "name": "androidx/core/os/MessageCompat$Api22Impl.class", "size": 884, "crc": 1780742617}, {"key": "androidx/core/os/MessageCompat.class", "name": "androidx/core/os/MessageCompat.class", "size": 1393, "crc": 1288633183}, {"key": "androidx/core/os/OperationCanceledException.class", "name": "androidx/core/os/OperationCanceledException.class", "size": 734, "crc": 1084133341}, {"key": "androidx/core/os/ParcelCompat$Api29Impl.class", "name": "androidx/core/os/ParcelCompat$Api29Impl.class", "size": 1234, "crc": 695637281}, {"key": "androidx/core/os/ParcelCompat$Api30Impl.class", "name": "androidx/core/os/ParcelCompat$Api30Impl.class", "size": 1173, "crc": -1581830279}, {"key": "androidx/core/os/ParcelCompat$Api33Impl.class", "name": "androidx/core/os/ParcelCompat$Api33Impl.class", "size": 5851, "crc": 359949473}, {"key": "androidx/core/os/ParcelCompat.class", "name": "androidx/core/os/ParcelCompat.class", "size": 9011, "crc": 1763807924}, {"key": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "name": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "size": 1865, "crc": -1411295453}, {"key": "androidx/core/os/ParcelableCompat.class", "name": "androidx/core/os/ParcelableCompat.class", "size": 1172, "crc": 276111255}, {"key": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "name": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "size": 521, "crc": -1661855711}, {"key": "androidx/core/os/ProcessCompat$Api19Impl.class", "name": "androidx/core/os/ProcessCompat$Api19Impl.class", "size": 1688, "crc": -612991505}, {"key": "androidx/core/os/ProcessCompat$Api24Impl.class", "name": "androidx/core/os/ProcessCompat$Api24Impl.class", "size": 628, "crc": -36967095}, {"key": "androidx/core/os/ProcessCompat.class", "name": "androidx/core/os/ProcessCompat.class", "size": 705, "crc": 1617640224}, {"key": "androidx/core/os/TraceCompat$Api29Impl.class", "name": "androidx/core/os/TraceCompat$Api29Impl.class", "size": 1104, "crc": 527588178}, {"key": "androidx/core/os/TraceCompat.class", "name": "androidx/core/os/TraceCompat.class", "size": 3632, "crc": -126247558}, {"key": "androidx/core/os/UserHandleCompat$Api24Impl.class", "name": "androidx/core/os/UserHandleCompat$Api24Impl.class", "size": 712, "crc": 376925078}, {"key": "androidx/core/os/UserHandleCompat.class", "name": "androidx/core/os/UserHandleCompat.class", "size": 2995, "crc": -**********}, {"key": "androidx/core/os/UserManagerCompat$Api24Impl.class", "name": "androidx/core/os/UserManagerCompat$Api24Impl.class", "size": 858, "crc": -689420772}, {"key": "androidx/core/os/UserManagerCompat.class", "name": "androidx/core/os/UserManagerCompat.class", "size": 785, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$1.class", "name": "androidx/core/provider/CallbackWithHandler$1.class", "size": 1177, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$2.class", "name": "androidx/core/provider/CallbackWithHandler$2.class", "size": 1101, "crc": -579370637}, {"key": "androidx/core/provider/CallbackWithHandler.class", "name": "androidx/core/provider/CallbackWithHandler.class", "size": 2578, "crc": -255845993}, {"key": "androidx/core/provider/CalleeHandler.class", "name": "androidx/core/provider/CalleeHandler.class", "size": 748, "crc": -356584081}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "size": 539, "crc": -744260981}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "size": 2243, "crc": -373053568}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "size": 1243, "crc": -191673428}, {"key": "androidx/core/provider/DocumentsContractCompat.class", "name": "androidx/core/provider/DocumentsContractCompat.class", "size": 4223, "crc": -**********}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapper.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapper.class", "size": 1164, "crc": -**********}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapperApi16Impl.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapperApi16Impl.class", "size": 1965, "crc": -711959768}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapperApi24Impl.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapperApi24Impl.class", "size": 2044, "crc": -**********}, {"key": "androidx/core/provider/FontProvider.class", "name": "androidx/core/provider/FontProvider.class", "size": 9818, "crc": -**********}, {"key": "androidx/core/provider/FontRequest.class", "name": "androidx/core/provider/FontRequest.class", "size": 4062, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker$1.class", "name": "androidx/core/provider/FontRequestWorker$1.class", "size": 1628, "crc": **********}, {"key": "androidx/core/provider/FontRequestWorker$2.class", "name": "androidx/core/provider/FontRequestWorker$2.class", "size": 1631, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker$3.class", "name": "androidx/core/provider/FontRequestWorker$3.class", "size": 1940, "crc": 129325094}, {"key": "androidx/core/provider/FontRequestWorker$4.class", "name": "androidx/core/provider/FontRequestWorker$4.class", "size": 2067, "crc": -757734499}, {"key": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "name": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "size": 1197, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker.class", "name": "androidx/core/provider/FontRequestWorker.class", "size": 8149, "crc": **********}, {"key": "androidx/core/provider/FontsContractCompat$Columns.class", "name": "androidx/core/provider/FontsContractCompat$Columns.class", "size": 1073, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "name": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "size": 1802, "crc": 805654133}, {"key": "androidx/core/provider/FontsContractCompat$FontInfo.class", "name": "androidx/core/provider/FontsContractCompat$FontInfo.class", "size": 2142, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "size": 809, "crc": -566122054}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "size": 1726, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat.class", "name": "androidx/core/provider/FontsContractCompat.class", "size": 6954, "crc": -837802792}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "size": 989, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "size": 1108, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "name": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "size": 1416, "crc": **********}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "size": 1134, "crc": -721637920}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "size": 2086, "crc": -361715969}, {"key": "androidx/core/provider/RequestExecutor.class", "name": "androidx/core/provider/RequestExecutor.class", "size": 4032, "crc": 45623863}, {"key": "androidx/core/provider/SelfDestructiveThread$1.class", "name": "androidx/core/provider/SelfDestructiveThread$1.class", "size": 1098, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$2$1.class", "name": "androidx/core/provider/SelfDestructiveThread$2$1.class", "size": 1084, "crc": -**********}, {"key": "androidx/core/provider/SelfDestructiveThread$2.class", "name": "androidx/core/provider/SelfDestructiveThread$2.class", "size": 1835, "crc": -507995055}, {"key": "androidx/core/provider/SelfDestructiveThread$3.class", "name": "androidx/core/provider/SelfDestructiveThread$3.class", "size": 1943, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "name": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "size": 379, "crc": -538938372}, {"key": "androidx/core/provider/SelfDestructiveThread.class", "name": "androidx/core/provider/SelfDestructiveThread.class", "size": 6254, "crc": **********}, {"key": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "name": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "size": 2541, "crc": 1624604}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "size": 969, "crc": **********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "size": 985, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "size": 387, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat.class", "name": "androidx/core/service/quicksettings/TileServiceCompat.class", "size": 2414, "crc": 1757355155}, {"key": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "name": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "size": 758, "crc": 1419660002}, {"key": "androidx/core/telephony/SubscriptionManagerCompat.class", "name": "androidx/core/telephony/SubscriptionManagerCompat.class", "size": 1698, "crc": 1557138825}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "size": 1087, "crc": 1091714028}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "size": 1054, "crc": -1566470395}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "size": 835, "crc": 1008375281}, {"key": "androidx/core/telephony/TelephonyManagerCompat.class", "name": "androidx/core/telephony/TelephonyManagerCompat.class", "size": 2906, "crc": 1474125055}, {"key": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "name": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "size": 2186, "crc": -1878492408}, {"key": "androidx/core/telephony/mbms/MbmsHelper.class", "name": "androidx/core/telephony/mbms/MbmsHelper.class", "size": 1004, "crc": -1438195000}, {"key": "androidx/core/text/BidiFormatter$Builder.class", "name": "androidx/core/text/BidiFormatter$Builder.class", "size": 2036, "crc": 1378760591}, {"key": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "name": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "size": 4094, "crc": -1902417185}, {"key": "androidx/core/text/BidiFormatter.class", "name": "androidx/core/text/BidiFormatter.class", "size": 5789, "crc": -838162369}, {"key": "androidx/core/text/HtmlCompat$Api24Impl.class", "name": "androidx/core/text/HtmlCompat$Api24Impl.class", "size": 1362, "crc": 146217190}, {"key": "androidx/core/text/HtmlCompat.class", "name": "androidx/core/text/HtmlCompat.class", "size": 2554, "crc": -261951905}, {"key": "androidx/core/text/ICUCompat$Api21Impl.class", "name": "androidx/core/text/ICUCompat$Api21Impl.class", "size": 734, "crc": 1318579150}, {"key": "androidx/core/text/ICUCompat$Api24Impl.class", "name": "androidx/core/text/ICUCompat$Api24Impl.class", "size": 1128, "crc": -209947284}, {"key": "androidx/core/text/ICUCompat.class", "name": "androidx/core/text/ICUCompat.class", "size": 3456, "crc": -1561930858}, {"key": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "name": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "size": 769, "crc": -1309899149}, {"key": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "name": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "size": 2092, "crc": 896385675}, {"key": "androidx/core/text/PrecomputedTextCompat$Params.class", "name": "androidx/core/text/PrecomputedTextCompat$Params.class", "size": 6425, "crc": 931090495}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "size": 1631, "crc": 1319644435}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "size": 1136, "crc": 1854164396}, {"key": "androidx/core/text/PrecomputedTextCompat.class", "name": "androidx/core/text/PrecomputedTextCompat.class", "size": 9184, "crc": -392844784}, {"key": "androidx/core/text/TextDirectionHeuristicCompat.class", "name": "androidx/core/text/TextDirectionHeuristicCompat.class", "size": 222, "crc": -118200751}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "size": 1358, "crc": -116397953}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "size": 1162, "crc": 1465235069}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "size": 342, "crc": 964437877}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "size": 1745, "crc": 2136242730}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "size": 1131, "crc": -1371447970}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "size": 1258, "crc": 1502971490}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat.class", "size": 2235, "crc": 1050206764}, {"key": "androidx/core/text/TextUtilsCompat.class", "name": "androidx/core/text/TextUtilsCompat.class", "size": 818, "crc": 924146042}, {"key": "androidx/core/text/method/LinkMovementMethodCompat.class", "name": "androidx/core/text/method/LinkMovementMethodCompat.class", "size": 2369, "crc": -1733823913}, {"key": "androidx/core/text/util/FindAddress$ZipRange.class", "name": "androidx/core/text/util/FindAddress$ZipRange.class", "size": 1027, "crc": 1759049123}, {"key": "androidx/core/text/util/FindAddress.class", "name": "androidx/core/text/util/FindAddress.class", "size": 11163, "crc": 106913627}, {"key": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "name": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "size": 1668, "crc": 818694512}, {"key": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "name": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "size": 548, "crc": 978455525}, {"key": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "name": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "size": 655, "crc": -1830119234}, {"key": "androidx/core/text/util/LinkifyCompat.class", "name": "androidx/core/text/util/LinkifyCompat.class", "size": 12426, "crc": -1518519244}, {"key": "androidx/core/text/util/LocalePreferences$1.class", "name": "androidx/core/text/util/LocalePreferences$1.class", "size": 940, "crc": 378637435}, {"key": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "size": 1260, "crc": 1069784012}, {"key": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "size": 2948, "crc": 1431973722}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "size": 754, "crc": 2111337730}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType.class", "size": 1131, "crc": 1121003693}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "size": 742, "crc": -925039115}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "size": 892, "crc": -1082602966}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "size": 747, "crc": -1451601347}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle.class", "size": 770, "crc": 2050120332}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "size": 769, "crc": 372884259}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "size": 793, "crc": -221813898}, {"key": "androidx/core/text/util/LocalePreferences.class", "name": "androidx/core/text/util/LocalePreferences.class", "size": 5452, "crc": 518336472}, {"key": "androidx/core/util/AtomicFile.class", "name": "androidx/core/util/AtomicFile.class", "size": 4462, "crc": 1282749343}, {"key": "androidx/core/util/DebugUtils.class", "name": "androidx/core/util/DebugUtils.class", "size": 1525, "crc": -788472052}, {"key": "androidx/core/util/LogWriter.class", "name": "androidx/core/util/LogWriter.class", "size": 1684, "crc": -707190681}, {"key": "androidx/core/util/ObjectsCompat.class", "name": "androidx/core/util/ObjectsCompat.class", "size": 1858, "crc": 1955834765}, {"key": "androidx/core/util/Pair.class", "name": "androidx/core/util/Pair.class", "size": 1918, "crc": -1970849920}, {"key": "androidx/core/util/PatternsCompat.class", "name": "androidx/core/util/PatternsCompat.class", "size": 58674, "crc": 176158261}, {"key": "androidx/core/util/Preconditions.class", "name": "androidx/core/util/Preconditions.class", "size": 6183, "crc": 1496653811}, {"key": "androidx/core/util/Predicate.class", "name": "androidx/core/util/Predicate.class", "size": 3398, "crc": 1789136790}, {"key": "androidx/core/util/SizeFCompat$Api21Impl.class", "name": "androidx/core/util/SizeFCompat$Api21Impl.class", "size": 1233, "crc": 1355410199}, {"key": "androidx/core/util/SizeFCompat.class", "name": "androidx/core/util/SizeFCompat.class", "size": 1988, "crc": 703664480}, {"key": "androidx/core/util/TimeUtils.class", "name": "androidx/core/util/TimeUtils.class", "size": 4432, "crc": 943155114}, {"key": "androidx/core/util/TypedValueCompat$Api34Impl.class", "name": "androidx/core/util/TypedValueCompat$Api34Impl.class", "size": 817, "crc": 1538916558}, {"key": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "name": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "size": 659, "crc": 1797910148}, {"key": "androidx/core/util/TypedValueCompat.class", "name": "androidx/core/util/TypedValueCompat.class", "size": 2402, "crc": -**********}, {"key": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "name": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "size": 4776, "crc": 922346686}, {"key": "androidx/core/view/AccessibilityDelegateCompat.class", "name": "androidx/core/view/AccessibilityDelegateCompat.class", "size": 7248, "crc": -861580143}, {"key": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "name": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "size": 543, "crc": -**********}, {"key": "androidx/core/view/ActionProvider$VisibilityListener.class", "name": "androidx/core/view/ActionProvider$VisibilityListener.class", "size": 289, "crc": -**********}, {"key": "androidx/core/view/ActionProvider.class", "name": "androidx/core/view/ActionProvider.class", "size": 3576, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "size": 3079, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$Builder.class", "name": "androidx/core/view/ContentInfoCompat$Builder.class", "size": 2697, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "size": 666, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "size": 2633, "crc": -413578322}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "size": 2445, "crc": 793814999}, {"key": "androidx/core/view/ContentInfoCompat$Compat.class", "name": "androidx/core/view/ContentInfoCompat$Compat.class", "size": 599, "crc": 322977362}, {"key": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "size": 1978, "crc": -1656001563}, {"key": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "size": 2855, "crc": 783683588}, {"key": "androidx/core/view/ContentInfoCompat$Flags.class", "name": "androidx/core/view/ContentInfoCompat$Flags.class", "size": 645, "crc": 103647730}, {"key": "androidx/core/view/ContentInfoCompat$Source.class", "name": "androidx/core/view/ContentInfoCompat$Source.class", "size": 647, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat.class", "name": "androidx/core/view/ContentInfoCompat.class", "size": 7544, "crc": -710995835}, {"key": "androidx/core/view/DifferentialMotionFlingController$DifferentialVelocityProvider.class", "name": "androidx/core/view/DifferentialMotionFlingController$DifferentialVelocityProvider.class", "size": 491, "crc": **********}, {"key": "androidx/core/view/DifferentialMotionFlingController$FlingVelocityThresholdCalculator.class", "name": "androidx/core/view/DifferentialMotionFlingController$FlingVelocityThresholdCalculator.class", "size": 510, "crc": 573487127}, {"key": "androidx/core/view/DifferentialMotionFlingController.class", "name": "androidx/core/view/DifferentialMotionFlingController.class", "size": 5499, "crc": -**********}, {"key": "androidx/core/view/DifferentialMotionFlingTarget.class", "name": "androidx/core/view/DifferentialMotionFlingTarget.class", "size": 286, "crc": -504038964}, {"key": "androidx/core/view/DisplayCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$Api23Impl.class", "size": 3224, "crc": -**********}, {"key": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "size": 984, "crc": -**********}, {"key": "androidx/core/view/DisplayCompat$ModeCompat.class", "name": "androidx/core/view/DisplayCompat$ModeCompat.class", "size": 2226, "crc": 1802698466}, {"key": "androidx/core/view/DisplayCompat.class", "name": "androidx/core/view/DisplayCompat.class", "size": 5311, "crc": 426530620}, {"key": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "size": 1861, "crc": -2061863409}, {"key": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "size": 1139, "crc": 2065672441}, {"key": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "size": 1458, "crc": -1323522111}, {"key": "androidx/core/view/DisplayCutoutCompat.class", "name": "androidx/core/view/DisplayCutoutCompat.class", "size": 5691, "crc": -278841617}, {"key": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "name": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "size": 1222, "crc": -1494528412}, {"key": "androidx/core/view/DragAndDropPermissionsCompat.class", "name": "androidx/core/view/DragAndDropPermissionsCompat.class", "size": 1686, "crc": -112841901}, {"key": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "name": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "size": 416, "crc": -2064185111}, {"key": "androidx/core/view/DragStartHelper.class", "name": "androidx/core/view/DragStartHelper.class", "size": 3436, "crc": -1050781143}, {"key": "androidx/core/view/GestureDetectorCompat.class", "name": "androidx/core/view/GestureDetectorCompat.class", "size": 2078, "crc": -590261000}, {"key": "androidx/core/view/GravityCompat.class", "name": "androidx/core/view/GravityCompat.class", "size": 1482, "crc": -1284594109}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "size": 696, "crc": 172944499}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "size": 694, "crc": 1352997797}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat.class", "size": 2165, "crc": -233855463}, {"key": "androidx/core/view/InputDeviceCompat.class", "name": "androidx/core/view/InputDeviceCompat.class", "size": 1178, "crc": -1611156102}, {"key": "androidx/core/view/KeyEventDispatcher$Component.class", "name": "androidx/core/view/KeyEventDispatcher$Component.class", "size": 377, "crc": 1819687057}, {"key": "androidx/core/view/KeyEventDispatcher.class", "name": "androidx/core/view/KeyEventDispatcher.class", "size": 5617, "crc": -251757181}, {"key": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "name": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "size": 1824, "crc": 709925757}, {"key": "androidx/core/view/LayoutInflaterCompat.class", "name": "androidx/core/view/LayoutInflaterCompat.class", "size": 3446, "crc": 1815067315}, {"key": "androidx/core/view/LayoutInflaterFactory.class", "name": "androidx/core/view/LayoutInflaterFactory.class", "size": 362, "crc": 1217133356}, {"key": "androidx/core/view/MarginLayoutParamsCompat.class", "name": "androidx/core/view/MarginLayoutParamsCompat.class", "size": 2016, "crc": 52886746}, {"key": "androidx/core/view/MenuCompat$Api28Impl.class", "name": "androidx/core/view/MenuCompat$Api28Impl.class", "size": 763, "crc": 493244291}, {"key": "androidx/core/view/MenuCompat.class", "name": "androidx/core/view/MenuCompat.class", "size": 1173, "crc": -465812236}, {"key": "androidx/core/view/MenuHost.class", "name": "androidx/core/view/MenuHost.class", "size": 802, "crc": 1764291970}, {"key": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "name": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "size": 1047, "crc": -2092317000}, {"key": "androidx/core/view/MenuHostHelper.class", "name": "androidx/core/view/MenuHostHelper.class", "size": 6015, "crc": 1640962395}, {"key": "androidx/core/view/MenuItemCompat$1.class", "name": "androidx/core/view/MenuItemCompat$1.class", "size": 1202, "crc": -273191399}, {"key": "androidx/core/view/MenuItemCompat$Api26Impl.class", "name": "androidx/core/view/MenuItemCompat$Api26Impl.class", "size": 3334, "crc": -387095062}, {"key": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "name": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "size": 427, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat.class", "name": "androidx/core/view/MenuItemCompat.class", "size": 7928, "crc": **********}, {"key": "androidx/core/view/MenuProvider.class", "name": "androidx/core/view/MenuProvider.class", "size": 694, "crc": -168585429}, {"key": "androidx/core/view/MotionEventCompat.class", "name": "androidx/core/view/MotionEventCompat.class", "size": 5062, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingChild.class", "name": "androidx/core/view/NestedScrollingChild.class", "size": 612, "crc": 643083790}, {"key": "androidx/core/view/NestedScrollingChild2.class", "name": "androidx/core/view/NestedScrollingChild2.class", "size": 512, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild3.class", "name": "androidx/core/view/NestedScrollingChild3.class", "size": 369, "crc": 538066002}, {"key": "androidx/core/view/NestedScrollingChildHelper.class", "name": "androidx/core/view/NestedScrollingChildHelper.class", "size": 5709, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent.class", "name": "androidx/core/view/NestedScrollingParent.class", "size": 808, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent2.class", "name": "androidx/core/view/NestedScrollingParent2.class", "size": 695, "crc": -892996660}, {"key": "androidx/core/view/NestedScrollingParent3.class", "name": "androidx/core/view/NestedScrollingParent3.class", "size": 350, "crc": -686595616}, {"key": "androidx/core/view/NestedScrollingParentHelper.class", "name": "androidx/core/view/NestedScrollingParentHelper.class", "size": 1541, "crc": 461928738}, {"key": "androidx/core/view/OnApplyWindowInsetsListener.class", "name": "androidx/core/view/OnApplyWindowInsetsListener.class", "size": 418, "crc": 821875535}, {"key": "androidx/core/view/OnReceiveContentListener.class", "name": "androidx/core/view/OnReceiveContentListener.class", "size": 440, "crc": -51731300}, {"key": "androidx/core/view/OnReceiveContentViewBehavior.class", "name": "androidx/core/view/OnReceiveContentViewBehavior.class", "size": 423, "crc": -1932407804}, {"key": "androidx/core/view/OneShotPreDrawListener.class", "name": "androidx/core/view/OneShotPreDrawListener.class", "size": 2307, "crc": -501338103}, {"key": "androidx/core/view/PointerIconCompat$Api24Impl.class", "name": "androidx/core/view/PointerIconCompat$Api24Impl.class", "size": 1259, "crc": -1765924208}, {"key": "androidx/core/view/PointerIconCompat.class", "name": "androidx/core/view/PointerIconCompat.class", "size": 3083, "crc": 652835173}, {"key": "androidx/core/view/ScaleGestureDetectorCompat.class", "name": "androidx/core/view/ScaleGestureDetectorCompat.class", "size": 1187, "crc": 1698678462}, {"key": "androidx/core/view/ScrollingView.class", "name": "androidx/core/view/ScrollingView.class", "size": 364, "crc": -218140119}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "size": 596, "crc": -596741455}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "size": 2596, "crc": 1848626303}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "size": 4052, "crc": -820004648}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "size": 1685, "crc": -1955609034}, {"key": "androidx/core/view/TintableBackgroundView.class", "name": "androidx/core/view/TintableBackgroundView.class", "size": 723, "crc": 1795767615}, {"key": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "name": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "size": 1135, "crc": -56836724}, {"key": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "name": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "size": 711, "crc": 1429362118}, {"key": "androidx/core/view/VelocityTrackerCompat.class", "name": "androidx/core/view/VelocityTrackerCompat.class", "size": 4023, "crc": -1355891040}, {"key": "androidx/core/view/VelocityTrackerFallback.class", "name": "androidx/core/view/VelocityTrackerFallback.class", "size": 2833, "crc": 1553350646}, {"key": "androidx/core/view/ViewCompat$1.class", "name": "androidx/core/view/ViewCompat$1.class", "size": 2347, "crc": 2014484077}, {"key": "androidx/core/view/ViewCompat$2.class", "name": "androidx/core/view/ViewCompat$2.class", "size": 2160, "crc": 1965995768}, {"key": "androidx/core/view/ViewCompat$3.class", "name": "androidx/core/view/ViewCompat$3.class", "size": 2155, "crc": 1332965266}, {"key": "androidx/core/view/ViewCompat$4.class", "name": "androidx/core/view/ViewCompat$4.class", "size": 2217, "crc": -1634602532}, {"key": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "name": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "size": 3791, "crc": 1632848381}, {"key": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "name": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "size": 3143, "crc": -1256677346}, {"key": "androidx/core/view/ViewCompat$Api20Impl.class", "name": "androidx/core/view/ViewCompat$Api20Impl.class", "size": 1126, "crc": -9984510}, {"key": "androidx/core/view/ViewCompat$Api21Impl$1.class", "name": "androidx/core/view/ViewCompat$Api21Impl$1.class", "size": 2038, "crc": -1553055455}, {"key": "androidx/core/view/ViewCompat$Api21Impl.class", "name": "androidx/core/view/ViewCompat$Api21Impl.class", "size": 7146, "crc": -1249325660}, {"key": "androidx/core/view/ViewCompat$Api23Impl.class", "name": "androidx/core/view/ViewCompat$Api23Impl.class", "size": 1943, "crc": -949989883}, {"key": "androidx/core/view/ViewCompat$Api24Impl.class", "name": "androidx/core/view/ViewCompat$Api24Impl.class", "size": 2146, "crc": -935460301}, {"key": "androidx/core/view/ViewCompat$Api26Impl.class", "name": "androidx/core/view/ViewCompat$Api26Impl.class", "size": 3597, "crc": -788702953}, {"key": "androidx/core/view/ViewCompat$Api28Impl.class", "name": "androidx/core/view/ViewCompat$Api28Impl.class", "size": 4705, "crc": 592211972}, {"key": "androidx/core/view/ViewCompat$Api29Impl.class", "name": "androidx/core/view/ViewCompat$Api29Impl.class", "size": 3051, "crc": -128268501}, {"key": "androidx/core/view/ViewCompat$Api30Impl.class", "name": "androidx/core/view/ViewCompat$Api30Impl.class", "size": 2180, "crc": 172388591}, {"key": "androidx/core/view/ViewCompat$Api31Impl.class", "name": "androidx/core/view/ViewCompat$Api31Impl.class", "size": 2229, "crc": 923740304}, {"key": "androidx/core/view/ViewCompat$FocusDirection.class", "name": "androidx/core/view/ViewCompat$FocusDirection.class", "size": 642, "crc": 1155425484}, {"key": "androidx/core/view/ViewCompat$FocusRealDirection.class", "name": "androidx/core/view/ViewCompat$FocusRealDirection.class", "size": 650, "crc": 749280326}, {"key": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "name": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "size": 658, "crc": 1576246077}, {"key": "androidx/core/view/ViewCompat$NestedScrollType.class", "name": "androidx/core/view/ViewCompat$NestedScrollType.class", "size": 646, "crc": 1643799421}, {"key": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "name": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "size": 1714, "crc": 750143101}, {"key": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "name": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "size": 424, "crc": -762508931}, {"key": "androidx/core/view/ViewCompat$ScrollAxis.class", "name": "androidx/core/view/ViewCompat$ScrollAxis.class", "size": 634, "crc": -1481000453}, {"key": "androidx/core/view/ViewCompat$ScrollIndicators.class", "name": "androidx/core/view/ViewCompat$ScrollIndicators.class", "size": 646, "crc": -1203320779}, {"key": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "name": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "size": 5914, "crc": -68237476}, {"key": "androidx/core/view/ViewCompat.class", "name": "androidx/core/view/ViewCompat.class", "size": 59370, "crc": 1896916239}, {"key": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "size": 946, "crc": 1784829614}, {"key": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "size": 990, "crc": -912023001}, {"key": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "size": 1161, "crc": -89677823}, {"key": "androidx/core/view/ViewConfigurationCompat.class", "name": "androidx/core/view/ViewConfigurationCompat.class", "size": 7481, "crc": -1797316171}, {"key": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "size": 1055, "crc": 402402769}, {"key": "androidx/core/view/ViewGroupCompat.class", "name": "androidx/core/view/ViewGroupCompat.class", "size": 2808, "crc": 756322627}, {"key": "androidx/core/view/ViewParentCompat$Api21Impl.class", "name": "androidx/core/view/ViewParentCompat$Api21Impl.class", "size": 2353, "crc": -585042330}, {"key": "androidx/core/view/ViewParentCompat.class", "name": "androidx/core/view/ViewParentCompat.class", "size": 7252, "crc": -895791330}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "size": 1433, "crc": -1943472778}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "size": 1230, "crc": -189188170}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat.class", "size": 10340, "crc": -2110957819}, {"key": "androidx/core/view/ViewPropertyAnimatorListener.class", "name": "androidx/core/view/ViewPropertyAnimatorListener.class", "size": 371, "crc": 1924827163}, {"key": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "name": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "size": 844, "crc": -799948790}, {"key": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "name": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "size": 305, "crc": 1797357548}, {"key": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "name": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "size": 1495, "crc": 1645219278}, {"key": "androidx/core/view/ViewStructureCompat.class", "name": "androidx/core/view/ViewStructureCompat.class", "size": 2193, "crc": -729643503}, {"key": "androidx/core/view/WindowCompat$Api16Impl.class", "name": "androidx/core/view/WindowCompat$Api16Impl.class", "size": 1077, "crc": -494859518}, {"key": "androidx/core/view/WindowCompat$Api28Impl.class", "name": "androidx/core/view/WindowCompat$Api28Impl.class", "size": 856, "crc": -841604914}, {"key": "androidx/core/view/WindowCompat$Api30Impl.class", "name": "androidx/core/view/WindowCompat$Api30Impl.class", "size": 869, "crc": -1462428972}, {"key": "androidx/core/view/WindowCompat.class", "name": "androidx/core/view/WindowCompat.class", "size": 2194, "crc": -1144865123}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "size": 3016, "crc": -663239670}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "size": 771, "crc": -19343690}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "size": 2066, "crc": 209494665}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "size": 1808, "crc": 400888180}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "size": 2587, "crc": 430262637}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "size": 1635, "crc": 138536965}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "size": 2016, "crc": -1061163575}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "size": 5763, "crc": -1045538398}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "size": 9225, "crc": -312622289}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "size": 4827, "crc": 21717133}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "size": 3826, "crc": 426416019}, {"key": "androidx/core/view/WindowInsetsAnimationCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat.class", "size": 3503, "crc": -476679620}, {"key": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "size": 519, "crc": -1375959183}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "size": 1814, "crc": 1764069739}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "size": 2580, "crc": -1670379821}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "size": 2455, "crc": -1206962161}, {"key": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "name": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "size": 3507, "crc": -451676334}, {"key": "androidx/core/view/WindowInsetsCompat$Builder.class", "name": "androidx/core/view/WindowInsetsCompat$Builder.class", "size": 3668, "crc": 873871434}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "size": 3188, "crc": 952023470}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "size": 3845, "crc": -1367789157}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "size": 3157, "crc": -1700995255}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "size": 1858, "crc": -362799567}, {"key": "androidx/core/view/WindowInsetsCompat$Impl.class", "name": "androidx/core/view/WindowInsetsCompat$Impl.class", "size": 4440, "crc": 587911647}, {"key": "androidx/core/view/WindowInsetsCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsCompat$Impl20.class", "size": 9787, "crc": -1633841618}, {"key": "androidx/core/view/WindowInsetsCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsCompat$Impl21.class", "size": 2425, "crc": 202908659}, {"key": "androidx/core/view/WindowInsetsCompat$Impl28.class", "name": "androidx/core/view/WindowInsetsCompat$Impl28.class", "size": 2327, "crc": -559644749}, {"key": "androidx/core/view/WindowInsetsCompat$Impl29.class", "name": "androidx/core/view/WindowInsetsCompat$Impl29.class", "size": 2617, "crc": -220432070}, {"key": "androidx/core/view/WindowInsetsCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsCompat$Impl30.class", "size": 2255, "crc": 2128860632}, {"key": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "name": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "size": 719, "crc": 1917876408}, {"key": "androidx/core/view/WindowInsetsCompat$Type.class", "name": "androidx/core/view/WindowInsetsCompat$Type.class", "size": 2387, "crc": 1816556379}, {"key": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "size": 1293, "crc": 903114413}, {"key": "androidx/core/view/WindowInsetsCompat.class", "name": "androidx/core/view/WindowInsetsCompat.class", "size": 10735, "crc": 1488646022}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "size": 2318, "crc": 487295909}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "size": 4149, "crc": 740369200}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "size": 1629, "crc": -135562880}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "size": 1634, "crc": 1693726333}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "size": 2291, "crc": 1968305723}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "size": 7376, "crc": -2028220928}, {"key": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "name": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "size": 494, "crc": 1407225033}, {"key": "androidx/core/view/WindowInsetsControllerCompat.class", "name": "androidx/core/view/WindowInsetsControllerCompat.class", "size": 5064, "crc": 152015089}, {"key": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "name": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "size": 1703, "crc": 566446454}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "size": 1131, "crc": 1836249342}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "size": 718, "crc": 701609424}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "size": 5402, "crc": -1750471231}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "size": 477, "crc": 1509031422}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "size": 811, "crc": 1411476729}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "size": 1686, "crc": 575055372}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "size": 938, "crc": -1054095445}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "size": 384, "crc": -1837815159}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "size": 1713, "crc": 1165713886}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "size": 4426, "crc": -1383485855}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "size": 11582, "crc": 1273619610}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api21Impl.class", "size": 1399, "crc": -696913560}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "size": 1536, "crc": 575269572}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "size": 4419, "crc": 1345792111}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "size": 3590, "crc": 1833340314}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "size": 2117, "crc": 1385820584}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "size": 3564, "crc": 1295266963}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "size": 2985, "crc": 2023102495}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "size": 2028, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "size": 2545, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "size": 46148, "crc": 126892516}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "size": 2633, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "size": 1697, "crc": -251749915}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "size": 2621, "crc": 188254524}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "size": 8568, "crc": 311434289}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "size": 1020, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "size": 1120, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "size": 1014, "crc": 712771277}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "size": 1000, "crc": 2123823754}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "size": 1070, "crc": -838097745}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "size": 932, "crc": 1128765126}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "size": 1022, "crc": 1087470567}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "size": 1024, "crc": 637844774}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "size": 1347, "crc": -1100584727}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "size": 2788, "crc": 917831218}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "size": 1224, "crc": -523677408}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "size": 937, "crc": 855397755}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "size": 858, "crc": 1761157054}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "size": 1687, "crc": -1238990567}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "size": 1140, "crc": -814534727}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "size": 8894, "crc": -1701157246}, {"key": "androidx/core/view/animation/PathInterpolatorApi14.class", "name": "androidx/core/view/animation/PathInterpolatorApi14.class", "size": 2333, "crc": 1168129793}, {"key": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "name": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "size": 1289, "crc": 1556389558}, {"key": "androidx/core/view/animation/PathInterpolatorCompat.class", "name": "androidx/core/view/animation/PathInterpolatorCompat.class", "size": 1515, "crc": -1645199051}, {"key": "androidx/core/view/autofill/AutofillIdCompat.class", "name": "androidx/core/view/autofill/AutofillIdCompat.class", "size": 1073, "crc": -867735316}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "size": 890, "crc": -112680947}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "size": 2720, "crc": -1799560617}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "size": 1238, "crc": 1545892060}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "size": 5395, "crc": -518664933}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "size": 1627, "crc": 725359131}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat.class", "size": 8702, "crc": -386770413}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "size": 1833, "crc": 797796400}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "size": 1649, "crc": 2047863385}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "size": 1134, "crc": 1360607248}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "size": 525, "crc": 1476989102}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat.class", "size": 11367, "crc": -1885061227}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "size": 2094, "crc": 1456256422}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "size": 1773, "crc": 1624196168}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "size": 690, "crc": 1074534587}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "size": 2707, "crc": 877487393}, {"key": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "name": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "size": 3002, "crc": -1881276443}, {"key": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "name": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "size": 1582, "crc": 1842928479}, {"key": "androidx/core/widget/AutoScrollHelper.class", "name": "androidx/core/widget/AutoScrollHelper.class", "size": 8621, "crc": 1783212020}, {"key": "androidx/core/widget/AutoSizeableTextView.class", "name": "androidx/core/widget/AutoSizeableTextView.class", "size": 1312, "crc": 383680365}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "size": 1853, "crc": -283751687}, {"key": "androidx/core/widget/CheckedTextViewCompat.class", "name": "androidx/core/widget/CheckedTextViewCompat.class", "size": 2536, "crc": -801797701}, {"key": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "size": 1729, "crc": 177035831}, {"key": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "size": 875, "crc": -1351051043}, {"key": "androidx/core/widget/CompoundButtonCompat.class", "name": "androidx/core/widget/CompoundButtonCompat.class", "size": 3549, "crc": -578277603}, {"key": "androidx/core/widget/ContentLoadingProgressBar.class", "name": "androidx/core/widget/ContentLoadingProgressBar.class", "size": 3255, "crc": -251890886}, {"key": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "size": 827, "crc": 1024816863}, {"key": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "size": 1565, "crc": 941125704}, {"key": "androidx/core/widget/EdgeEffectCompat.class", "name": "androidx/core/widget/EdgeEffectCompat.class", "size": 2939, "crc": -1785843503}, {"key": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "name": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "size": 1670, "crc": -1775682416}, {"key": "androidx/core/widget/ImageViewCompat.class", "name": "androidx/core/widget/ImageViewCompat.class", "size": 2780, "crc": -795067799}, {"key": "androidx/core/widget/ListPopupWindowCompat.class", "name": "androidx/core/widget/ListPopupWindowCompat.class", "size": 1253, "crc": 1222268757}, {"key": "androidx/core/widget/ListViewAutoScrollHelper.class", "name": "androidx/core/widget/ListViewAutoScrollHelper.class", "size": 1611, "crc": 1886614277}, {"key": "androidx/core/widget/ListViewCompat.class", "name": "androidx/core/widget/ListViewCompat.class", "size": 899, "crc": -1092787587}, {"key": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "name": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "size": 3675, "crc": -1158356231}, {"key": "androidx/core/widget/NestedScrollView$Api21Impl.class", "name": "androidx/core/widget/NestedScrollView$Api21Impl.class", "size": 762, "crc": 1045126175}, {"key": "androidx/core/widget/NestedScrollView$DifferentialMotionFlingTargetImpl.class", "name": "androidx/core/widget/NestedScrollView$DifferentialMotionFlingTargetImpl.class", "size": 1290, "crc": -1836699415}, {"key": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "name": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "size": 422, "crc": -761668443}, {"key": "androidx/core/widget/NestedScrollView$SavedState$1.class", "name": "androidx/core/widget/NestedScrollView$SavedState$1.class", "size": 1315, "crc": 2029700730}, {"key": "androidx/core/widget/NestedScrollView$SavedState.class", "name": "androidx/core/widget/NestedScrollView$SavedState.class", "size": 1969, "crc": -1634809784}, {"key": "androidx/core/widget/NestedScrollView.class", "name": "androidx/core/widget/NestedScrollView.class", "size": 41899, "crc": -1408253433}, {"key": "androidx/core/widget/PopupMenuCompat.class", "name": "androidx/core/widget/PopupMenuCompat.class", "size": 845, "crc": 2027517968}, {"key": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "size": 1283, "crc": -1877238009}, {"key": "androidx/core/widget/PopupWindowCompat.class", "name": "androidx/core/widget/PopupWindowCompat.class", "size": 3936, "crc": -1990325588}, {"key": "androidx/core/widget/ScrollerCompat.class", "name": "androidx/core/widget/ScrollerCompat.class", "size": 3656, "crc": -54539407}, {"key": "androidx/core/widget/TextViewCompat$Api23Impl.class", "name": "androidx/core/widget/TextViewCompat$Api23Impl.class", "size": 2274, "crc": -1823767142}, {"key": "androidx/core/widget/TextViewCompat$Api24Impl.class", "name": "androidx/core/widget/TextViewCompat$Api24Impl.class", "size": 781, "crc": 870517322}, {"key": "androidx/core/widget/TextViewCompat$Api26Impl.class", "name": "androidx/core/widget/TextViewCompat$Api26Impl.class", "size": 1950, "crc": 1313675659}, {"key": "androidx/core/widget/TextViewCompat$Api28Impl.class", "name": "androidx/core/widget/TextViewCompat$Api28Impl.class", "size": 1619, "crc": 775530023}, {"key": "androidx/core/widget/TextViewCompat$Api34Impl.class", "name": "androidx/core/widget/TextViewCompat$Api34Impl.class", "size": 959, "crc": 1521316654}, {"key": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "name": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "size": 662, "crc": -1792362665}, {"key": "androidx/core/widget/TextViewCompat$OreoCallback.class", "name": "androidx/core/widget/TextViewCompat$OreoCallback.class", "size": 7346, "crc": -1591411371}, {"key": "androidx/core/widget/TextViewCompat.class", "name": "androidx/core/widget/TextViewCompat.class", "size": 15171, "crc": 495483427}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener.class", "size": 3894, "crc": -1743557502}, {"key": "androidx/core/widget/TintableCheckedTextView.class", "name": "androidx/core/widget/TintableCheckedTextView.class", "size": 946, "crc": -75470643}, {"key": "androidx/core/widget/TintableCompoundButton.class", "name": "androidx/core/widget/TintableCompoundButton.class", "size": 709, "crc": -546728220}, {"key": "androidx/core/widget/TintableCompoundDrawablesView.class", "name": "androidx/core/widget/TintableCompoundDrawablesView.class", "size": 767, "crc": -2060620041}, {"key": "androidx/core/widget/TintableImageSourceView.class", "name": "androidx/core/widget/TintableImageSourceView.class", "size": 930, "crc": -1647897496}, {"key": "META-INF/androidx.core_core.version", "name": "META-INF/androidx.core_core.version", "size": 7, "crc": -1000712479}, {"key": "META-INF/core_release.kotlin_module", "name": "META-INF/core_release.kotlin_module", "size": 24, "crc": 1613429616}]