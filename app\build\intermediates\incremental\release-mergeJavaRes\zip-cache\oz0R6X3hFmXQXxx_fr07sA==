[{"key": "androidx/compose/runtime/AbstractApplier.class", "name": "androidx/compose/runtime/AbstractApplier.class", "size": 4522, "crc": 1744135459}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt$DefaultMonotonicFrameClock$2.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt$DefaultMonotonicFrameClock$2.class", "size": 1753, "crc": 868946224}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt.class", "size": 5140, "crc": -1571080916}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt.class", "size": 4092, "crc": 1044881723}, {"key": "androidx/compose/runtime/Anchor.class", "name": "androidx/compose/runtime/Anchor.class", "size": 2201, "crc": -1822756540}, {"key": "androidx/compose/runtime/AnchoredGroupPath.class", "name": "androidx/compose/runtime/AnchoredGroupPath.class", "size": 1315, "crc": -258126999}, {"key": "androidx/compose/runtime/Applier$DefaultImpls.class", "name": "androidx/compose/runtime/Applier$DefaultImpls.class", "size": 967, "crc": -252894882}, {"key": "androidx/compose/runtime/Applier.class", "name": "androidx/compose/runtime/Applier.class", "size": 1647, "crc": 1287229015}, {"key": "androidx/compose/runtime/AtomicInt.class", "name": "androidx/compose/runtime/AtomicInt.class", "size": 2103, "crc": 1139685444}, {"key": "androidx/compose/runtime/BitVector.class", "name": "androidx/compose/runtime/BitVector.class", "size": 4375, "crc": -2044057771}, {"key": "androidx/compose/runtime/BitwiseOperatorsKt.class", "name": "androidx/compose/runtime/BitwiseOperatorsKt.class", "size": 760, "crc": 466936685}, {"key": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "name": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "size": 3242, "crc": 1989657943}, {"key": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "size": 3713, "crc": -1979059919}, {"key": "androidx/compose/runtime/BroadcastFrameClock.class", "name": "androidx/compose/runtime/BroadcastFrameClock.class", "size": 12367, "crc": -1171633957}, {"key": "androidx/compose/runtime/Composable.class", "name": "androidx/compose/runtime/Composable.class", "size": 969, "crc": 1238586231}, {"key": "androidx/compose/runtime/ComposableInferredTarget.class", "name": "androidx/compose/runtime/ComposableInferredTarget.class", "size": 1045, "crc": 667039861}, {"key": "androidx/compose/runtime/ComposableOpenTarget.class", "name": "androidx/compose/runtime/ComposableOpenTarget.class", "size": 980, "crc": -1602434713}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "size": 2206, "crc": 298212011}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "size": 2206, "crc": -694043192}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "size": 1838, "crc": -571114150}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "size": 2199, "crc": -1161955486}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "size": 1493, "crc": 1522897750}, {"key": "androidx/compose/runtime/ComposableTarget.class", "name": "androidx/compose/runtime/ComposableTarget.class", "size": 1009, "crc": 757223313}, {"key": "androidx/compose/runtime/ComposableTargetMarker.class", "name": "androidx/compose/runtime/ComposableTargetMarker.class", "size": 979, "crc": -758062925}, {"key": "androidx/compose/runtime/ComposablesKt.class", "name": "androidx/compose/runtime/ComposablesKt.class", "size": 18289, "crc": 2113183306}, {"key": "androidx/compose/runtime/ComposeCompilerApi.class", "name": "androidx/compose/runtime/ComposeCompilerApi.class", "size": 798, "crc": -1144562057}, {"key": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "name": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "size": 556, "crc": -1240631825}, {"key": "androidx/compose/runtime/ComposeRuntimeError.class", "name": "androidx/compose/runtime/ComposeRuntimeError.class", "size": 1155, "crc": -135944264}, {"key": "androidx/compose/runtime/ComposeVersion.class", "name": "androidx/compose/runtime/ComposeVersion.class", "size": 882, "crc": 649017146}, {"key": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "name": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "size": 912, "crc": -573561917}, {"key": "androidx/compose/runtime/Composer$Companion.class", "name": "androidx/compose/runtime/Composer$Companion.class", "size": 1495, "crc": 2132480395}, {"key": "androidx/compose/runtime/Composer.class", "name": "androidx/compose/runtime/Composer.class", "size": 9728, "crc": -636275039}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "size": 1751, "crc": 1568768521}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "size": 13501, "crc": -367876385}, {"key": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "name": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "size": 1907, "crc": -476342967}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "size": 6058, "crc": -1423118344}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "size": 1947, "crc": 2031846306}, {"key": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "name": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "size": 2940, "crc": -787038646}, {"key": "androidx/compose/runtime/ComposerImpl.class", "name": "androidx/compose/runtime/ComposerImpl.class", "size": 104069, "crc": 1850061143}, {"key": "androidx/compose/runtime/ComposerKt.class", "name": "androidx/compose/runtime/ComposerKt.class", "size": 26977, "crc": 1501066410}, {"key": "androidx/compose/runtime/Composition.class", "name": "androidx/compose/runtime/Composition.class", "size": 991, "crc": -1493225744}, {"key": "androidx/compose/runtime/CompositionContext.class", "name": "androidx/compose/runtime/CompositionContext.class", "size": 5689, "crc": 1523199777}, {"key": "androidx/compose/runtime/CompositionContextKt.class", "name": "androidx/compose/runtime/CompositionContextKt.class", "size": 1044, "crc": 1676118894}, {"key": "androidx/compose/runtime/CompositionImpl$RememberEventDispatcher.class", "name": "androidx/compose/runtime/CompositionImpl$RememberEventDispatcher.class", "size": 11071, "crc": -1433398105}, {"key": "androidx/compose/runtime/CompositionImpl$observe$2.class", "name": "androidx/compose/runtime/CompositionImpl$observe$2.class", "size": 3088, "crc": 1964713700}, {"key": "androidx/compose/runtime/CompositionImpl.class", "name": "androidx/compose/runtime/CompositionImpl.class", "size": 84070, "crc": -369437339}, {"key": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "name": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "size": 966, "crc": -1793823563}, {"key": "androidx/compose/runtime/CompositionKt.class", "name": "androidx/compose/runtime/CompositionKt.class", "size": 7316, "crc": -1200656545}, {"key": "androidx/compose/runtime/CompositionLocal.class", "name": "androidx/compose/runtime/CompositionLocal.class", "size": 3383, "crc": 130533151}, {"key": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "name": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "size": 837, "crc": -328208158}, {"key": "androidx/compose/runtime/CompositionLocalContext.class", "name": "androidx/compose/runtime/CompositionLocalContext.class", "size": 1199, "crc": **********}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "size": 2360, "crc": -210389942}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "size": 2255, "crc": 998739605}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "size": 2240, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalKt.class", "name": "androidx/compose/runtime/CompositionLocalKt.class", "size": 10381, "crc": **********}, {"key": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "name": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "size": 1289, "crc": -609937834}, {"key": "androidx/compose/runtime/CompositionLocalMap.class", "name": "androidx/compose/runtime/CompositionLocalMap.class", "size": 1183, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalMapKt.class", "name": "androidx/compose/runtime/CompositionLocalMapKt.class", "size": 6847, "crc": **********}, {"key": "androidx/compose/runtime/CompositionObserverHolder.class", "name": "androidx/compose/runtime/CompositionObserverHolder.class", "size": 2030, "crc": 879441498}, {"key": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "name": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "size": 1878, "crc": 2120706359}, {"key": "androidx/compose/runtime/CompositionServiceKey.class", "name": "androidx/compose/runtime/CompositionServiceKey.class", "size": 488, "crc": -496338052}, {"key": "androidx/compose/runtime/CompositionServices.class", "name": "androidx/compose/runtime/CompositionServices.class", "size": 905, "crc": 460722935}, {"key": "androidx/compose/runtime/CompositionTracer.class", "name": "androidx/compose/runtime/CompositionTracer.class", "size": 863, "crc": -824089828}, {"key": "androidx/compose/runtime/ComputedProvidableCompositionLocal$1.class", "name": "androidx/compose/runtime/ComputedProvidableCompositionLocal$1.class", "size": 1273, "crc": 77432647}, {"key": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "size": 3224, "crc": 1590193451}, {"key": "androidx/compose/runtime/ComputedValueHolder.class", "name": "androidx/compose/runtime/ComputedValueHolder.class", "size": 4642, "crc": -819374660}, {"key": "androidx/compose/runtime/ControlledComposition.class", "name": "androidx/compose/runtime/ControlledComposition.class", "size": 3140, "crc": -2109976709}, {"key": "androidx/compose/runtime/DataIterator.class", "name": "androidx/compose/runtime/DataIterator.class", "size": 3448, "crc": 472030055}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "size": 3336, "crc": 240263098}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "size": 1829, "crc": -2109445933}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "size": 3021, "crc": 2035933371}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "size": 7014, "crc": 540874096}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "size": 1200, "crc": -1521236967}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "size": 12891, "crc": -725990510}, {"key": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "name": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "size": 3131, "crc": -1653165992}, {"key": "androidx/compose/runtime/DerivedSnapshotState.class", "name": "androidx/compose/runtime/DerivedSnapshotState.class", "size": 18338, "crc": 946622408}, {"key": "androidx/compose/runtime/DerivedState$Record.class", "name": "androidx/compose/runtime/DerivedState$Record.class", "size": 1079, "crc": -682744111}, {"key": "androidx/compose/runtime/DerivedState.class", "name": "androidx/compose/runtime/DerivedState.class", "size": 1319, "crc": -1235172193}, {"key": "androidx/compose/runtime/DerivedStateObserver.class", "name": "androidx/compose/runtime/DerivedStateObserver.class", "size": 793, "crc": -1880963137}, {"key": "androidx/compose/runtime/DisallowComposableCalls.class", "name": "androidx/compose/runtime/DisallowComposableCalls.class", "size": 928, "crc": 757151631}, {"key": "androidx/compose/runtime/DisposableEffectImpl.class", "name": "androidx/compose/runtime/DisposableEffectImpl.class", "size": 2205, "crc": 482750958}, {"key": "androidx/compose/runtime/DisposableEffectResult.class", "name": "androidx/compose/runtime/DisposableEffectResult.class", "size": 453, "crc": -1829342907}, {"key": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "name": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "size": 1696, "crc": 651467134}, {"key": "androidx/compose/runtime/DisposableEffectScope.class", "name": "androidx/compose/runtime/DisposableEffectScope.class", "size": 1527, "crc": -682631942}, {"key": "androidx/compose/runtime/DontMemoize.class", "name": "androidx/compose/runtime/DontMemoize.class", "size": 754, "crc": -893898696}, {"key": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "size": 1033, "crc": -1157124520}, {"key": "androidx/compose/runtime/DoubleState.class", "name": "androidx/compose/runtime/DoubleState.class", "size": 1494, "crc": 1682501546}, {"key": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "name": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "size": 2481, "crc": -1297720387}, {"key": "androidx/compose/runtime/DynamicValueHolder.class", "name": "androidx/compose/runtime/DynamicValueHolder.class", "size": 4271, "crc": -385072930}, {"key": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "name": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "size": 1990, "crc": -711302787}, {"key": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "name": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "size": 1713, "crc": -229799156}, {"key": "androidx/compose/runtime/EffectsKt.class", "name": "androidx/compose/runtime/EffectsKt.class", "size": 19660, "crc": -2026375044}, {"key": "androidx/compose/runtime/ExpectKt$ThreadLocal$1.class", "name": "androidx/compose/runtime/ExpectKt$ThreadLocal$1.class", "size": 1101, "crc": -1349357325}, {"key": "androidx/compose/runtime/ExpectKt.class", "name": "androidx/compose/runtime/ExpectKt.class", "size": 1340, "crc": -552430871}, {"key": "androidx/compose/runtime/ExperimentalComposeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeApi.class", "size": 1223, "crc": 656735070}, {"key": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "size": 1247, "crc": -2074507129}, {"key": "androidx/compose/runtime/ExplicitGroupsComposable.class", "name": "androidx/compose/runtime/ExplicitGroupsComposable.class", "size": 865, "crc": -252095505}, {"key": "androidx/compose/runtime/FloatState$DefaultImpls.class", "name": "androidx/compose/runtime/FloatState$DefaultImpls.class", "size": 1023, "crc": -2066494080}, {"key": "androidx/compose/runtime/FloatState.class", "name": "androidx/compose/runtime/FloatState.class", "size": 1483, "crc": 1991470395}, {"key": "androidx/compose/runtime/GroupInfo.class", "name": "androidx/compose/runtime/GroupInfo.class", "size": 1349, "crc": -616390984}, {"key": "androidx/compose/runtime/GroupIterator.class", "name": "androidx/compose/runtime/GroupIterator.class", "size": 2830, "crc": -1066289695}, {"key": "androidx/compose/runtime/GroupKind$Companion.class", "name": "androidx/compose/runtime/GroupKind$Companion.class", "size": 1372, "crc": 978907983}, {"key": "androidx/compose/runtime/GroupKind.class", "name": "androidx/compose/runtime/GroupKind.class", "size": 3210, "crc": 143691553}, {"key": "androidx/compose/runtime/GroupSourceInformation.class", "name": "androidx/compose/runtime/GroupSourceInformation.class", "size": 8430, "crc": -957144693}, {"key": "androidx/compose/runtime/HotReloader$Companion.class", "name": "androidx/compose/runtime/HotReloader$Companion.class", "size": 2558, "crc": -279726275}, {"key": "androidx/compose/runtime/HotReloader.class", "name": "androidx/compose/runtime/HotReloader.class", "size": 883, "crc": -397004248}, {"key": "androidx/compose/runtime/HotReloaderKt.class", "name": "androidx/compose/runtime/HotReloaderKt.class", "size": 3604, "crc": 1059367699}, {"key": "androidx/compose/runtime/Immutable.class", "name": "androidx/compose/runtime/Immutable.class", "size": 959, "crc": -1811685330}, {"key": "androidx/compose/runtime/IntStack.class", "name": "androidx/compose/runtime/IntStack.class", "size": 2402, "crc": -1110259686}, {"key": "androidx/compose/runtime/IntState$DefaultImpls.class", "name": "androidx/compose/runtime/IntState$DefaultImpls.class", "size": 1015, "crc": 33389163}, {"key": "androidx/compose/runtime/IntState.class", "name": "androidx/compose/runtime/IntState.class", "size": 1477, "crc": 1267204505}, {"key": "androidx/compose/runtime/InternalComposeApi.class", "name": "androidx/compose/runtime/InternalComposeApi.class", "size": 1189, "crc": -938449997}, {"key": "androidx/compose/runtime/InternalComposeTracingApi.class", "name": "androidx/compose/runtime/InternalComposeTracingApi.class", "size": 955, "crc": -61782969}, {"key": "androidx/compose/runtime/Invalidation.class", "name": "androidx/compose/runtime/Invalidation.class", "size": 1818, "crc": 2037498004}, {"key": "androidx/compose/runtime/InvalidationResult.class", "name": "androidx/compose/runtime/InvalidationResult.class", "size": 1540, "crc": -1584083556}, {"key": "androidx/compose/runtime/JoinedKey.class", "name": "androidx/compose/runtime/JoinedKey.class", "size": 3031, "crc": -881333858}, {"key": "androidx/compose/runtime/KeyInfo.class", "name": "androidx/compose/runtime/KeyInfo.class", "size": 1702, "crc": -132843134}, {"key": "androidx/compose/runtime/Latch$await$2$2.class", "name": "androidx/compose/runtime/Latch$await$2$2.class", "size": 2962, "crc": 1369873859}, {"key": "androidx/compose/runtime/Latch.class", "name": "androidx/compose/runtime/Latch.class", "size": 6613, "crc": 541954179}, {"key": "androidx/compose/runtime/LaunchedEffectImpl.class", "name": "androidx/compose/runtime/LaunchedEffectImpl.class", "size": 3227, "crc": -726645999}, {"key": "androidx/compose/runtime/LazyValueHolder.class", "name": "androidx/compose/runtime/LazyValueHolder.class", "size": 2655, "crc": 426536718}, {"key": "androidx/compose/runtime/LeftCompositionCancellationException.class", "name": "androidx/compose/runtime/LeftCompositionCancellationException.class", "size": 1850, "crc": -1068847271}, {"key": "androidx/compose/runtime/LongState$DefaultImpls.class", "name": "androidx/compose/runtime/LongState$DefaultImpls.class", "size": 1013, "crc": -584322641}, {"key": "androidx/compose/runtime/LongState.class", "name": "androidx/compose/runtime/LongState.class", "size": 1472, "crc": 2070576272}, {"key": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "name": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "size": 3397, "crc": 1235685219}, {"key": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "name": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "size": 1051, "crc": -1986932794}, {"key": "androidx/compose/runtime/MonotonicFrameClock.class", "name": "androidx/compose/runtime/MonotonicFrameClock.class", "size": 2118, "crc": 2086433001}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "size": 2140, "crc": -1274335771}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt.class", "size": 4524, "crc": 1179733883}, {"key": "androidx/compose/runtime/MovableContent.class", "name": "androidx/compose/runtime/MovableContent.class", "size": 1792, "crc": -1866344453}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "size": 2560, "crc": -924880359}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "size": 2914, "crc": 1786875167}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "size": 3257, "crc": -1812134286}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "size": 3532, "crc": 503935510}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "size": 3814, "crc": 374068673}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "size": 2874, "crc": -1485965204}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "size": 3377, "crc": 464885046}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "size": 3494, "crc": -1964507563}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "size": 3607, "crc": 1217464475}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "size": 2964, "crc": 1036324418}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "size": 3307, "crc": -1851863183}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "size": 3581, "crc": 791903}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "size": 3863, "crc": 534241256}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "size": 3082, "crc": 1664291843}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "size": 3417, "crc": -145381504}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "size": 3537, "crc": 861673534}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "size": 3650, "crc": 1413776864}, {"key": "androidx/compose/runtime/MovableContentKt.class", "name": "androidx/compose/runtime/MovableContentKt.class", "size": 8581, "crc": -1747431980}, {"key": "androidx/compose/runtime/MovableContentState.class", "name": "androidx/compose/runtime/MovableContentState.class", "size": 1212, "crc": -328835292}, {"key": "androidx/compose/runtime/MovableContentStateReference.class", "name": "androidx/compose/runtime/MovableContentStateReference.class", "size": 4436, "crc": 1914663966}, {"key": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "size": 1296, "crc": -1067044414}, {"key": "androidx/compose/runtime/MutableDoubleState.class", "name": "androidx/compose/runtime/MutableDoubleState.class", "size": 2173, "crc": 1397055746}, {"key": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "size": 1285, "crc": 1690377782}, {"key": "androidx/compose/runtime/MutableFloatState.class", "name": "androidx/compose/runtime/MutableFloatState.class", "size": 2157, "crc": -1223683742}, {"key": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "size": 1275, "crc": -172430677}, {"key": "androidx/compose/runtime/MutableIntState.class", "name": "androidx/compose/runtime/MutableIntState.class", "size": 2141, "crc": -303146133}, {"key": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "size": 1274, "crc": 551835724}, {"key": "androidx/compose/runtime/MutableLongState.class", "name": "androidx/compose/runtime/MutableLongState.class", "size": 2141, "crc": 890262603}, {"key": "androidx/compose/runtime/MutableScatterMultiMap.class", "name": "androidx/compose/runtime/MutableScatterMultiMap.class", "size": 6871, "crc": 914626874}, {"key": "androidx/compose/runtime/MutableState.class", "name": "androidx/compose/runtime/MutableState.class", "size": 1124, "crc": 1883532302}, {"key": "androidx/compose/runtime/NeverEqualPolicy.class", "name": "androidx/compose/runtime/NeverEqualPolicy.class", "size": 1398, "crc": -633449108}, {"key": "androidx/compose/runtime/NoLiveLiterals.class", "name": "androidx/compose/runtime/NoLiveLiterals.class", "size": 865, "crc": -1997887906}, {"key": "androidx/compose/runtime/NonRestartableComposable.class", "name": "androidx/compose/runtime/NonRestartableComposable.class", "size": 865, "crc": 524130731}, {"key": "androidx/compose/runtime/NonSkippableComposable.class", "name": "androidx/compose/runtime/NonSkippableComposable.class", "size": 859, "crc": -318800196}, {"key": "androidx/compose/runtime/OffsetApplier.class", "name": "androidx/compose/runtime/OffsetApplier.class", "size": 4136, "crc": 2056112640}, {"key": "androidx/compose/runtime/OpaqueKey.class", "name": "androidx/compose/runtime/OpaqueKey.class", "size": 2287, "crc": -380305036}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "size": 2023, "crc": -1002366417}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "size": 1162, "crc": 231433740}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "size": 2282, "crc": 766277014}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "size": 2014, "crc": -529458880}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "size": 1157, "crc": -1971679964}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "size": 2271, "crc": 308437552}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "size": 1996, "crc": 1245760980}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "size": 1147, "crc": -828468465}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "size": 2229, "crc": 169229765}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "size": 2005, "crc": 1946890528}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "size": 1152, "crc": -1223816640}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "size": 2260, "crc": -1075727579}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "size": 4148, "crc": -487554878}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "size": 1292, "crc": 1511734866}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "size": 3546, "crc": -1196789295}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "size": 1977, "crc": 1745961350}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "size": 5448, "crc": -1905374807}, {"key": "androidx/compose/runtime/Pending$keyMap$2.class", "name": "androidx/compose/runtime/Pending$keyMap$2.class", "size": 2478, "crc": 1520853295}, {"key": "androidx/compose/runtime/Pending.class", "name": "androidx/compose/runtime/Pending.class", "size": 14360, "crc": 220312480}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "size": 1449, "crc": 1544038335}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "size": 2777, "crc": 1159030359}, {"key": "androidx/compose/runtime/PreconditionsKt.class", "name": "androidx/compose/runtime/PreconditionsKt.class", "size": 1878, "crc": 1661584296}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "size": 1641, "crc": 244497337}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "size": 2164, "crc": -505770364}, {"key": "androidx/compose/runtime/PrioritySet.class", "name": "androidx/compose/runtime/PrioritySet.class", "size": 4936, "crc": 586797447}, {"key": "androidx/compose/runtime/ProduceFrameSignal.class", "name": "androidx/compose/runtime/ProduceFrameSignal.class", "size": 6108, "crc": 1036451212}, {"key": "androidx/compose/runtime/ProduceStateScope.class", "name": "androidx/compose/runtime/ProduceStateScope.class", "size": 1244, "crc": -450016262}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "size": 1864, "crc": 279138295}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl.class", "size": 5622, "crc": -2043181431}, {"key": "androidx/compose/runtime/ProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ProvidableCompositionLocal.class", "size": 5501, "crc": 1965789537}, {"key": "androidx/compose/runtime/ProvidedValue.class", "name": "androidx/compose/runtime/ProvidedValue.class", "size": 5840, "crc": 1770766222}, {"key": "androidx/compose/runtime/ReadOnlyComposable.class", "name": "androidx/compose/runtime/ReadOnlyComposable.class", "size": 938, "crc": -2111181013}, {"key": "androidx/compose/runtime/RecomposeScope.class", "name": "androidx/compose/runtime/RecomposeScope.class", "size": 451, "crc": 189986874}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "size": 5009, "crc": -40483197}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "size": 5761, "crc": 955730557}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "size": 2988, "crc": 406655405}, {"key": "androidx/compose/runtime/RecomposeScopeImpl.class", "name": "androidx/compose/runtime/RecomposeScopeImpl.class", "size": 20500, "crc": -704056808}, {"key": "androidx/compose/runtime/RecomposeScopeImplKt.class", "name": "androidx/compose/runtime/RecomposeScopeImplKt.class", "size": 1593, "crc": 939409895}, {"key": "androidx/compose/runtime/RecomposeScopeOwner.class", "name": "androidx/compose/runtime/RecomposeScopeOwner.class", "size": 1116, "crc": 1856969226}, {"key": "androidx/compose/runtime/Recomposer$Companion.class", "name": "androidx/compose/runtime/Recomposer$Companion.class", "size": 10923, "crc": -1663819605}, {"key": "androidx/compose/runtime/Recomposer$HotReloadable.class", "name": "androidx/compose/runtime/Recomposer$HotReloadable.class", "size": 2060, "crc": -984295542}, {"key": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "name": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "size": 1378, "crc": -1937361286}, {"key": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "name": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "size": 9167, "crc": 2029095826}, {"key": "androidx/compose/runtime/Recomposer$State.class", "name": "androidx/compose/runtime/Recomposer$State.class", "size": 1749, "crc": 883995846}, {"key": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "name": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "size": 3426, "crc": -1403053730}, {"key": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "name": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "size": 3807, "crc": -1916944648}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "size": 3882, "crc": 1221631525}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "size": 4769, "crc": 1779040012}, {"key": "androidx/compose/runtime/Recomposer$join$2.class", "name": "androidx/compose/runtime/Recomposer$join$2.class", "size": 3335, "crc": -667128618}, {"key": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "name": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "size": 4218, "crc": -1620972231}, {"key": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "size": 1567, "crc": 101131086}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "size": 4069, "crc": -777386599}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "size": 8659, "crc": -1708246219}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "size": 9443, "crc": -73834335}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "size": 2165, "crc": 2131721150}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "size": 9387, "crc": 849750552}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "size": 17861, "crc": 462677536}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "size": 15302, "crc": -132993131}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "size": 6259, "crc": -427550778}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "size": 4146, "crc": 1829584619}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "size": 14993, "crc": 1634848105}, {"key": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "size": 2070, "crc": 1432512284}, {"key": "androidx/compose/runtime/Recomposer.class", "name": "androidx/compose/runtime/Recomposer.class", "size": 70877, "crc": -704097874}, {"key": "androidx/compose/runtime/RecomposerErrorInfo.class", "name": "androidx/compose/runtime/RecomposerErrorInfo.class", "size": 781, "crc": 824971784}, {"key": "androidx/compose/runtime/RecomposerInfo.class", "name": "androidx/compose/runtime/RecomposerInfo.class", "size": 1076, "crc": 1345748937}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "size": 3519, "crc": 1890766184}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "size": 4974, "crc": -1165585936}, {"key": "androidx/compose/runtime/RecomposerKt.class", "name": "androidx/compose/runtime/RecomposerKt.class", "size": 4782, "crc": -392713361}, {"key": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "name": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "size": 1461, "crc": 1435341165}, {"key": "androidx/compose/runtime/RelativeGroupPath.class", "name": "androidx/compose/runtime/RelativeGroupPath.class", "size": 1659, "crc": 1599020746}, {"key": "androidx/compose/runtime/RememberManager.class", "name": "androidx/compose/runtime/RememberManager.class", "size": 1380, "crc": -1969705494}, {"key": "androidx/compose/runtime/RememberObserver.class", "name": "androidx/compose/runtime/RememberObserver.class", "size": 526, "crc": 928327424}, {"key": "androidx/compose/runtime/RememberObserverHolder.class", "name": "androidx/compose/runtime/RememberObserverHolder.class", "size": 1830, "crc": -1276314851}, {"key": "androidx/compose/runtime/ReusableComposition.class", "name": "androidx/compose/runtime/ReusableComposition.class", "size": 1019, "crc": 149615240}, {"key": "androidx/compose/runtime/ReusableRememberObserver.class", "name": "androidx/compose/runtime/ReusableRememberObserver.class", "size": 502, "crc": -413308931}, {"key": "androidx/compose/runtime/ScopeInvalidated.class", "name": "androidx/compose/runtime/ScopeInvalidated.class", "size": 804, "crc": -1001040052}, {"key": "androidx/compose/runtime/ScopeUpdateScope.class", "name": "androidx/compose/runtime/ScopeUpdateScope.class", "size": 869, "crc": 478004799}, {"key": "androidx/compose/runtime/SdkStubsFallbackFrameClock$withFrameNanos$2.class", "name": "androidx/compose/runtime/SdkStubsFallbackFrameClock$withFrameNanos$2.class", "size": 3848, "crc": -1699212202}, {"key": "androidx/compose/runtime/SdkStubsFallbackFrameClock.class", "name": "androidx/compose/runtime/SdkStubsFallbackFrameClock.class", "size": 4211, "crc": -1607941878}, {"key": "androidx/compose/runtime/SkippableUpdater.class", "name": "androidx/compose/runtime/SkippableUpdater.class", "size": 3961, "crc": 427653778}, {"key": "androidx/compose/runtime/SlotReader.class", "name": "androidx/compose/runtime/SlotReader.class", "size": 17160, "crc": 776327285}, {"key": "androidx/compose/runtime/SlotTable.class", "name": "androidx/compose/runtime/SlotTable.class", "size": 36937, "crc": 729519244}, {"key": "androidx/compose/runtime/SlotTableGroup.class", "name": "androidx/compose/runtime/SlotTableGroup.class", "size": 8836, "crc": 1873722177}, {"key": "androidx/compose/runtime/SlotTableKt.class", "name": "androidx/compose/runtime/SlotTableKt.class", "size": 19763, "crc": 953020864}, {"key": "androidx/compose/runtime/SlotWriter$Companion.class", "name": "androidx/compose/runtime/SlotWriter$Companion.class", "size": 11227, "crc": 1548768421}, {"key": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "name": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "size": 2137, "crc": -860065617}, {"key": "androidx/compose/runtime/SlotWriter.class", "name": "androidx/compose/runtime/SlotWriter.class", "size": 64411, "crc": -1683393794}, {"key": "androidx/compose/runtime/SnapshotContextElementImpl.class", "name": "androidx/compose/runtime/SnapshotContextElementImpl.class", "size": 5025, "crc": 1882400551}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "size": 1645, "crc": 1577886748}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "size": 2173, "crc": 1442149848}, {"key": "androidx/compose/runtime/SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt.class", "size": 1609, "crc": 480514241}, {"key": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "size": 2118, "crc": -1816137362}, {"key": "androidx/compose/runtime/SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt.class", "size": 1621, "crc": 1222965091}, {"key": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "size": 2139, "crc": -1494893383}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "size": 1796, "crc": 366479906}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "size": 1499, "crc": -1323938551}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "size": 9567, "crc": -1040018091}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "size": 1787, "crc": 1724124814}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "size": 1490, "crc": 1184659762}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "size": 9520, "crc": 760002397}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "size": 1769, "crc": 2132402881}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "size": 1469, "crc": -889409683}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "size": 9148, "crc": -1787766372}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "size": 1778, "crc": 1501617301}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "size": 1481, "crc": -808084296}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "size": 8696, "crc": -1950826505}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "size": 2037, "crc": -1720522210}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "size": 1561, "crc": -1849947633}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "size": 9903, "crc": 597578973}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "size": 1143, "crc": 1886515562}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy.class", "size": 1462, "crc": 986125056}, {"key": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "name": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "size": 2671, "crc": -901759764}, {"key": "androidx/compose/runtime/SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt.class", "size": 11204, "crc": -2072084221}, {"key": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "size": 8030, "crc": 1966721798}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "size": 4350, "crc": 497225842}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "size": 4368, "crc": -936811327}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "size": 4386, "crc": -289227731}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "size": 4404, "crc": 661708023}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "size": 4369, "crc": -1821881217}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "size": 13144, "crc": -1377406028}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "size": 1909, "crc": -1930584277}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "size": 2004, "crc": 1295399806}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "size": 4144, "crc": -1753816291}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "size": 5049, "crc": 2112364732}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "size": 3126, "crc": 670015985}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "size": 7623, "crc": 1257851384}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "size": 9893, "crc": 2093966925}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "size": 8936, "crc": 1059917538}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "size": 2137, "crc": -1658047304}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "size": 9184, "crc": -1484728010}, {"key": "androidx/compose/runtime/SnapshotThreadLocal.class", "name": "androidx/compose/runtime/SnapshotThreadLocal.class", "size": 3706, "crc": 626297437}, {"key": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "size": 5141, "crc": 1342569989}, {"key": "androidx/compose/runtime/SourceInformationGroupIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupIterator.class", "size": 4664, "crc": -1927840912}, {"key": "androidx/compose/runtime/SourceInformationGroupPath.class", "name": "androidx/compose/runtime/SourceInformationGroupPath.class", "size": 1145, "crc": 303030074}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "size": 4685, "crc": 599332944}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "size": 2832, "crc": 792186274}, {"key": "androidx/compose/runtime/Stable.class", "name": "androidx/compose/runtime/Stable.class", "size": 1019, "crc": -797317208}, {"key": "androidx/compose/runtime/StableMarker.class", "name": "androidx/compose/runtime/StableMarker.class", "size": 931, "crc": 1906422324}, {"key": "androidx/compose/runtime/Stack.class", "name": "androidx/compose/runtime/Stack.class", "size": 2409, "crc": 1131432970}, {"key": "androidx/compose/runtime/State.class", "name": "androidx/compose/runtime/State.class", "size": 637, "crc": 245308741}, {"key": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "name": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "size": 2100, "crc": -1465733730}, {"key": "androidx/compose/runtime/StaticValueHolder.class", "name": "androidx/compose/runtime/StaticValueHolder.class", "size": 3939, "crc": -654088811}, {"key": "androidx/compose/runtime/StructuralEqualityPolicy.class", "name": "androidx/compose/runtime/StructuralEqualityPolicy.class", "size": 1483, "crc": 2102532343}, {"key": "androidx/compose/runtime/ThreadLocal.class", "name": "androidx/compose/runtime/ThreadLocal.class", "size": 1878, "crc": -2108795344}, {"key": "androidx/compose/runtime/Trace.class", "name": "androidx/compose/runtime/Trace.class", "size": 1343, "crc": 1794700392}, {"key": "androidx/compose/runtime/TraceKt.class", "name": "androidx/compose/runtime/TraceKt.class", "size": 1508, "crc": 1362521038}, {"key": "androidx/compose/runtime/UnboxedDoubleState.class", "name": "androidx/compose/runtime/UnboxedDoubleState.class", "size": 2167, "crc": 783037081}, {"key": "androidx/compose/runtime/UnboxedFloatState.class", "name": "androidx/compose/runtime/UnboxedFloatState.class", "size": 2156, "crc": 837039512}, {"key": "androidx/compose/runtime/UnboxedIntState.class", "name": "androidx/compose/runtime/UnboxedIntState.class", "size": 2144, "crc": -801134785}, {"key": "androidx/compose/runtime/UnboxedLongState.class", "name": "androidx/compose/runtime/UnboxedLongState.class", "size": 2145, "crc": -1230904531}, {"key": "androidx/compose/runtime/Updater$init$1.class", "name": "androidx/compose/runtime/Updater$init$1.class", "size": 1736, "crc": 1924190097}, {"key": "androidx/compose/runtime/Updater$reconcile$1.class", "name": "androidx/compose/runtime/Updater$reconcile$1.class", "size": 1751, "crc": 46724730}, {"key": "androidx/compose/runtime/Updater.class", "name": "androidx/compose/runtime/Updater.class", "size": 6382, "crc": -297844778}, {"key": "androidx/compose/runtime/ValueHolder.class", "name": "androidx/compose/runtime/ValueHolder.class", "size": 1266, "crc": 318967962}, {"key": "androidx/compose/runtime/WeakReference.class", "name": "androidx/compose/runtime/WeakReference.class", "size": 1055, "crc": -1321389368}, {"key": "androidx/compose/runtime/changelist/ChangeList.class", "name": "androidx/compose/runtime/changelist/ChangeList.class", "size": 62535, "crc": 944858965}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "size": 956, "crc": 690481447}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "size": 19238, "crc": -1938361245}, {"key": "androidx/compose/runtime/changelist/FixupList.class", "name": "androidx/compose/runtime/changelist/FixupList.class", "size": 14770, "crc": -1806733040}, {"key": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "name": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "size": 3563, "crc": 1009180238}, {"key": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "name": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "size": 4517, "crc": -379455553}, {"key": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "name": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "size": 4769, "crc": 1485271287}, {"key": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "size": 5316, "crc": 1479396080}, {"key": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "size": 6795, "crc": 887777442}, {"key": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "size": 2118, "crc": -705389636}, {"key": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "name": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "size": 4743, "crc": -876837386}, {"key": "androidx/compose/runtime/changelist/Operation$Downs.class", "name": "androidx/compose/runtime/changelist/Operation$Downs.class", "size": 4019, "crc": -1089433806}, {"key": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "size": 4270, "crc": -1333371418}, {"key": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "size": 2003, "crc": -1488832015}, {"key": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "name": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "size": 2424, "crc": -489695265}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "size": 3754, "crc": -1798018939}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "size": 2030, "crc": 946001723}, {"key": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "size": 5500, "crc": 811313129}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "size": 4396, "crc": 1961581574}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "size": 6256, "crc": 390222489}, {"key": "androidx/compose/runtime/changelist/Operation$IntParameter.class", "name": "androidx/compose/runtime/changelist/Operation$IntParameter.class", "size": 2280, "crc": 415032738}, {"key": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "size": 3569, "crc": 1052679876}, {"key": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "size": 4209, "crc": -1682747190}, {"key": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "name": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "size": 2412, "crc": -422186996}, {"key": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "size": 4993, "crc": -164915560}, {"key": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "name": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "size": 5130, "crc": -223258510}, {"key": "androidx/compose/runtime/changelist/Operation$Remember.class", "name": "androidx/compose/runtime/changelist/Operation$Remember.class", "size": 3708, "crc": -2118292370}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "size": 2102, "crc": -1040346221}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "size": 3910, "crc": -746045846}, {"key": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "name": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "size": 1978, "crc": -679512641}, {"key": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "name": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "size": 3692, "crc": -1300030981}, {"key": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "size": 2026, "crc": -1487846168}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "size": 2274, "crc": -1024653459}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "size": 6292, "crc": -744192610}, {"key": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "name": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "size": 5110, "crc": -198120570}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "size": 6594, "crc": 165585434}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "size": 3619, "crc": -1045507978}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "size": 4264, "crc": -1735172914}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "size": 5235, "crc": 1811826776}, {"key": "androidx/compose/runtime/changelist/Operation$Ups.class", "name": "androidx/compose/runtime/changelist/Operation$Ups.class", "size": 3660, "crc": 1560341086}, {"key": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "name": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "size": 2309, "crc": -990340337}, {"key": "androidx/compose/runtime/changelist/Operation.class", "name": "androidx/compose/runtime/changelist/Operation.class", "size": 9766, "crc": 389332449}, {"key": "androidx/compose/runtime/changelist/OperationArgContainer.class", "name": "androidx/compose/runtime/changelist/OperationArgContainer.class", "size": 1133, "crc": -1452498303}, {"key": "androidx/compose/runtime/changelist/OperationKt$releaseMovableGroupAtCurrent$movableContentRecomposeScopeOwner$1.class", "name": "androidx/compose/runtime/changelist/OperationKt$releaseMovableGroupAtCurrent$movableContentRecomposeScopeOwner$1.class", "size": 3153, "crc": -1860681113}, {"key": "androidx/compose/runtime/changelist/OperationKt.class", "name": "androidx/compose/runtime/changelist/OperationKt.class", "size": 8454, "crc": 303777098}, {"key": "androidx/compose/runtime/changelist/Operations$Companion.class", "name": "androidx/compose/runtime/changelist/Operations$Companion.class", "size": 936, "crc": 402820299}, {"key": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "name": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "size": 3033, "crc": -320190266}, {"key": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "name": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "size": 6629, "crc": 89248027}, {"key": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "name": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "size": 1785, "crc": 1504895459}, {"key": "androidx/compose/runtime/changelist/Operations.class", "name": "androidx/compose/runtime/changelist/Operations.class", "size": 20199, "crc": 566155857}, {"key": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "name": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "size": 1414, "crc": -871691524}, {"key": "androidx/compose/runtime/collection/ActualIntMap_androidKt.class", "name": "androidx/compose/runtime/collection/ActualIntMap_androidKt.class", "size": 452, "crc": 940011615}, {"key": "androidx/compose/runtime/collection/IntMap.class", "name": "androidx/compose/runtime/collection/IntMap.class", "size": 2639, "crc": -84108942}, {"key": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "name": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "size": 6883, "crc": 1185450767}, {"key": "androidx/compose/runtime/collection/MutableVector$SubList.class", "name": "androidx/compose/runtime/collection/MutableVector$SubList.class", "size": 8230, "crc": -282584805}, {"key": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "name": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "size": 2723, "crc": 2019867414}, {"key": "androidx/compose/runtime/collection/MutableVector.class", "name": "androidx/compose/runtime/collection/MutableVector.class", "size": 27069, "crc": -338738155}, {"key": "androidx/compose/runtime/collection/MutableVectorKt.class", "name": "androidx/compose/runtime/collection/MutableVectorKt.class", "size": 5133, "crc": -1612959218}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "size": 7029, "crc": 76814607}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "size": 5188, "crc": 161122273}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "size": 6766, "crc": 26471146}, {"key": "androidx/compose/runtime/collection/ScopeMap.class", "name": "androidx/compose/runtime/collection/ScopeMap.class", "size": 16619, "crc": 1127433322}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "size": 44671, "crc": 871634668}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "size": 724, "crc": -1928243005}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "size": 2887, "crc": -2145684073}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "size": 1844, "crc": -2034045203}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "size": 1799, "crc": 2050427298}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "size": 975, "crc": 782172065}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "size": 1289, "crc": -249050980}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "size": 3016, "crc": -503479167}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "size": 1634, "crc": 911618663}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "size": 4131, "crc": -106511572}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "size": 1264, "crc": 1103560205}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "size": 2703, "crc": -317623062}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "size": 1623, "crc": -596983889}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "size": 3179, "crc": 732318457}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "size": 4299, "crc": -1386972210}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "size": 6270, "crc": 1852452552}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "size": 7711, "crc": 1526954162}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "size": 1721, "crc": 2273184}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "size": 2990, "crc": 1838372787}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "size": 1902, "crc": 87550251}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "size": 1937, "crc": 1070919301}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "size": 8659, "crc": 978785767}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "size": 2135, "crc": 881186825}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "size": 1297, "crc": -1211415870}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "size": 16461, "crc": -707123758}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "size": 1827, "crc": 611938006}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "size": 30259, "crc": 453154772}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "size": 3183, "crc": -1554422731}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "size": 5890, "crc": -1961032283}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "size": 1905, "crc": -11087310}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "size": 1549, "crc": -2008563037}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "size": 12958, "crc": -1879553808}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "size": 4194, "crc": 1683868701}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "size": 2508, "crc": -617376571}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "size": 2251, "crc": 2061539731}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "size": 3697, "crc": -979692211}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "size": 3085, "crc": 421999246}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "size": 2323, "crc": 259626929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "size": 13005, "crc": -378948757}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "size": 5682, "crc": 173452538}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "size": 11551, "crc": 1631507063}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "size": 7506, "crc": 2076585778}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "size": 4976, "crc": 1577746967}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "size": 4196, "crc": -580561192}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "size": 3333, "crc": 1812292338}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "size": 2775, "crc": -276552489}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "size": 3171, "crc": 22354424}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "size": 2781, "crc": -591056866}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "size": 515, "crc": -1996005144}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "size": 4646, "crc": 1811032799}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "size": 2792, "crc": 1181019558}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "size": 3119, "crc": -168332770}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "size": 2650, "crc": -62409802}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "size": 3167, "crc": 2108044832}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "size": 2656, "crc": -315869199}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "size": 1492, "crc": -832940801}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "size": 4371, "crc": 631278538}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "size": 43959, "crc": 125910739}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "size": 4783, "crc": -57188897}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "size": 2244, "crc": 354892509}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "size": 1749, "crc": 2059705273}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "size": 4364, "crc": 1551772354}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "size": 3188, "crc": 777449415}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "size": 1752, "crc": 940979941}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "size": 1972, "crc": 1692139379}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "size": 11252, "crc": 1657885459}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "size": 10465, "crc": 103244544}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "size": 5603, "crc": 1764774349}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "size": 6258, "crc": 271923600}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "size": 1486, "crc": 629664787}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "size": 38469, "crc": -953162557}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "size": 3909, "crc": 1185127987}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "size": 2777, "crc": -189832842}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "size": 3942, "crc": -2000177510}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "size": 3575, "crc": 335419055}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "size": 3457, "crc": -1801938329}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "size": 2425, "crc": 719452645}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "size": 14518, "crc": -945778448}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "size": 9543, "crc": 7245491}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "size": 5100, "crc": -652661666}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "size": 4293, "crc": 722385342}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "size": 3424, "crc": 1546463599}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "size": 3237, "crc": 1484205499}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "size": 6585, "crc": -1290283123}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "size": 3262, "crc": -1128305648}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "size": 3323, "crc": -2144642758}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "size": 4518, "crc": -1863002464}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "size": 3944, "crc": -1443778485}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "size": 2954, "crc": 812845929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "size": 3355, "crc": 619265929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "size": 4437, "crc": 2005812120}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "size": 3002, "crc": 3421047}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "size": 3402, "crc": 116485536}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "size": 2611, "crc": -1922564181}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "size": 2030, "crc": -1739252680}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "size": 13264, "crc": 542254362}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "size": 8267, "crc": 1754261541}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "size": 4026, "crc": -200115814}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "size": 4239, "crc": 1912694572}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "size": 548, "crc": 244407076}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "size": 2806, "crc": -1969764508}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "size": 887, "crc": 600339257}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "size": 1387, "crc": 981379766}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "size": 3528, "crc": -286554118}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "size": 817, "crc": -591097335}, {"key": "androidx/compose/runtime/internal/ComposableLambda.class", "name": "androidx/compose/runtime/internal/ComposableLambda.class", "size": 9084, "crc": 621267890}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "size": 2022, "crc": -718195717}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "size": 2658, "crc": -1927806707}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "size": 2937, "crc": -1330991907}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "size": 3026, "crc": 129801193}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "size": 3111, "crc": -800993976}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "size": 3196, "crc": -1197528401}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "size": 3281, "crc": 505449138}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "size": 3366, "crc": -639840462}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "size": 3451, "crc": 331455212}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "size": 3536, "crc": 2109226588}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "size": 2102, "crc": 1146736659}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "size": 2182, "crc": -2076501991}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "size": 2262, "crc": 68090437}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "size": 2342, "crc": -711171389}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "size": 2422, "crc": 1103902397}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "size": 2502, "crc": 419483073}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "size": 2582, "crc": 267254581}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "size": 2662, "crc": -679419553}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "size": 50080, "crc": -246394984}, {"key": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "size": 6296, "crc": 1488278630}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN.class", "size": 739, "crc": 1833348103}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "size": 4012, "crc": 485376525}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "size": 6901, "crc": 1847096210}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "size": 5144, "crc": 426339715}, {"key": "androidx/compose/runtime/internal/Decoy.class", "name": "androidx/compose/runtime/internal/Decoy.class", "size": 1029, "crc": -2078636242}, {"key": "androidx/compose/runtime/internal/DecoyImplementation.class", "name": "androidx/compose/runtime/internal/DecoyImplementation.class", "size": 1006, "crc": -496063648}, {"key": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "name": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "size": 961, "crc": 1076525739}, {"key": "androidx/compose/runtime/internal/DecoyKt.class", "name": "androidx/compose/runtime/internal/DecoyKt.class", "size": 1079, "crc": 1261849177}, {"key": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "name": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "size": 1501, "crc": -859740172}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "size": 913, "crc": 870267617}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "size": 1297, "crc": 216572539}, {"key": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "name": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "size": 971, "crc": -366835767}, {"key": "androidx/compose/runtime/internal/IntRef.class", "name": "androidx/compose/runtime/internal/IntRef.class", "size": 1916, "crc": 1216340085}, {"key": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 560, "crc": -1863150101}, {"key": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "size": 965, "crc": -84332509}, {"key": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "size": 1026, "crc": -693514666}, {"key": "androidx/compose/runtime/internal/LiveLiteralKt.class", "name": "androidx/compose/runtime/internal/LiveLiteralKt.class", "size": 4360, "crc": -1177433970}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "size": 6608, "crc": -386737237}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "size": 1382, "crc": -381514527}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "size": 9602, "crc": 1528202554}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "size": 3332, "crc": -1297606436}, {"key": "androidx/compose/runtime/internal/StabilityInferred.class", "name": "androidx/compose/runtime/internal/StabilityInferred.class", "size": 955, "crc": -177704314}, {"key": "androidx/compose/runtime/internal/ThreadMap.class", "name": "androidx/compose/runtime/internal/ThreadMap.class", "size": 3914, "crc": 505637417}, {"key": "androidx/compose/runtime/internal/ThreadMap_jvmKt.class", "name": "androidx/compose/runtime/internal/ThreadMap_jvmKt.class", "size": 1601, "crc": 1730516084}, {"key": "androidx/compose/runtime/reflect/ComposableInfo.class", "name": "androidx/compose/runtime/reflect/ComposableInfo.class", "size": 3352, "crc": 2093087670}, {"key": "androidx/compose/runtime/reflect/ComposableMethod.class", "name": "androidx/compose/runtime/reflect/ComposableMethod.class", "size": 8239, "crc": 15479675}, {"key": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "name": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "size": 10619, "crc": 950071042}, {"key": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "name": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "size": 1009, "crc": 494427556}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$1$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$1$1$1.class", "size": 2793, "crc": -316452215}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1.class", "size": 3917, "crc": -713930480}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1.class", "size": 3627, "crc": 883796025}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "size": 6528, "crc": 1504885692}, {"key": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "name": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "size": 12852, "crc": -1481692209}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "size": 903, "crc": -2008158309}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "size": 36446, "crc": 1396437910}, {"key": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "size": 7776, "crc": 960607343}, {"key": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "size": 5983, "crc": -2119868759}, {"key": "androidx/compose/runtime/snapshots/ObserverHandle.class", "name": "androidx/compose/runtime/snapshots/ObserverHandle.class", "size": 462, "crc": 449216628}, {"key": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "name": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "size": 1543, "crc": 2008060952}, {"key": "androidx/compose/runtime/snapshots/ReaderKind.class", "name": "androidx/compose/runtime/snapshots/ReaderKind.class", "size": 3098, "crc": -1047107092}, {"key": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "size": 5621, "crc": 1023953046}, {"key": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "size": 18716, "crc": 2029727378}, {"key": "androidx/compose/runtime/snapshots/Snapshot.class", "name": "androidx/compose/runtime/snapshots/Snapshot.class", "size": 11903, "crc": 1691744507}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "size": 1259, "crc": -301302269}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "size": 1775, "crc": 2050448662}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "size": 1261, "crc": -248486829}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "size": 1403, "crc": -1160001039}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "size": 3157, "crc": 575677703}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "size": 1119, "crc": -1327373666}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "size": 1112, "crc": -1077834103}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "size": 1130, "crc": -498687761}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "size": 4866, "crc": -47690800}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "size": 438, "crc": 1851865193}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "size": 1193, "crc": 1712580653}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "size": 5032, "crc": 1987839504}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "size": 17338, "crc": -22410182}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSetKt.class", "size": 846, "crc": 637150015}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$advanceGlobalSnapshot$3.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$advanceGlobalSnapshot$3.class", "size": 1543, "crc": 2068308772}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "size": 1494, "crc": -345039565}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "size": 1857, "crc": 1255661525}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "size": 1860, "crc": 396124503}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "size": 3734, "crc": -656237005}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt.class", "size": 41823, "crc": 1539392105}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "size": 13664, "crc": 1674600517}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "size": 12587, "crc": 435984421}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "size": 2513, "crc": 140548567}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "size": 14896, "crc": -2085480308}, {"key": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "name": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "size": 979, "crc": 1303726931}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "size": 4707, "crc": -1358296567}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "size": 1749, "crc": -518108963}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "size": 1705, "crc": -237100260}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "size": 55481, "crc": -770021757}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "size": 2004, "crc": 255570255}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "size": 4513, "crc": 602378785}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "size": 30874, "crc": -986928495}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "size": 852, "crc": 1987451710}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "size": 2027, "crc": -1771378849}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "size": 42406, "crc": -396304124}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "size": 2256, "crc": 1558404608}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "size": 2454, "crc": -1973866177}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "size": 3730, "crc": -37099958}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "size": 19680, "crc": 1181441646}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "size": 6036, "crc": -301883879}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "size": 422, "crc": 1539315218}, {"key": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "name": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "size": 935, "crc": -589967162}, {"key": "androidx/compose/runtime/snapshots/StateListIterator.class", "name": "androidx/compose/runtime/snapshots/StateListIterator.class", "size": 4446, "crc": 776742699}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "size": 4051, "crc": 59183992}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "size": 2286, "crc": 2039138113}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "size": 5988, "crc": -1423834444}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "size": 1980, "crc": 970630391}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "size": 1985, "crc": -2058839914}, {"key": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "size": 1192, "crc": 68055110}, {"key": "androidx/compose/runtime/snapshots/StateObject.class", "name": "androidx/compose/runtime/snapshots/StateObject.class", "size": 1859, "crc": -1220529037}, {"key": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "name": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "size": 2717, "crc": -898159930}, {"key": "androidx/compose/runtime/snapshots/StateRecord.class", "name": "androidx/compose/runtime/snapshots/StateRecord.class", "size": 1886, "crc": 878509835}, {"key": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "name": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "size": 3338, "crc": -122209347}, {"key": "androidx/compose/runtime/snapshots/SubList.class", "name": "androidx/compose/runtime/snapshots/SubList.class", "size": 9830, "crc": -963773691}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "size": 10129, "crc": -423552587}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "size": 8249, "crc": 1641390447}, {"key": "androidx/compose/runtime/tooling/CompositionData.class", "name": "androidx/compose/runtime/tooling/CompositionData.class", "size": 1211, "crc": 376254531}, {"key": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "name": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "size": 1561, "crc": 578238820}, {"key": "androidx/compose/runtime/tooling/CompositionGroup.class", "name": "androidx/compose/runtime/tooling/CompositionGroup.class", "size": 2403, "crc": 1994065739}, {"key": "androidx/compose/runtime/tooling/CompositionObserver.class", "name": "androidx/compose/runtime/tooling/CompositionObserver.class", "size": 1194, "crc": 987247663}, {"key": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "name": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "size": 588, "crc": 247969004}, {"key": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "name": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "size": 2489, "crc": 168388353}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "size": 1401, "crc": 1470652688}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "size": 1567, "crc": 1708835644}, {"key": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "name": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "size": 929, "crc": 1824493179}, {"key": "META-INF/androidx.compose.runtime_runtime.version", "name": "META-INF/androidx.compose.runtime_runtime.version", "size": 6, "crc": 1621725393}, {"key": "META-INF/runtime_release.kotlin_module", "name": "META-INF/runtime_release.kotlin_module", "size": 2131, "crc": 1049419945}]