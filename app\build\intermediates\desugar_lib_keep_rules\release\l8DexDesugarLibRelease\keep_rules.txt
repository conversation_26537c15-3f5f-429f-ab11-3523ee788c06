-keep class j$.lang.Iterable$-CC {
  public static void $default$forEach(java.lang.Iterable, java.util.function.Consumer);
}
-keep enum j$.time.DayOfWeek {
  public java.lang.String getDisplayName(j$.time.format.TextStyle, java.util.Locale);
  public int getValue();
  public static j$.time.DayOfWeek[] values();
}
-keep class j$.time.Duration {
  public int compareTo(j$.time.Duration);
  public int getNano();
  public long getSeconds();
  public static j$.time.Duration ofMillis(long);
  public static j$.time.Duration ofSeconds(long, long);
  public long toMillis();
  j$.time.Duration ZERO;
}
-keep class j$.time.Instant {
  public j$.time.ZonedDateTime atZone(j$.time.ZoneId);
  public static j$.time.Instant ofEpochMilli(long);
  public long toEpochMilli();
}
-keep class j$.time.LocalDate {
  public j$.time.LocalDateTime atStartOfDay();
  public j$.time.LocalDateTime atTime(j$.time.LocalTime);
  public java.lang.String format(j$.time.format.DateTimeFormatter);
  public int getDayOfMonth();
  public j$.time.DayOfWeek getDayOfWeek();
  public j$.time.Month getMonth();
  public int getMonthValue();
  public int getYear();
  public int lengthOfMonth();
  public j$.time.LocalDate minusMonths(long);
  public static j$.time.LocalDate now();
  public static j$.time.LocalDate of(int, int, int);
  public static j$.time.LocalDate parse(java.lang.CharSequence, j$.time.format.DateTimeFormatter);
  public j$.time.LocalDate plusMonths(long);
}
-keep class j$.time.LocalDateTime {
  public j$.time.ZonedDateTime atZone(j$.time.ZoneId);
  public boolean isBefore(j$.time.chrono.ChronoLocalDateTime);
  public static j$.time.LocalDateTime now();
  public j$.time.LocalDateTime plusDays(long);
  public long toEpochSecond(j$.time.ZoneOffset);
  public j$.time.LocalDate toLocalDate();
}
-keep class j$.time.LocalTime {
  public java.lang.String format(j$.time.format.DateTimeFormatter);
  public int getHour();
  public int getMinute();
  public boolean isBefore(j$.time.LocalTime);
  public static j$.time.LocalTime now();
  public static j$.time.LocalTime of(int, int);
  j$.time.LocalTime MIDNIGHT;
}
-keep enum j$.time.Month {
  public int getValue();
}
-keep class j$.time.TimeConversions {
  public static java.time.Duration convert(j$.time.Duration);
  public static j$.time.Duration convert(java.time.Duration);
}
-keep class j$.time.ZoneId {
  public static j$.time.ZoneId of(java.lang.String);
  public static j$.time.ZoneId systemDefault();
}
-keep class j$.time.ZoneOffset {
  j$.time.ZoneOffset UTC;
}
-keep class j$.time.ZonedDateTime {
  public j$.time.Instant toInstant();
  public j$.time.LocalDate toLocalDate();
  public j$.time.ZonedDateTime withDayOfMonth(int);
}
-keep interface j$.time.chrono.ChronoLocalDateTime {
}
-keep class j$.time.chrono.Chronology$-CC {
  public static j$.time.chrono.Chronology ofLocale(java.util.Locale);
}
-keep interface j$.time.chrono.Chronology {
}
-keep class j$.time.format.DateTimeFormatter {
  public static j$.time.format.DateTimeFormatter ofPattern(java.lang.String);
  public static j$.time.format.DateTimeFormatter ofPattern(java.lang.String, java.util.Locale);
  public j$.time.format.DateTimeFormatter withDecimalStyle(j$.time.format.DecimalStyle);
}
-keep class j$.time.format.DateTimeFormatterBuilder {
  public static java.lang.String getLocalizedDateTimePattern(j$.time.format.FormatStyle, j$.time.format.FormatStyle, j$.time.chrono.Chronology, java.util.Locale);
}
-keep class j$.time.format.DateTimeParseException {
}
-keep class j$.time.format.DecimalStyle {
  public static j$.time.format.DecimalStyle of(java.util.Locale);
}
-keep enum j$.time.format.FormatStyle {
  j$.time.format.FormatStyle SHORT;
}
-keep enum j$.time.format.TextStyle {
  j$.time.format.TextStyle FULL;
  j$.time.format.TextStyle NARROW;
}
-keep enum j$.time.temporal.ChronoUnit {
  public j$.time.Duration getDuration();
  j$.time.temporal.ChronoUnit MILLIS;
}
-keep class j$.time.temporal.WeekFields {
  public j$.time.DayOfWeek getFirstDayOfWeek();
  public static j$.time.temporal.WeekFields of(java.util.Locale);
}
-keep class j$.util.Collection$-CC {
  public static j$.util.stream.Stream $default$parallelStream(java.util.Collection);
  public static boolean $default$removeIf(java.util.Collection, java.util.function.Predicate);
  public static j$.util.Spliterator $default$spliterator(java.util.Collection);
  public static j$.util.stream.Stream $default$stream(java.util.Collection);
  public static java.lang.Object[] $default$toArray(java.util.Collection, java.util.function.IntFunction);
}
-keep interface j$.util.Collection {
  public void forEach(java.util.function.Consumer);
  public j$.util.stream.Stream parallelStream();
  public boolean removeIf(java.util.function.Predicate);
  public j$.util.Spliterator spliterator();
  public j$.util.stream.Stream stream();
  public java.lang.Object[] toArray(java.util.function.IntFunction);
}
-keep class j$.util.Comparator$-CC {
  public static java.util.Comparator $default$thenComparing(java.util.Comparator, java.util.Comparator);
  public static java.util.Comparator $default$thenComparing(java.util.Comparator, java.util.function.Function);
  public static java.util.Comparator $default$thenComparing(java.util.Comparator, java.util.function.Function, java.util.Comparator);
  public static java.util.Comparator $default$thenComparingDouble(java.util.Comparator, java.util.function.ToDoubleFunction);
  public static java.util.Comparator $default$thenComparingInt(java.util.Comparator, java.util.function.ToIntFunction);
  public static java.util.Comparator $default$thenComparingLong(java.util.Comparator, java.util.function.ToLongFunction);
}
-keep interface j$.util.Comparator {
  public java.util.Comparator reversed();
  public java.util.Comparator thenComparing(java.util.Comparator);
  public java.util.Comparator thenComparing(java.util.function.Function);
  public java.util.Comparator thenComparing(java.util.function.Function, java.util.Comparator);
  public java.util.Comparator thenComparingDouble(java.util.function.ToDoubleFunction);
  public java.util.Comparator thenComparingInt(java.util.function.ToIntFunction);
  public java.util.Comparator thenComparingLong(java.util.function.ToLongFunction);
}
-keep class j$.util.DesugarCollections {
  public static java.util.Map synchronizedMap(java.util.Map);
}
-keep class j$.util.DesugarTimeZone {
  public static java.util.TimeZone getTimeZone(java.lang.String);
}
-keep class j$.util.List$-CC {
  public static j$.util.Spliterator $default$spliterator(java.util.List);
}
-keep interface j$.util.List {
  public void forEach(java.util.function.Consumer);
  public j$.util.stream.Stream parallelStream();
  public boolean removeIf(java.util.function.Predicate);
  public void replaceAll(java.util.function.UnaryOperator);
  public void sort(java.util.Comparator);
  public j$.util.Spliterator spliterator();
  public j$.util.stream.Stream stream();
  public java.lang.Object[] toArray(java.util.function.IntFunction);
}
-keep class j$.util.Map$-CC {
  public static java.lang.Object $default$compute(java.util.Map, java.lang.Object, java.util.function.BiFunction);
  public static java.lang.Object $default$computeIfAbsent(java.util.Map, java.lang.Object, java.util.function.Function);
  public static java.lang.Object $default$computeIfPresent(java.util.Map, java.lang.Object, java.util.function.BiFunction);
  public static void $default$forEach(java.util.Map, java.util.function.BiConsumer);
  public static java.lang.Object $default$getOrDefault(java.util.Map, java.lang.Object, java.lang.Object);
  public static java.lang.Object $default$merge(java.util.Map, java.lang.Object, java.lang.Object, java.util.function.BiFunction);
  public static java.lang.Object $default$putIfAbsent(java.util.Map, java.lang.Object, java.lang.Object);
  public static boolean $default$remove(java.util.Map, java.lang.Object, java.lang.Object);
  public static java.lang.Object $default$replace(java.util.Map, java.lang.Object, java.lang.Object);
  public static boolean $default$replace(java.util.Map, java.lang.Object, java.lang.Object, java.lang.Object);
  public static void $default$replaceAll(java.util.Map, java.util.function.BiFunction);
}
-keep class j$.util.Map$-EL {
  public static java.lang.Object getOrDefault(java.util.Map, java.lang.Object, java.lang.Object);
  public static boolean remove(java.util.Map, java.lang.Object, java.lang.Object);
}
-keep interface j$.util.Map {
  public java.lang.Object compute(java.lang.Object, java.util.function.BiFunction);
  public java.lang.Object computeIfAbsent(java.lang.Object, java.util.function.Function);
  public java.lang.Object computeIfPresent(java.lang.Object, java.util.function.BiFunction);
  public void forEach(java.util.function.BiConsumer);
  public java.lang.Object getOrDefault(java.lang.Object, java.lang.Object);
  public java.lang.Object merge(java.lang.Object, java.lang.Object, java.util.function.BiFunction);
  public java.lang.Object putIfAbsent(java.lang.Object, java.lang.Object);
  public boolean remove(java.lang.Object, java.lang.Object);
  public java.lang.Object replace(java.lang.Object, java.lang.Object);
  public boolean replace(java.lang.Object, java.lang.Object, java.lang.Object);
  public void replaceAll(java.util.function.BiFunction);
}
-keep class j$.util.Objects {
  public static boolean equals(java.lang.Object, java.lang.Object);
  public static int hash(java.lang.Object[]);
  public static boolean isNull(java.lang.Object);
  public static java.lang.Object requireNonNull(java.lang.Object);
  public static java.lang.Object requireNonNull(java.lang.Object, java.lang.String);
  public static java.lang.String toString(java.lang.Object);
}
-keep class j$.util.Optional {
  public java.lang.Object get();
  public boolean isPresent();
  public java.lang.Object orElse(java.lang.Object);
}
-keep interface j$.util.PrimitiveIterator$OfDouble {
}
-keep interface j$.util.PrimitiveIterator$OfInt {
}
-keep interface j$.util.PrimitiveIterator$OfLong {
}
-keep class j$.util.Set$-CC {
  public static j$.util.Spliterator $default$spliterator(java.util.Set);
}
-keep interface j$.util.Set {
  public j$.util.Spliterator spliterator();
}
-keep class j$.util.Spliterator$Wrapper {
  public static java.util.Spliterator convert(j$.util.Spliterator);
}
-keep interface j$.util.Spliterator {
}
-keep class j$.util.Spliterators {
  public static j$.util.Spliterator spliteratorUnknownSize(java.util.Iterator, int);
}
-keep class j$.util.concurrent.ConcurrentHashMap {
  public <init>();
  public java.lang.Object get(java.lang.Object);
  public java.lang.Object put(java.lang.Object, java.lang.Object);
}
-keep class j$.util.concurrent.ThreadLocalRandom {
  public static j$.util.concurrent.ThreadLocalRandom current();
  public double nextDouble(double);
  public int nextInt(int, int);
  public long nextLong(long);
  public long nextLong(long, long);
}
-keep class j$.util.function.BiFunction$-CC {
  public static java.util.function.BiFunction $default$andThen(java.util.function.BiFunction, java.util.function.Function);
}
-keep class j$.util.function.Consumer$-CC {
  public static java.util.function.Consumer $default$andThen(java.util.function.Consumer, java.util.function.Consumer);
}
-keep class j$.util.function.DoubleUnaryOperator$-CC {
  public static java.util.function.DoubleUnaryOperator $default$andThen(java.util.function.DoubleUnaryOperator, java.util.function.DoubleUnaryOperator);
  public static java.util.function.DoubleUnaryOperator $default$compose(java.util.function.DoubleUnaryOperator, java.util.function.DoubleUnaryOperator);
}
-keep interface j$.util.stream.BaseStream {
  public void close();
  public java.util.Iterator iterator();
}
-keep interface j$.util.stream.Collector {
}
-keep class j$.util.stream.Collectors {
  public static j$.util.stream.Collector toList();
}
-keep interface j$.util.stream.DoubleStream {
  public j$.util.PrimitiveIterator$OfDouble iterator();
  public double[] toArray();
}
-keep class j$.util.stream.IntStream$VivifiedWrapper {
  public static j$.util.stream.IntStream convert(java.util.stream.IntStream);
}
-keep class j$.util.stream.IntStream$Wrapper {
  public static java.util.stream.IntStream convert(j$.util.stream.IntStream);
}
-keep interface j$.util.stream.IntStream {
  public j$.util.PrimitiveIterator$OfInt iterator();
  public int[] toArray();
}
-keep interface j$.util.stream.LongStream {
  public j$.util.PrimitiveIterator$OfLong iterator();
  public long[] toArray();
}
-keep class j$.util.stream.Stream$Wrapper {
  public static java.util.stream.Stream convert(j$.util.stream.Stream);
}
-keep interface j$.util.stream.Stream {
  public java.lang.Object collect(j$.util.stream.Collector);
}
-keep class j$.util.stream.StreamSupport {
  public static j$.util.stream.Stream stream(java.util.function.Supplier, int, boolean);
}
-keep interface java.util.function.BiConsumer {
}
-keep interface java.util.function.BiFunction {
}
-keep interface java.util.function.Consumer {
  public void accept(java.lang.Object);
}
-keep interface java.util.function.DoubleUnaryOperator {
  public double applyAsDouble(double);
}
-keep interface java.util.function.Function {
}
-keep interface java.util.function.IntConsumer {
  public void accept(int);
}
-keep interface java.util.function.IntFunction {
}
-keep interface java.util.function.Predicate {
  public boolean test(java.lang.Object);
}
-keep interface java.util.function.Supplier {
  public java.lang.Object get();
}
-keep interface java.util.function.ToDoubleFunction {
}
-keep interface java.util.function.ToIntFunction {
}
-keep interface java.util.function.ToLongFunction {
}
-keep interface java.util.function.UnaryOperator {
}
