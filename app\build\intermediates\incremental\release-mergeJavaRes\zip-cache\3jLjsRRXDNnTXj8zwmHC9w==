[{"key": "androidx/lifecycle/compose/DropUnlessLifecycleKt$dropUnlessStateIsAtLeast$2$1.class", "name": "androidx/lifecycle/compose/DropUnlessLifecycleKt$dropUnlessStateIsAtLeast$2$1.class", "size": 2223, "crc": -1934489251}, {"key": "androidx/lifecycle/compose/DropUnlessLifecycleKt.class", "name": "androidx/lifecycle/compose/DropUnlessLifecycleKt.class", "size": 7133, "crc": 2056915164}, {"key": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$1.class", "name": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$1.class", "size": 1954, "crc": -296086656}, {"key": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$2$1.class", "name": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$2$1.class", "size": 2044, "crc": 950390208}, {"key": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$2.class", "name": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1$2.class", "size": 4186, "crc": -1329569787}, {"key": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1.class", "name": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1$1.class", "size": 4939, "crc": -2106914640}, {"key": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1.class", "name": "androidx/lifecycle/compose/FlowExtKt$collectAsStateWithLifecycle$1$1.class", "size": 4876, "crc": 293212992}, {"key": "androidx/lifecycle/compose/FlowExtKt.class", "name": "androidx/lifecycle/compose/FlowExtKt.class", "size": 9558, "crc": -834266539}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$1$1$invoke$$inlined$onDispose$1.class", "size": 2513, "crc": -780540473}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$1$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$1$1.class", "size": 4943, "crc": 1643586512}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$2.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleEventEffect$2.class", "size": 2404, "crc": -1767044484}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$1.class", "size": 2447, "crc": 1716323028}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$2.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$2.class", "size": 2547, "crc": 556870026}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$3.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$3.class", "size": 2647, "crc": 1176270009}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$4.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$4.class", "size": 2565, "crc": -1924009614}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$5.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffect$5.class", "size": 2347, "crc": 1002970599}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1$WhenMappings.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1$WhenMappings.class", "size": 927, "crc": -1359658987}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1$invoke$$inlined$onDispose$1.class", "size": 2917, "crc": -52751483}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$1$1.class", "size": 5972, "crc": -2131814480}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$2.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleResumeEffectImpl$2.class", "size": 2636, "crc": -1910579864}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$1.class", "size": 2438, "crc": 1767116956}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$2.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$2.class", "size": 2538, "crc": -2106430912}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$3.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$3.class", "size": 2638, "crc": 866595960}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$4.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$4.class", "size": 2556, "crc": -1414865657}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$5.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffect$5.class", "size": 2338, "crc": 35372927}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1$WhenMappings.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1$WhenMappings.class", "size": 923, "crc": -1650551702}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1$invoke$$inlined$onDispose$1.class", "size": 2907, "crc": 1412646238}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$1$1.class", "size": 5940, "crc": 1188990636}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$2.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt$LifecycleStartEffectImpl$2.class", "size": 2618, "crc": -854421333}, {"key": "androidx/lifecycle/compose/LifecycleEffectKt.class", "name": "androidx/lifecycle/compose/LifecycleEffectKt.class", "size": 34033, "crc": 517290110}, {"key": "androidx/lifecycle/compose/LifecycleExtKt.class", "name": "androidx/lifecycle/compose/LifecycleExtKt.class", "size": 2251, "crc": -1681412393}, {"key": "androidx/lifecycle/compose/LifecyclePauseOrDisposeEffectResult.class", "name": "androidx/lifecycle/compose/LifecyclePauseOrDisposeEffectResult.class", "size": 527, "crc": 739671877}, {"key": "androidx/lifecycle/compose/LifecycleResumePauseEffectScope$onPauseOrDispose$1.class", "name": "androidx/lifecycle/compose/LifecycleResumePauseEffectScope$onPauseOrDispose$1.class", "size": 2249, "crc": 1899651943}, {"key": "androidx/lifecycle/compose/LifecycleResumePauseEffectScope.class", "name": "androidx/lifecycle/compose/LifecycleResumePauseEffectScope.class", "size": 2233, "crc": 530226202}, {"key": "androidx/lifecycle/compose/LifecycleStartStopEffectScope$onStopOrDispose$1.class", "name": "androidx/lifecycle/compose/LifecycleStartStopEffectScope$onStopOrDispose$1.class", "size": 2221, "crc": -728451297}, {"key": "androidx/lifecycle/compose/LifecycleStartStopEffectScope.class", "name": "androidx/lifecycle/compose/LifecycleStartStopEffectScope.class", "size": 2217, "crc": -1512801718}, {"key": "androidx/lifecycle/compose/LifecycleStopOrDisposeEffectResult.class", "name": "androidx/lifecycle/compose/LifecycleStopOrDisposeEffectResult.class", "size": 522, "crc": 1739070122}, {"key": "androidx/lifecycle/compose/LocalLifecycleOwnerKt$LocalLifecycleOwner$1$1.class", "name": "androidx/lifecycle/compose/LocalLifecycleOwnerKt$LocalLifecycleOwner$1$1.class", "size": 1479, "crc": -1198594356}, {"key": "androidx/lifecycle/compose/LocalLifecycleOwnerKt.class", "name": "androidx/lifecycle/compose/LocalLifecycleOwnerKt.class", "size": 4698, "crc": -1285912257}, {"key": "META-INF/androidx.lifecycle_lifecycle-runtime-compose.version", "name": "META-INF/androidx.lifecycle_lifecycle-runtime-compose.version", "size": 6, "crc": -1787584022}, {"key": "META-INF/lifecycle-runtime-compose_release.kotlin_module", "name": "META-INF/lifecycle-runtime-compose_release.kotlin_module", "size": 146, "crc": -1059344406}]