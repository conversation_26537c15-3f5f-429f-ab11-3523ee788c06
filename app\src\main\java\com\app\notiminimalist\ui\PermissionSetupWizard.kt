package com.app.notiminimalist.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.app.notiminimalist.ui.theme.NotiBlue
import com.app.notiminimalist.ui.theme.NotiGray
import com.app.notiminimalist.utils.PermissionManager
import com.app.notiminimalist.utils.AccessibilityHelper

/**
 * Guided permission setup wizard that walks users through
 * all required permissions with explanations and visual indicators.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionSetupWizard(
    permissionManager: PermissionManager,
    onComplete: () -> Unit,
    onDismiss: () -> Unit
) {
    var currentStep by remember { mutableIntStateOf(0) }
    var permissionStates by remember { 
        mutableStateOf(
            PermissionStates(
                dnd = permissionManager.hasDndPermission(),
                exactAlarm = permissionManager.hasExactAlarmPermission(),
                notificationListener = permissionManager.hasNotificationListenerPermission()
            )
        )
    }
    
    // Update permission states when they change
    LaunchedEffect(Unit) {
        // Periodically check permission states (when returning from settings)
        while (true) {
            kotlinx.coroutines.delay(1000)
            permissionStates = PermissionStates(
                dnd = permissionManager.hasDndPermission(),
                exactAlarm = permissionManager.hasExactAlarmPermission(),
                notificationListener = permissionManager.hasNotificationListenerPermission()
            )
        }
    }
    
    val steps = listOf(
        PermissionStep(
            title = "Welcome to Setup",
            description = "Let's configure the necessary permissions for Notiminimalist to work reliably.",
            icon = Icons.Default.Settings,
            isRequired = false,
            isGranted = true
        ),
        PermissionStep(
            title = "Do Not Disturb Access",
            description = "This permission allows the app to automatically enable and disable Do Not Disturb mode during your scheduled hours.",
            icon = Icons.Default.Notifications,
            isRequired = true,
            isGranted = permissionStates.dnd,
            actionLabel = "Grant Permission",
            onAction = { permissionManager.requestDndPermission() }
        ),
        PermissionStep(
            title = "Exact Alarm Scheduling",
            description = "This permission ensures precise timing for starting and stopping notification silencing, even when your device is in power-saving mode.",
            icon = Icons.Default.DateRange,
            isRequired = true,
            isGranted = permissionStates.exactAlarm,
            actionLabel = "Grant Permission",
            onAction = { permissionManager.requestExactAlarmPermission() }
        ),
        PermissionStep(
            title = "Notification Listener",
            description = "This permission allows the app to automatically dismiss notifications from selected apps, providing seamless silencing.",
            icon = Icons.Default.Notifications,
            isRequired = true,
            isGranted = permissionStates.notificationListener,
            actionLabel = "Grant Permission",
            onAction = { permissionManager.requestNotificationListenerPermission() }
        ),
        PermissionStep(
            title = "Setup Complete!",
            description = "Great! All permissions have been configured. Notiminimalist is now ready to help you manage your notifications.",
            icon = Icons.Default.CheckCircle,
            isRequired = false,
            isGranted = true
        )
    )
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f)
                .semantics {
                    contentDescription = "Permission Setup Wizard, step ${currentStep + 1} of ${steps.size}"
                    role = Role.Button
                },
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
            ) {
                // Header with progress
                WizardHeader(
                    currentStep = currentStep,
                    totalSteps = steps.size,
                    onClose = onDismiss
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Main content
                WizardContent(
                    step = steps[currentStep],
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Navigation buttons
                WizardNavigation(
                    currentStep = currentStep,
                    totalSteps = steps.size,
                    canProceed = canProceedToNext(steps, currentStep),
                    onPrevious = { if (currentStep > 0) currentStep-- },
                    onNext = { 
                        if (currentStep < steps.size - 1) {
                            currentStep++
                        } else {
                            onComplete()
                        }
                    },
                    onSkip = { currentStep++ }
                )
            }
        }
    }
}

@Composable
private fun WizardHeader(
    currentStep: Int,
    totalSteps: Int,
    onClose: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Setup Wizard",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            IconButton(
                onClick = onClose,
                modifier = Modifier.semantics {
                    contentDescription = "Close setup wizard"
                }
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close"
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Progress indicator
        LinearProgressIndicator(
            progress = { (currentStep + 1).toFloat() / totalSteps },
            modifier = Modifier
                .fillMaxWidth()
                .semantics {
                    AccessibilityHelper.createProgressSemantics(
                        label = "Setup progress",
                        progress = (currentStep + 1).toFloat() / totalSteps
                    ).invoke(this)
                },
            color = NotiBlue
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Step ${currentStep + 1} of $totalSteps",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.semantics {
                liveRegion = LiveRegionMode.Polite
            }
        )
    }
}

@Composable
private fun WizardContent(
    step: PermissionStep,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Step icon with status
        Box(
            modifier = Modifier.size(80.dp),
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier.fillMaxSize(),
                shape = RoundedCornerShape(40.dp),
                colors = CardDefaults.cardColors(
                    containerColor = when {
                        step.isGranted -> Color(0xFF4CAF50)
                        step.isRequired -> NotiBlue
                        else -> NotiGray
                    }
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = if (step.isGranted) Icons.Default.CheckCircle else step.icon,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(40.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Step title
        Text(
            text = step.title,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.semantics {
                heading()
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Step description
        Text(
            text = step.description,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
            lineHeight = 24.sp
        )
        
        // Status indicator
        Spacer(modifier = Modifier.height(24.dp))
        
        PermissionStatusIndicator(
            isGranted = step.isGranted,
            isRequired = step.isRequired
        )
        
        // Action button for permissions
        if (step.onAction != null && !step.isGranted) {
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = step.onAction,
                modifier = Modifier
                    .fillMaxWidth()
                    .semantics {
                        contentDescription = "${step.actionLabel} for ${step.title}"
                    },
                colors = ButtonDefaults.buttonColors(
                    containerColor = NotiBlue
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(step.actionLabel ?: "Grant Permission")
            }
        }
        
        // Additional manufacturer-specific guidance
        step.additionalInfo?.let { info ->
            Spacer(modifier = Modifier.height(16.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "Additional information",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Device-Specific Instructions",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = info,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}

@Composable
private fun PermissionStatusIndicator(
    isGranted: Boolean,
    isRequired: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
            .semantics {
                contentDescription = when {
                    isGranted -> "Permission granted"
                    isRequired -> "Permission required but not granted"
                    else -> "Permission optional"
                }
            }
    ) {
        Icon(
            imageVector = when {
                isGranted -> Icons.Default.CheckCircle
                isRequired -> Icons.Default.Warning
                else -> Icons.Default.Info
            },
            contentDescription = null,
            tint = when {
                isGranted -> Color(0xFF4CAF50)
                isRequired -> Color(0xFFFF9800)
                else -> Color(0xFF2196F3)
            },
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = when {
                isGranted -> "Permission Granted"
                isRequired -> "Permission Required"
                else -> "Optional (Recommended)"
            },
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = when {
                isGranted -> Color(0xFF4CAF50)
                isRequired -> Color(0xFFFF9800)
                else -> Color(0xFF2196F3)
            }
        )
    }
}

@Composable
private fun WizardNavigation(
    currentStep: Int,
    totalSteps: Int,
    canProceed: Boolean,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Previous button
        if (currentStep > 0) {
            OutlinedButton(
                onClick = onPrevious,
                modifier = Modifier.semantics {
                    contentDescription = "Go to previous step"
                }
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Previous")
            }
        } else {
            Spacer(modifier = Modifier.width(1.dp))
        }
        
        // Skip button (for optional steps)
        if (currentStep < totalSteps - 1 && !canProceed) {
            TextButton(
                onClick = onSkip,
                modifier = Modifier.semantics {
                    contentDescription = "Skip this optional step"
                }
            ) {
                Text("Skip")
            }
        }
        
        // Next/Finish button
        Button(
            onClick = onNext,
            enabled = canProceed || currentStep == totalSteps - 1,
            colors = ButtonDefaults.buttonColors(
                containerColor = NotiBlue
            ),
            modifier = Modifier.semantics {
                contentDescription = if (currentStep == totalSteps - 1) {
                    "Finish setup"
                } else {
                    "Go to next step"
                }
            }
        ) {
            Text(
                if (currentStep == totalSteps - 1) "Finish" else "Next"
            )
            if (currentStep < totalSteps - 1) {
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

private fun canProceedToNext(steps: List<PermissionStep>, currentStep: Int): Boolean {
    val step = steps[currentStep]
    // Can proceed if step is not required or if it's granted
    return !step.isRequired || step.isGranted
}

data class PermissionStep(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val isRequired: Boolean,
    val isGranted: Boolean,
    val actionLabel: String? = null,
    val onAction: (() -> Unit)? = null,
    val additionalInfo: String? = null
)

data class PermissionStates(
    val dnd: Boolean,
    val exactAlarm: Boolean,
    val notificationListener: Boolean
)