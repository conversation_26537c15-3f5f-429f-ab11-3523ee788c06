package com.app.notiminimalist

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.res.painterResource
import android.graphics.drawable.BitmapDrawable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.heading
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.onClick
import androidx.compose.ui.semantics.LiveRegionMode
import androidx.compose.ui.semantics.liveRegion
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.activity.compose.setContent
import com.app.notiminimalist.data.AppInfo
import com.app.notiminimalist.data.SchedulePreferences
import com.app.notiminimalist.data.SilencingMode
import com.app.notiminimalist.service.AlarmScheduler
import com.app.notiminimalist.service.DndManager
import com.app.notiminimalist.ui.theme.NotiBlue
import com.app.notiminimalist.ui.theme.NotiGray
import com.app.notiminimalist.ui.theme.NotiminimalistTheme
import com.app.notiminimalist.utils.AppManager
import com.app.notiminimalist.utils.BatteryOptimizer
import com.app.notiminimalist.utils.PermissionManager
import com.app.notiminimalist.utils.AccessibilityHelper
import com.app.notiminimalist.utils.accessibleClickable
import com.app.notiminimalist.utils.accessibleToggle
import com.app.notiminimalist.utils.AccessibleSpacer
import com.app.notiminimalist.utils.getAccessibleColors
import com.app.notiminimalist.ui.PermissionSetupWizard
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.Calendar

/**
 * Data class to hold permission states for UI updates
 */
data class PermissionStates(
    val dnd: Boolean,
    val exactAlarm: Boolean,
    val notificationListener: Boolean
)

class MainActivity : ComponentActivity() {
    
    private lateinit var permissionManager: PermissionManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Configure status bar to show light content (white icons/text)
        WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = false
        
        // Initialize permission manager
        permissionManager = PermissionManager(this)
        permissionManager.initialize()
        
        setContent {
            MainApp(permissionManager = permissionManager)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainApp(permissionManager: PermissionManager) {
    var selectedTab by remember { mutableIntStateOf(0) }
    val context = LocalContext.current
    
    // Persist theme preference
    val themePrefs = remember { context.getSharedPreferences("theme_prefs", Context.MODE_PRIVATE) }
    var isDarkTheme by remember { 
        mutableStateOf(themePrefs.getBoolean("dark_theme", false))
    }
    
    // Save theme preference when changed
    LaunchedEffect(isDarkTheme) {
        themePrefs.edit().putBoolean("dark_theme", isDarkTheme).apply()
    }
    
    // Configure system UI to match theme dynamically
    LaunchedEffect(isDarkTheme) {
        val activity = context as? MainActivity
        activity?.let {
            WindowCompat.setDecorFitsSystemWindows(it.window, false)
            
            // Dynamic status bar color based on theme
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                it.window.statusBarColor = if (isDarkTheme) {
                    android.graphics.Color.parseColor("#1C1C1E") // Dark surface color
                } else {
                    android.graphics.Color.parseColor("#2B9EDA") // Primary blue
                }
            }
            
            // Configure status bar content (light/dark icons)
            WindowCompat.getInsetsController(it.window, it.window.decorView).isAppearanceLightStatusBars = !isDarkTheme
        }
    }
    
    NotiminimalistTheme(darkTheme = isDarkTheme, dynamicColor = false) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                                    .background(
                                        color = MaterialTheme.colorScheme.primaryContainer, // Use theme-aware color
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .padding(4.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.app_logo),
                                    contentDescription = "App icon",
                                    tint = MaterialTheme.colorScheme.onPrimaryContainer, // Theme-aware tint
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Notiminimalist",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.onPrimary, // Theme-aware text color
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.semantics {
                                    heading()
                                }
                            )
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = { isDarkTheme = !isDarkTheme },
                            modifier = Modifier.semantics {
                                contentDescription = if (isDarkTheme) "Switch to light mode" else "Switch to dark mode"
                            }
                        ) {
                            Icon(
                                imageVector = if (isDarkTheme) Icons.Default.LightMode else Icons.Default.DarkMode,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimary, // Theme-aware icon color
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        scrolledContainerColor = MaterialTheme.colorScheme.primary
                    )
                )
            },
            bottomBar = {
                NavigationBar(
                    containerColor = MaterialTheme.colorScheme.primary,
                    tonalElevation = 8.dp // Restored standard elevation
                    // Removed explicit height modifier - let it use default height (~80dp)
                ) {
                    val tabs = listOf(
                        Triple("Home", Icons.Default.Home, Icons.Outlined.Home),
                        Triple("Apps", Icons.Default.Apps, Icons.Outlined.Apps),
                        Triple("Settings", Icons.Default.Settings, Icons.Outlined.Settings)
                    )
                    
                    tabs.forEachIndexed { index, (title, selectedIcon, unselectedIcon) ->
                        NavigationBarItem(
                            icon = { 
                                Icon(
                                    imageVector = if (selectedTab == index) selectedIcon else unselectedIcon,
                                    contentDescription = "$title tab",
                                    modifier = Modifier.size(24.dp)
                                )
                            },
                            label = { 
                                Text(
                                    text = title,
                                    style = MaterialTheme.typography.labelMedium,
                                    fontWeight = if (selectedTab == index) FontWeight.SemiBold else FontWeight.Normal
                                )
                            },
                            selected = selectedTab == index,
                            onClick = { selectedTab = index },
                            colors = NavigationBarItemDefaults.colors(
                                selectedIconColor = MaterialTheme.colorScheme.onPrimary,
                                selectedTextColor = MaterialTheme.colorScheme.onPrimary,
                                unselectedIconColor = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f),
                                unselectedTextColor = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f),
                                indicatorColor = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.2f)
                            ),
                            modifier = Modifier.semantics {
                                AccessibilityHelper.createNavigationSemantics(
                                    label = title,
                                    selected = selectedTab == index,
                                    index = index,
                                    total = tabs.size
                                ).invoke(this)
                            }
                        )
                    }
                }
            }
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
                    .padding(innerPadding)
            ) {
                when (selectedTab) {
                    0 -> HomeScreen(
                        permissionManager = permissionManager,
                        modifier = Modifier.fillMaxSize()
                    )
                    1 -> ChooseAppsScreen(
                        modifier = Modifier.fillMaxSize()
                    )
                    2 -> SettingsScreen(
                        modifier = Modifier.fillMaxSize(),
                        permissionManager = permissionManager
                    )
                }
            }
        }
    }
}

@Composable
fun HomeScreen(
    permissionManager: PermissionManager,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val schedulePreferences = remember { SchedulePreferences(context) }
    val alarmScheduler = remember { AlarmScheduler(context) }
    val accessibleColors = getAccessibleColors()
    val scope = rememberCoroutineScope()
    
    var isScheduledSilencingEnabled by remember { mutableStateOf(schedulePreferences.isSilencingEnabled) }
    var showPermissionDialog by remember { mutableStateOf(false) }
    var showPermissionWizard by remember { mutableStateOf(false) }
    var permissionDialogMessage by remember { mutableStateOf("") }
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    var startTime by remember { mutableStateOf(schedulePreferences.startTime) }
    var endTime by remember { mutableStateOf(schedulePreferences.endTime) }
    
    // Show setup wizard on first run or if critical permissions are missing
    LaunchedEffect(Unit) {
        if (schedulePreferences.isFirstRun || !permissionManager.hasAllRequiredPermissions()) {
            showPermissionWizard = true
        }
    }
    
    // Date and time picker dialogs
    val datePickerDialog = DatePickerDialog(
        context,
        { _, year, month, dayOfMonth ->
            selectedDate = LocalDate.of(year, month + 1, dayOfMonth)
        },
        selectedDate.year,
        selectedDate.monthValue - 1,
        selectedDate.dayOfMonth
    )
    
    val startTimePickerDialog = TimePickerDialog(
        context,
        { _, hourOfDay, minute ->
            startTime = LocalTime.of(hourOfDay, minute)
            schedulePreferences.startTime = startTime
            if (isScheduledSilencingEnabled) {
                // Use launched effect to handle suspend function
                scope.launch {
                    alarmScheduler.rescheduleAlarms()
                }
            }
        },
        startTime.hour,
        startTime.minute,
        false
    )
    
    val endTimePickerDialog = TimePickerDialog(
        context,
        { _, hourOfDay, minute ->
            endTime = LocalTime.of(hourOfDay, minute)
            schedulePreferences.endTime = endTime
            if (isScheduledSilencingEnabled) {
                // Use launched effect to handle suspend function
                scope.launch {
                    alarmScheduler.rescheduleAlarms()
                }
            }
        },
        endTime.hour,
        endTime.minute,
        false
    )
    
    // Update UI when preferences change
    LaunchedEffect(schedulePreferences.isSilencingEnabled) {
        isScheduledSilencingEnabled = schedulePreferences.isSilencingEnabled
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(horizontal = 20.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp),
        contentPadding = PaddingValues(vertical = 24.dp)
    ) {
        // Hero Section
        item {
            ModernHeroSection(
                isEnabled = isScheduledSilencingEnabled,
                onToggle = {
                    if (permissionManager.hasAllRequiredPermissions()) {
                        isScheduledSilencingEnabled = !isScheduledSilencingEnabled
                        schedulePreferences.isSilencingEnabled = isScheduledSilencingEnabled
                        
                        scope.launch {
                            if (isScheduledSilencingEnabled) {
                                alarmScheduler.scheduleAlarms()
                            } else {
                                alarmScheduler.cancelAlarms()
                            }
                        }
                    } else {
                        permissionDialogMessage = permissionManager.getMissingPermissionsDescription()
                        showPermissionDialog = true
                    }
                }
            )
        }
        
        // Schedule Configuration Card
        item {
            ModernScheduleCard(
                startTime = startTime,
                endTime = endTime,
                onStartTimeClick = { startTimePickerDialog.show() },
                onEndTimeClick = { endTimePickerDialog.show() },
                isEnabled = isScheduledSilencingEnabled
            )
        }
        
        // Quick Stats Card
        item {
            QuickStatsCard(
                isEnabled = isScheduledSilencingEnabled,
                startTime = startTime,
                endTime = endTime
            )
        }
        
        // Permission warning (if needed)
        if (!permissionManager.hasAllRequiredPermissions()) {
            item {
                PermissionWarningCard(
                    missingPermissions = permissionManager.getMissingPermissionsDescription(),
                    onSetupClick = {
                        permissionDialogMessage = permissionManager.getMissingPermissionsDescription()
                        showPermissionDialog = true
                    }
                )
            }
        }
    }
    
    // Permission Dialog
    if (showPermissionDialog) {
        PermissionDialog(
            message = permissionDialogMessage,
            onGrantPermissions = {
                showPermissionDialog = false
                permissionManager.requestAllMissingPermissions()
            },
            onDismiss = {
                showPermissionDialog = false
            }
        )
    }
    
    // Permission Setup Wizard
    if (showPermissionWizard) {
        PermissionSetupWizard(
            permissionManager = permissionManager,
            onComplete = {
                showPermissionWizard = false
                schedulePreferences.isFirstRun = false
            },
            onDismiss = {
                showPermissionWizard = false
                schedulePreferences.isFirstRun = false
            }
        )
    }
}

@Composable
fun ChooseAppsScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val appManager = remember { AppManager(context) }
    val scope = rememberCoroutineScope()
    
    var apps by remember { mutableStateOf<List<AppInfo>>(emptyList()) }
    var filteredApps by remember { mutableStateOf<List<AppInfo>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var searchQuery by remember { mutableStateOf("") }
    var selectAllChecked by remember { mutableStateOf(false) }
    var selectedCount by remember { mutableIntStateOf(0) }
    var isSelectAllLoading by remember { mutableStateOf(false) }
    
    // Load apps on first composition
    LaunchedEffect(Unit) {
        scope.launch {
            isLoading = true
            apps = appManager.getInstalledApps()
            filteredApps = apps
            // Count silenced apps for the selection count
            selectedCount = apps.count { it.silencingMode == SilencingMode.SILENCED }
            selectAllChecked = selectedCount == apps.size
            isLoading = false
        }
    }
    
    // Filter apps based on search query
    LaunchedEffect(searchQuery, apps) {
        filteredApps = if (searchQuery.isBlank()) {
            apps
        } else {
            apps.filter { 
                it.appName.contains(searchQuery, ignoreCase = true) ||
                it.packageName.contains(searchQuery, ignoreCase = true)
            }
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentPadding = PaddingValues(horizontal = 20.dp, vertical = 24.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Header
        item {
            ModernAppsHeader()
        }
        
        // Search Section
        item {
            ModernSearchSection(
                searchQuery = searchQuery,
                onSearchChange = { searchQuery = it }
            )
        }
        
        // Selection Control Section
        item {
            ModernSelectionControl(
                selectedCount = selectedCount,
                totalCount = apps.size,
                selectAllChecked = selectAllChecked,
                isLoading = isSelectAllLoading,
                onSelectAllChange = { checked ->
                    scope.launch {
                        isSelectAllLoading = true
                        val targetMode = if (checked) SilencingMode.SILENCED else SilencingMode.ALWAYS_ALLOWED
                        
                        // Use batch operation for better performance
                        val updatedApps = appManager.setAllAppsSilencingMode(targetMode)
                        
                        // Update UI state in a single operation
                        selectAllChecked = checked
                        selectedCount = if (checked) updatedApps.size else 0
                        apps = updatedApps
                        filteredApps = if (searchQuery.isBlank()) {
                            updatedApps
                        } else {
                            updatedApps.filter { 
                                it.appName.contains(searchQuery, ignoreCase = true) ||
                                it.packageName.contains(searchQuery, ignoreCase = true)
                            }
                        }
                        isSelectAllLoading = false
                    }
                }
            )
        }
        
        // Apps List
        if (isLoading) {
            item {
                ModernLoadingState()
            }
        } else if (filteredApps.isEmpty()) {
            item {
                ModernEmptyState(
                    searchQuery = searchQuery
                )
            }
        } else {
            items(
                items = filteredApps,
                key = { app -> app.packageName } // Stable key for better performance
            ) { app ->
                ModernAppItem(
                    app = app,
                    onToggle = { _ -> 
                        // Legacy toggle functionality removed
                        // Apps are now controlled only via the silencing mode toggle
                    },
                    onSilencingModeChange = { packageName, newMode ->
                        scope.launch {
                            appManager.setAppSilencingMode(packageName, newMode)
                            
                            // Efficiently update only the changed app and counts
                            val updatedApps = apps.map { app ->
                                if (app.packageName == packageName) {
                                    app.copy(
                                        silencingMode = newMode,
                                        isSelected = newMode == SilencingMode.SILENCED
                                    )
                                } else app
                            }
                            
                            // Update counts and state efficiently
                            selectedCount = updatedApps.count { it.silencingMode == SilencingMode.SILENCED }
                            selectAllChecked = selectedCount == updatedApps.size
                            apps = updatedApps
                            
                            // Update filtered apps only if search is active
                            if (searchQuery.isNotBlank()) {
                                filteredApps = updatedApps.filter { 
                                    it.appName.contains(searchQuery, ignoreCase = true) ||
                                    it.packageName.contains(searchQuery, ignoreCase = true)
                                }
                            } else {
                                filteredApps = updatedApps
                            }
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun ModernAppsHeader() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Apps,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(32.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Choose Apps",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.semantics {
                heading()
            }
        )
        
        Text(
            text = "Use the toggle to set each app as silenced or always allowed",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 32.dp)
        )
    }
}

@Composable
fun ModernSearchSection(
    searchQuery: String,
    onSearchChange: (String) -> Unit
) {
    OutlinedTextField(
        value = searchQuery,
        onValueChange = onSearchChange,
        placeholder = { 
            Text(
                text = "Search apps...",
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Search apps",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        trailingIcon = if (searchQuery.isNotEmpty()) {
            {
                IconButton(
                    onClick = { onSearchChange("") }
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear search",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else null,
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        singleLine = true,
        colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
            focusedBorderColor = MaterialTheme.colorScheme.primary,
            unfocusedBorderColor = MaterialTheme.colorScheme.outline,
            focusedContainerColor = MaterialTheme.colorScheme.surface,
            unfocusedContainerColor = MaterialTheme.colorScheme.surface
        )
    )
}

@Composable
fun ModernSelectionControl(
    selectedCount: Int,
    totalCount: Int,
    selectAllChecked: Boolean,
    isLoading: Boolean = false,
    onSelectAllChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Select All Apps",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = FontWeight.SemiBold
                )
                
                Text(
                    text = "$selectedCount of $totalCount apps selected",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
            
            // Checkbox for Select All with loading state
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp,
                    color = MaterialTheme.colorScheme.primary
                )
            } else {
                Checkbox(
                    checked = selectAllChecked,
                    onCheckedChange = onSelectAllChange,
                    colors = CheckboxDefaults.colors(
                        checkedColor = MaterialTheme.colorScheme.primary,
                        uncheckedColor = MaterialTheme.colorScheme.outline,
                        checkmarkColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    modifier = Modifier.semantics {
                        contentDescription = if (selectAllChecked) {
                            "Uncheck to set all apps to always allowed"
                        } else {
                            "Check to silence all apps"
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun ModernLoadingState() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    strokeWidth = 3.dp
                )
                
                Text(
                    text = "Loading apps...",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun ModernEmptyState(
    searchQuery: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = if (searchQuery.isBlank()) {
                        Icons.Default.Apps
                    } else {
                        Icons.Default.SearchOff
                    },
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f),
                    modifier = Modifier.size(48.dp)
                )
                
                Text(
                    text = if (searchQuery.isBlank()) {
                        "No apps found"
                    } else {
                        "No apps match your search"
                    },
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = FontWeight.Medium
                )
                
                if (searchQuery.isNotBlank()) {
                    Text(
                        text = "Try a different search term",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
fun ModernAppItem(
    app: AppInfo,
    onToggle: (String) -> Unit,
    onSilencingModeChange: (String, SilencingMode) -> Unit = { _, _ -> }
) {
    val isSelected = app.silencingMode == SilencingMode.SILENCED
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle(app.packageName) },
        colors = CardDefaults.cardColors(
            containerColor = when (app.silencingMode) {
                SilencingMode.SILENCED -> 
                    MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                SilencingMode.ALWAYS_ALLOWED -> 
                    MaterialTheme.colorScheme.surface // Neutral appearance
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (app.silencingMode == SilencingMode.SILENCED) 3.dp else 1.dp
        ),
        shape = RoundedCornerShape(16.dp),
        border = if (app.silencingMode == SilencingMode.SILENCED) {
            androidx.compose.foundation.BorderStroke(
                width = 1.5.dp,
                color = MaterialTheme.colorScheme.error.copy(alpha = 0.5f)
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // App icon with selection indicator
            Box {
                app.icon?.let { drawable ->
                    ModernAppIcon(
                        drawable = drawable,
                        fallbackText = app.appName,
                        modifier = Modifier
                            .size(48.dp)
                            .clip(RoundedCornerShape(12.dp))
                    )
                } ?: run {
                    ModernAppIconFallback(app.appName)
                }
                
                // Selection indicator - only show for silenced apps
                if (app.silencingMode == SilencingMode.SILENCED) {
                    Surface(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .offset(x = 6.dp, y = (-6).dp)
                            .size(20.dp),
                        shape = CircleShape,
                        color = MaterialTheme.colorScheme.error
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            Icon(
                                imageVector = Icons.Default.NotificationsOff,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onError,
                                modifier = Modifier.size(12.dp)
                            )
                        }
                    }
                }
            }
            
            // App info
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = app.getDisplayName(),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Text(
                    text = app.packageName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // Status indicator - show for both silenced and always allowed apps
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    Surface(
                        shape = RoundedCornerShape(8.dp),
                        color = when (app.silencingMode) {
                            SilencingMode.SILENCED -> MaterialTheme.colorScheme.errorContainer
                            SilencingMode.ALWAYS_ALLOWED -> MaterialTheme.colorScheme.primaryContainer
                        },
                        modifier = Modifier.padding(0.dp)
                    ) {
                        Text(
                            text = app.getSilencingModeDescription(),
                            style = MaterialTheme.typography.labelMedium,
                            color = when (app.silencingMode) {
                                SilencingMode.SILENCED -> MaterialTheme.colorScheme.onErrorContainer
                                SilencingMode.ALWAYS_ALLOWED -> MaterialTheme.colorScheme.onPrimaryContainer
                            },
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
            
            // Mode selector toggle
            ModernSilencingToggle(
                currentMode = app.silencingMode,
                onModeChange = { newMode ->
                    onSilencingModeChange(app.packageName, newMode)
                }
            )
        }
    }
}

@Composable
fun ModernAppIcon(
    drawable: android.graphics.drawable.Drawable,
    fallbackText: String,
    modifier: Modifier = Modifier
) {
    when (drawable) {
        is BitmapDrawable -> {
            // Handle bitmap drawables (most common)
            androidx.compose.foundation.Image(
                bitmap = drawable.bitmap.asImageBitmap(),
                contentDescription = null,
                modifier = modifier
            )
        }
        else -> {
            // Handle all other drawable types using AndroidView
            AndroidView(
                factory = { context ->
                    android.widget.ImageView(context).apply {
                        setImageDrawable(drawable)
                        scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
                    }
                },
                modifier = modifier
            )
        }
    }
}

@Composable
fun ModernAppIconFallback(appName: String) {
    Surface(
        modifier = Modifier.size(48.dp),
        shape = RoundedCornerShape(12.dp),
        color = MaterialTheme.colorScheme.primaryContainer
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = appName.take(1).uppercase(),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

@Composable
fun ModernSilencingToggle(
    currentMode: SilencingMode,
    onModeChange: (SilencingMode) -> Unit
) {
    val isSilenced = currentMode == SilencingMode.SILENCED
    
    Surface(
        modifier = Modifier
            .clickable { 
                onModeChange(
                    if (isSilenced) SilencingMode.ALWAYS_ALLOWED 
                    else SilencingMode.SILENCED
                )
            }
            .semantics {
                AccessibilityHelper.createToggleSemantics(
                    label = "App silencing toggle",
                    checked = isSilenced,
                    onToggle = {
                        onModeChange(
                            if (isSilenced) SilencingMode.ALWAYS_ALLOWED 
                            else SilencingMode.SILENCED
                        )
                    }
                ).invoke(this)
            }
            .width(56.dp)
            .height(32.dp),
        shape = RoundedCornerShape(16.dp),
        color = when (currentMode) {
            SilencingMode.SILENCED -> MaterialTheme.colorScheme.errorContainer
            SilencingMode.ALWAYS_ALLOWED -> MaterialTheme.colorScheme.primaryContainer
        },
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = when (currentMode) {
                SilencingMode.SILENCED -> MaterialTheme.colorScheme.error
                SilencingMode.ALWAYS_ALLOWED -> MaterialTheme.colorScheme.primary
            }
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Toggle indicator (circle)
            Surface(
                modifier = Modifier
                    .size(24.dp)
                    .align(
                        if (isSilenced) Alignment.CenterStart 
                        else Alignment.CenterEnd
                    )
                    .offset(
                        x = if (isSilenced) 4.dp else (-4).dp
                    ),
                shape = CircleShape,
                color = when (currentMode) {
                    SilencingMode.SILENCED -> MaterialTheme.colorScheme.error
                    SilencingMode.ALWAYS_ALLOWED -> MaterialTheme.colorScheme.primary
                }
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    Icon(
                        imageVector = when (currentMode) {
                            SilencingMode.SILENCED -> Icons.Default.NotificationsOff
                            SilencingMode.ALWAYS_ALLOWED -> Icons.Default.Notifications
                        },
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier,
    permissionManager: PermissionManager
) {
    val context = LocalContext.current
    val schedulePreferences = remember { SchedulePreferences(context) }
    val appManager = remember { AppManager(context) }
    val scope = rememberCoroutineScope()
    
    var silencedAppCount by remember { mutableIntStateOf(0) }
    var selectionDescription by remember { mutableStateOf("") }
    var showPermissionDialog by remember { mutableStateOf(false) }
    var permissionDialogMessage by remember { mutableStateOf("") }
    
    // Permission states for real-time updates
    var permissionStates by remember {
        mutableStateOf(
            PermissionStates(
                dnd = permissionManager.hasDndPermission(),
                exactAlarm = permissionManager.hasExactAlarmPermission(),
                notificationListener = permissionManager.hasNotificationListenerPermission()
            )
        )
    }
    
    // Update permission states periodically
    LaunchedEffect(Unit) {
        while (true) {
            kotlinx.coroutines.delay(2000) // Check every 2 seconds
            permissionStates = PermissionStates(
                dnd = permissionManager.hasDndPermission(),
                exactAlarm = permissionManager.hasExactAlarmPermission(),
                notificationListener = permissionManager.hasNotificationListenerPermission()
            )
        }
    }
    
    // Load initial data
    LaunchedEffect(Unit) {
        scope.launch {
            silencedAppCount = appManager.getAppsCountByMode(SilencingMode.SILENCED)
            selectionDescription = appManager.getEnhancedSelectionDescription()
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentPadding = PaddingValues(horizontal = 20.dp, vertical = 24.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // Header
        item {
            ModernSettingsHeader()
        }
        
        // Schedule Overview Section
        item {
            ModernSettingsSection(
                title = "Schedule",
                icon = Icons.Default.Schedule
            ) {
                ModernSettingsItem(
                    title = "Current Schedule",
                    subtitle = schedulePreferences.getFormattedSchedule(),
                    icon = Icons.Default.AccessTime,
                    onClick = { /* Time settings handled in Home tab */ },
                    showArrow = false
                )
                
                ModernSettingsItem(
                    title = "Status",
                    subtitle = if (schedulePreferences.isSilencingEnabled) "Active" else "Inactive",
                    icon = if (schedulePreferences.isSilencingEnabled) Icons.Default.NotificationsOff else Icons.Default.Notifications,
                    onClick = { /* Toggle handled in Home tab */ },
                    showArrow = false,
                    statusColor = if (schedulePreferences.isSilencingEnabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
                )
            }
        }
        
        // App Management Section
        item {
            ModernSettingsSection(
                title = "Apps",
                icon = Icons.Default.Apps
            ) {
                ModernSettingsItem(
                    title = "Selected Apps",
                    subtitle = "$silencedAppCount apps will be silenced",
                    icon = Icons.Default.CheckCircle,
                    onClick = { /* Handled by Apps tab navigation */ },
                    showArrow = false
                )
            }
        }
        
        // Permissions Section
        item {
            ModernSettingsSection(
                title = "Permissions",
                icon = Icons.Default.Security
            ) {
                ModernSettingsItem(
                    title = "Do Not Disturb Access",
                    subtitle = if (permissionStates.dnd) "Granted" else "Required for automatic silencing",
                    icon = Icons.Default.DoNotDisturb,
                    onClick = {
                        if (!permissionStates.dnd) {
                            permissionManager.requestDndPermission()
                        }
                    },
                    statusColor = if (permissionStates.dnd) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                    showArrow = !permissionStates.dnd
                )
                
                ModernSettingsItem(
                    title = "Exact Alarm Scheduling",
                    subtitle = if (permissionStates.exactAlarm) "Granted" else "Required for precise timing",
                    icon = Icons.Default.Schedule,
                    onClick = {
                        if (!permissionStates.exactAlarm) {
                            permissionManager.requestExactAlarmPermission()
                        }
                    },
                    statusColor = if (permissionStates.exactAlarm) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                    showArrow = !permissionStates.exactAlarm
                )
                
                ModernSettingsItem(
                    title = "Notification Listener",
                    subtitle = if (permissionStates.notificationListener) "Granted" else "Required for notification management",
                    icon = Icons.Default.Notifications,
                    onClick = {
                        if (!permissionStates.notificationListener) {
                            permissionManager.requestNotificationListenerPermission()
                        }
                    },
                    statusColor = if (permissionStates.notificationListener) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                    showArrow = !permissionStates.notificationListener
                )
                
                // Overall status
                val allPermissionsGranted = permissionStates.dnd && permissionStates.exactAlarm && permissionStates.notificationListener
                ModernSettingsItem(
                    title = "Grant All Missing Permissions",
                    subtitle = if (allPermissionsGranted) "All permissions granted" else "Grant all required permissions at once",
                    icon = if (allPermissionsGranted) Icons.Default.CheckCircle else Icons.Default.Warning,
                    onClick = {
                        if (!allPermissionsGranted) {
                            permissionDialogMessage = permissionManager.getMissingPermissionsDescription()
                            showPermissionDialog = true
                        }
                    },
                    statusColor = if (allPermissionsGranted) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                    showArrow = !allPermissionsGranted
                )
            }
        }

        // About Section
        item {
            ModernSettingsSection(
                title = "About",
                icon = Icons.Default.Info
            ) {
                ModernSettingsItem(
                    title = "Notiminimalist",
                    subtitle = "Version 1.0.0",
                    icon = Icons.Default.PhoneAndroid,
                    onClick = { },
                    showArrow = false
                )
                
                ModernSettingsItem(
                    title = "Privacy",
                    subtitle = "No data collection • Completely offline",
                    icon = Icons.Default.Shield,
                    onClick = { },
                    showArrow = false
                )
            }
        }
    }
    
    // Permission Dialog
    if (showPermissionDialog) {
        PermissionDialog(
            message = permissionDialogMessage,
            onGrantPermissions = {
                showPermissionDialog = false
                permissionManager.requestAllMissingPermissions()
            },
            onDismiss = {
                showPermissionDialog = false
            }
        )
    }
}

@Composable
fun ModernSettingsHeader() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Settings,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(32.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Settings",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.semantics {
                heading()
            }
        )
        
        Text(
            text = "Customize your notification experience",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 32.dp)
        )
    }
}

@Composable
fun ModernSettingsSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Section Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            // Content
            content()
        }
    }
}

@Composable
fun ModernSettingsItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit,
    showArrow: Boolean = true,
    isDestructive: Boolean = false,
    statusColor: Color? = null
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isDestructive) {
                MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Icon
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = when {
                    isDestructive -> MaterialTheme.colorScheme.error
                    statusColor != null -> statusColor
                    else -> MaterialTheme.colorScheme.primary
                },
                modifier = Modifier.size(24.dp)
            )
            
            // Content
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (isDestructive) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isDestructive) {
                        MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // Arrow (optional)
            if (showArrow) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    NotiminimalistTheme(dynamicColor = false) {
        Surface {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White)
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Notification Silencing",
                    style = MaterialTheme.typography.headlineLarge,
                    color = NotiBlue,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(32.dp))
                Text(
                    text = "Inactive",
                    color = Color.Gray
                )
                Spacer(modifier = Modifier.height(24.dp))
                Box(
                    modifier = Modifier
                        .size(160.dp)
                        .clip(CircleShape)
                        .background(NotiGray),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "OFF",
                        style = MaterialTheme.typography.headlineMedium,
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

// Modern UI Components

@Composable
fun ModernHeroSection(
    isEnabled: Boolean,
    onToggle: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(20.dp)
        ) {
            // Status Icon and Text
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = if (isEnabled) Icons.Default.NotificationsOff else Icons.Default.Notifications,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(48.dp)
                )
                
                Text(
                    text = "Notification Silencing",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Text(
                    text = if (isEnabled) "Currently Active" else "Currently Inactive",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f),
                    textAlign = TextAlign.Center
                )
            }
            
            // Modern Toggle Button
            ModernToggleButton(
                isEnabled = isEnabled,
                onToggle = onToggle
            )
        }
    }
}

@Composable
fun ModernToggleButton(
    isEnabled: Boolean,
    onToggle: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(120.dp) // Fixed size - no animation
            .clip(CircleShape)
            .background(
                brush = if (isEnabled) {
                    androidx.compose.ui.graphics.Brush.radialGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primary, // Using your existing blue color
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
                        ),
                        radius = 200f
                    )
                } else {
                    androidx.compose.ui.graphics.Brush.radialGradient(
                        colors = listOf(
                            Color(0xFF9E9E9E), // Neutral gray
                            Color(0xFF616161)  // Darker gray
                        ),
                        radius = 200f
                    )
                }
            )
            .clickable { onToggle() }
            .accessibleToggle(
                label = "Scheduled notification silencing",
                checked = isEnabled,
                onToggle = onToggle
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            // Enhanced icon with animation
            val animatedIconSize by animateDpAsState(
                targetValue = if (isEnabled) 36.dp else 32.dp,
                animationSpec = tween(300, easing = EaseInOutCubic),
                label = "icon_size_animation"
            )
            
            Icon(
                imageVector = if (isEnabled) Icons.Default.NotificationsOff else Icons.Default.Notifications,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(animatedIconSize)
            )
            
            // Enhanced text with better styling
            Text(
                text = if (isEnabled) "ON" else "OFF",
                style = MaterialTheme.typography.labelLarge,
                color = Color.White,
                fontWeight = FontWeight.ExtraBold,
                letterSpacing = 1.sp
            )
        }
        
        // Subtle ripple effect overlay when enabled
        if (isEnabled) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.1f),
                                Color.Transparent
                            ),
                            radius = 150f
                        ),
                        shape = CircleShape
                    )
            )
        }
    }
}

@Composable
fun ModernScheduleCard(
    startTime: LocalTime,
    endTime: LocalTime,
    onStartTimeClick: () -> Unit,
    onEndTimeClick: () -> Unit,
    isEnabled: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = "Schedule Duration",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            // Time Selection Row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Start Time Field
                TimeSelectionCard(
                    modifier = Modifier.weight(1f),
                    label = "Start Time",
                    time = startTime,
                    icon = Icons.Default.AccessTime, // Clock icon instead of play arrow
                    onClick = onStartTimeClick,
                    enabled = true // Always allow time selection
                )
                
                // End Time Field
                TimeSelectionCard(
                    modifier = Modifier.weight(1f),
                    label = "End Time",
                    time = endTime,
                    icon = Icons.Default.AccessTime, // Clock icon instead of stop
                    onClick = onEndTimeClick,
                    enabled = true // Always allow time selection
                )
            }
        }
    }
}

@Composable
fun TimeSelectionCard(
    modifier: Modifier = Modifier,
    label: String,
    time: LocalTime,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit,
    enabled: Boolean
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Label
        Text(
            text = label,
            style = MaterialTheme.typography.labelLarge, // Increased from labelMedium
            color = if (enabled) {
                MaterialTheme.colorScheme.primary // Theme-aware primary color
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
            },
            fontWeight = FontWeight.Medium
        )
        
        // Clickable wrapper for the text field
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = enabled) { onClick() }
        ) {
            // Text Field with Clock Icon (non-clickable, display only)
            OutlinedTextField(
                value = time.format(DateTimeFormatter.ofPattern("h:mm a")),
                onValueChange = { /* Read-only field */ },
                readOnly = true,
                enabled = false, // Disable to prevent internal click handling
                singleLine = true, // Ensure single line to prevent height changes
                textStyle = MaterialTheme.typography.bodyMedium, // Slightly smaller text size
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.AccessTime,
                        contentDescription = null,
                        tint = if (enabled) {
                            MaterialTheme.colorScheme.onSurfaceVariant // Theme-aware icon color
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                        },
                        modifier = Modifier.size(20.dp) // Slightly smaller icon to save space
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledContainerColor = if (enabled) {
                        MaterialTheme.colorScheme.surfaceVariant // Theme-aware background
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    },
                    disabledBorderColor = if (enabled) {
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.5f) // Theme-aware border
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                    },
                    disabledTextColor = if (enabled) {
                        MaterialTheme.colorScheme.onSurface // Theme-aware text
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    }
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp) // Fixed height to prevent resizing
                    .padding(horizontal = 0.dp) // Remove horizontal padding for more space
                    .semantics {
                        contentDescription = "$label: ${time.format(DateTimeFormatter.ofPattern("h:mm a"))}"
                        if (enabled) {
                            role = Role.Button
                        }
                    }
            )
        }
    }
}

@Composable
fun QuickStatsCard(
    isEnabled: Boolean,
    startTime: LocalTime,
    endTime: LocalTime
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = "Schedule Overview",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                QuickStatItem(
                    label = "Status",
                    value = if (isEnabled) "Active" else "Inactive",
                    icon = if (isEnabled) Icons.Default.CheckCircle else Icons.Default.Cancel
                )
                
                QuickStatItem(
                    label = "Duration",
                    value = calculateDuration(startTime, endTime),
                    icon = Icons.Default.Timer
                )
            }
        }
    }
}

@Composable
fun QuickStatItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onPrimaryContainer,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun PermissionWarningCard(
    missingPermissions: String,
    onSetupClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.size(24.dp)
                )
                
                Text(
                    text = "Setup Required",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            Text(
                text = missingPermissions,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            
            OutlinedButton(
                onClick = onSetupClick,
                colors = androidx.compose.material3.ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.onErrorContainer
                ),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "Complete Setup",
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

private fun calculateDuration(startTime: LocalTime, endTime: LocalTime): String {
    val duration = if (endTime.isBefore(startTime)) {
        // Overnight duration
        val hoursUntilMidnight = 24 - startTime.hour
        val hoursAfterMidnight = endTime.hour
        val totalHours = hoursUntilMidnight + hoursAfterMidnight
        val totalMinutes = totalHours * 60 - startTime.minute + endTime.minute
        totalMinutes
    } else {
        // Same day duration
        val hours = endTime.hour - startTime.hour
        val minutes = endTime.minute - startTime.minute
        hours * 60 + minutes
    }
    
    val hours = duration / 60
    val minutes = duration % 60
    
    return when {
        hours == 0 -> "${minutes}m"
        minutes == 0 -> "${hours}h"
        else -> "${hours}h ${minutes}m"
    }
}

@Composable
fun PermissionDialog(
    message: String,
    onGrantPermissions: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Permissions Required",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium
            )
        },
        confirmButton = {
            TextButton(onClick = onGrantPermissions) {
                Text("Grant Permissions")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Later")
            }
        }
    )
}