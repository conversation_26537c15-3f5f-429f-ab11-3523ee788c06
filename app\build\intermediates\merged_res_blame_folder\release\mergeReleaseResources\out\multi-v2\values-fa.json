{"logs": [{"outputFile": "com.app.notiminimalist.app-mergeReleaseResources-41:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\18b22e515b4201f34167a2d64b698d76\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,983,1065,1139,1214,1288,1360,1435,1502", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,978,1060,1134,1209,1283,1355,1430,1497,1614"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "829,916,995,1089,1187,1273,1355,7617,7702,7785,7866,7948,8022,8097,8171,8344,8419,8486", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "911,990,1084,1182,1268,1350,1453,7697,7780,7861,7943,8017,8092,8166,8238,8414,8481,8598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a87ea0a2ae3b302fa8b2e67fc66b56aa\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,88", "endOffsets": "139,228"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8603,8692", "endColumns": "88,88", "endOffsets": "8687,8776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac5730e1294228dab72f8d7b7acde09d\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1458,1573,1683,1797,1915,2011,2107,2221,2360,2476,2611,2696,2799,2891,2988,3102,3225,3333,3466,3597,3719,3884,4006,4119,4235,4352,4445,4543,4664,4796,4903,5006,5111,5242,5378,5484,5594,5674,5767,5864,5985,6071,6155,6254,6336,6420,6521,6622,6719,6819,6906,7010,7110,7213,7333,7415,7519", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "1568,1678,1792,1910,2006,2102,2216,2355,2471,2606,2691,2794,2886,2983,3097,3220,3328,3461,3592,3714,3879,4001,4114,4230,4347,4440,4538,4659,4791,4898,5001,5106,5237,5373,5479,5589,5669,5762,5859,5980,6066,6150,6249,6331,6415,6516,6617,6714,6814,6901,7005,7105,7208,7328,7410,7514,7612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\38ea0471cd817173b4203329613a319d\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,405,505,606,712,8243", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "199,301,400,500,601,707,824,8339"}}]}]}