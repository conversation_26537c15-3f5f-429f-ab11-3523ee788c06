package com.app.notiminimalist.ui.theme

import androidx.compose.ui.graphics.Color

// Modern Material 3 inspired color palette
// Primary colors - Professional dark mode adaptations
val Primary = Color(0xFF2B9EDA) // Custom blue matching navigation bars
val PrimaryLight = Color(0xFF5EC5F3) // Light blue variant
val PrimaryDark = Color(0xFF1976D2) // Darker blue variant
val PrimaryDarkMode = Color(0xFF4FC3F7) // Desaturated blue for dark mode
val PrimaryContainer = Color(0xFFE3F2FD) // Very light blue
val PrimaryContainerDark = Color(0xFF1A365D) // Dark container with proper contrast
val OnPrimary = Color(0xFFFFFFFF)
val OnPrimaryContainer = Color(0xFF001D36)
val OnPrimaryContainerDark = Color(0xFFB3E5FC) // Light text on dark primary container

// Secondary colors - Subtle gray palette
val Secondary = Color(0xFF546E7A) // Blue gray
val SecondaryLight = Color(0xFF819CA9) // Light blue gray
val SecondaryDark = Color(0xFF29434E) // Dark blue gray
val SecondaryContainer = Color(0xFFECEFF1) // Very light gray
val OnSecondary = Color(0xFFFFFFFF)
val OnSecondaryContainer = Color(0xFF101518)

// Background and surface colors - Professional dark mode with proper elevation
val Background = Color(0xFFFCFCFC) // Pure white with subtle warmth
val BackgroundDark = Color(0xFF0F0F0F) // True dark background for better contrast
val Surface = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1C1C1E) // Elevated surface (iOS-inspired)
val SurfaceVariant = Color(0xFFF5F5F5)
val SurfaceVariantDark = Color(0xFF2C2C2E) // Higher elevation surface
// Professional elevation-based surface colors for dark mode
val SurfaceContainerLow = Color(0xFF121214) // Lower elevation surface
val SurfaceContainerHigh = Color(0xFF36363A) // Higher elevation surface
val SurfaceContainer = Color(0xFF1E1E20) // Mid-level surface

// Additional semantic colors for better dark mode
val Scrim = Color(0x80000000) // Semi-transparent overlay
val OutlineVariantDark = Color(0xFF494449) // Improved outline contrast

// Semantic colors
val Success = Color(0xFF2E7D32) // Green
val Warning = Color(0xFFF57C00) // Orange
val Error = Color(0xFFD32F2F) // Red
val Info = Color(0xFF1976D2) // Blue

// Text colors with WCAG AA compliant contrast ratios
val OnBackground = Color(0xFF1A1C1E)
val OnBackgroundDark = Color(0xFFE6E1E5) // High contrast for dark mode
val OnSurface = Color(0xFF1A1C1E)
val OnSurfaceDark = Color(0xFFE6E1E5) // High contrast primary text
val OnSurfaceVariant = Color(0xFF42474E)
val OnSurfaceVariantDark = Color(0xFFCAC4CF) // Secondary text with proper contrast
val OnSurfaceMuted = Color(0xFF939094) // Tertiary text for dark mode

// Legacy colors - updated for consistency with custom blue
val NotiBlue = Primary // Custom blue #2B9EDA
val NotiBlueLight = PrimaryLight
val NotiBlueDark = PrimaryDark
val NotiGray = SurfaceVariant
val NotiGrayDark = SecondaryDark
val NotiWhite = Surface
val NotiBlack = OnBackground

// Deprecated - for backward compatibility only
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)
val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)