<variant
    name="debug"
    package="com.app.notiminimalist"
    minSdkVersion="23"
    targetSdkVersion="36.0"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.12.2"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\ebd951eaa4d6e555d9addcb1664d9caf\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.13\transforms\e0c4142be62302ba695a11aadea72308\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\processDebugResources\R.jar"
      type="MAIN"
      applicationId="com.app.notiminimalist"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out"
      generatedResourceFolders="build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\ebd951eaa4d6e555d9addcb1664d9caf\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.13\transforms\e0c4142be62302ba695a11aadea72308\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
