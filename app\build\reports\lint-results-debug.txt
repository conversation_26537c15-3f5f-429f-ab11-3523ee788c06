C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:218: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val minute = String.format("%02d", time.minute)
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:173: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS [InlinedApi]
            val policyCategories = NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:174: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM [InlinedApi]
                                 NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:187: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:188: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:225: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:226: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:232: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:233: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:238: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_ALARMS [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_ALARMS or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:239: Warning: Field requires API level 28 (current min is 23): android.app.NotificationManager.Policy#PRIORITY_CATEGORY_SYSTEM [InlinedApi]
                    NotificationManager.Policy.PRIORITY_CATEGORY_SYSTEM or
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:256: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_AMBIENT [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_AMBIENT or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:257: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_BADGE [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_BADGE or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:258: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_FULL_SCREEN_INTENT [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_FULL_SCREEN_INTENT or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:259: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_LIGHTS [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_LIGHTS or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:260: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_NOTIFICATION_LIST [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_NOTIFICATION_LIST or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:261: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_PEEK [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_PEEK or
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:264: Warning: Field requires API level 28 (current min is 26): android.app.NotificationManager.Policy#SUPPRESSED_EFFECT_STATUS_BAR [InlinedApi]
                NotificationManager.Policy.SUPPRESSED_EFFECT_STATUS_BAR
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InlinedApi":
   This check scans through all the Android API field references in the
   application and flags certain constants, such as static final integers and
   Strings, which were introduced in later versions. These will actually be
   copied into the class files rather than being referenced, which means that
   the value is available even when running on older devices. In some cases
   that's fine, and in other cases it can result in a runtime crash or
   incorrect behavior. It depends on the context, so consider the code
   carefully and decide whether it's safe and can be suppressed or whether the
   code needs to be guarded.

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:40: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BatteryLife":
   This issue flags code that either
   * negatively affects battery life, or
   * uses APIs that have recently changed behavior to prevent background tasks
   from consuming memory and battery excessively.

   Generally, you should be using WorkManager instead.

   For more details on how to update your code, please see
   https://developer.android.com/topic/performance/background-optimization


C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\AppManager.kt:37: Warning: As of Android 11, this method no longer returns information about all apps; see https://g.co/dev/packagevisibility for details [QueryPermissionsNeeded]
            val installedPackages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "QueryPermissionsNeeded":
   Apps that target Android 11 cannot query or interact with other installed
   apps by default. If you need to query or interact with other installed
   apps, you may need to add a <queries> declaration in your manifest.

   As a corollary, the methods PackageManager#getInstalledPackages and
   PackageManager#getInstalledApplications will no longer return information
   about all installed apps. To query specific apps or types of apps, you can
   use methods like PackageManager#getPackageInfo or
   PackageManager#queryIntentActivities.

   https://g.co/dev/packagevisibility

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\AndroidManifest.xml:22: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.12.2 is available: 8.13.0 [AndroidGradlePluginVersion]
agp = "8.12.2"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts:56: Warning: A newer version of androidx.compose.material:material-icons-extended than 1.5.4 is available: 1.7.8 [GradleDependency]
    implementation("androidx.compose.material:material-icons-extended:1.5.4")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts:59: Warning: A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5 [GradleDependency]
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.3 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:10: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.08.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:96: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:119: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:139: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:158: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:186: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:194: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT is never true here [ObsoleteSdkInt]
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\AlarmScheduler.kt:243: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:28: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:39: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:53: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !isIgnoringBatteryOptimizations()) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:75: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:139: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:46: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:74: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.M is always true here (SDK_INT ≥ 23 and < 26) [ObsoleteSdkInt]
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:113: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.M is always true here (SDK_INT ≥ 23 and < 26) [ObsoleteSdkInt]
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:167: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:217: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:371: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.M is always true here (SDK_INT ≥ 23 and < 26) [ObsoleteSdkInt]
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> (silencedApps.isNotEmpty() || alwaysAllowedApps.isNotEmpty())
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:396: Warning: Unnecessary; Build.VERSION.SDK_INT >= Build.VERSION_CODES.M is always true here (SDK_INT ≥ 23 and < 26) [ObsoleteSdkInt]
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:176: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\NotificationListenerService.kt:136: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\NotificationListenerService.kt:162: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt:63: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt:107: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.white appears to be unused [UnusedResources]
    <color name="white">#FFFFFFFF</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\drawable-v24\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\res\drawable\app_logo.png: Warning: Found bitmap drawable res/drawable/app_logo.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:41: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("package:${context.packageName}")
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\BatteryOptimizer.kt:41: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("package:${context.packageName}")
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:419: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:437: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:482: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
            preferences.edit().putInt(PREFS_ORIGINAL_NOTIFICATION_VOLUME, originalVolume).apply()
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\service\DndManager.kt:504: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                preferences.edit().remove(PREFS_ORIGINAL_NOTIFICATION_VOLUME).apply()
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:166: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        themePrefs.edit().putBoolean("dark_theme", isDarkTheme).apply()
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:178: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                    android.graphics.Color.parseColor("#1C1C1E") // Dark surface color
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:178: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                    android.graphics.Color.parseColor("#1C1C1E") // Dark surface color
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:180: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                    android.graphics.Color.parseColor("#2B9EDA") // Primary blue
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\MainActivity.kt:180: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                    android.graphics.Color.parseColor("#2B9EDA") // Primary blue
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt:121: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("package:${activity.packageName}")
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\utils\PermissionManager.kt:121: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                data = Uri.parse("package:${activity.packageName}")
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:44: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putBoolean(KEY_SILENCING_ENABLED, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:54: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:67: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:78: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putStringSet(KEY_SELECTED_APPS, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:85: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putStringSet(KEY_SILENCED_APPS, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:92: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putStringSet(KEY_ALWAYS_ALLOWED_APPS, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:99: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putBoolean(KEY_SELECT_ALL_APPS, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:106: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        set(value) = preferences.edit().putBoolean(KEY_FIRST_RUN, value).apply()
                     ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\src\main\java\com\app\notiminimalist\data\SchedulePreferences.kt:234: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
        preferences.edit().clear().apply()
        ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts:56: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.compose.material:material-icons-extended:1.5.4")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\AndroidStudioProjects\Notiminimalist\app\build.gradle.kts:59: Warning: Use version catalog instead [UseTomlInstead]
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

0 errors, 91 warnings
