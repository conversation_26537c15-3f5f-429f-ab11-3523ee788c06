package com.app.notiminimalist.utils

import android.app.NotificationManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.service.notification.NotificationListenerService
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.app.notiminimalist.utils.BatteryOptimizer

/**
 * Manages all permission-related operations for the Notiminimalist app.
 * Handles Do Not Disturb permissions and exact alarm permissions.
 */
class PermissionManager(private val activity: ComponentActivity) {
    
    private var dndPermissionLauncher: ActivityResultLauncher<Intent>? = null
    private var exactAlarmPermissionLauncher: ActivityResultLauncher<Intent>? = null
    private var notificationListenerPermissionLauncher: ActivityResultLauncher<Intent>? = null
    private val batteryOptimizer = BatteryOptimizer(activity)
    
    companion object {
        private const val TAG = "PermissionManager"
    }
    
    /**
     * Initialize permission launchers. Call this in the activity's onCreate.
     */
    fun initialize(
        onDndPermissionResult: (Boolean) -> Unit = {},
        onExactAlarmPermissionResult: (Boolean) -> Unit = {},
        onNotificationListenerPermissionResult: (Boolean) -> Unit = {}
    ) {
        dndPermissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            onDndPermissionResult(hasDndPermission())
        }
        
        exactAlarmPermissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            onExactAlarmPermissionResult(hasExactAlarmPermission())
        }
        
        notificationListenerPermissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            onNotificationListenerPermissionResult(hasNotificationListenerPermission())
        }
    }
    
    /**
     * Check if the app has Do Not Disturb permission.
     */
    fun hasDndPermission(): Boolean {
        val notificationManager = activity.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            notificationManager.isNotificationPolicyAccessGranted
        } else {
            true // No permission needed for API < 23
        }
    }
    
    /**
     * Check if the app has exact alarm permission.
     */
    fun hasExactAlarmPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = activity.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            alarmManager.canScheduleExactAlarms()
        } else {
            true // No special permission needed for API < 31
        }
    }
    
    /**
     * Check if the app has notification listener permission.
     */
    fun hasNotificationListenerPermission(): Boolean {
        val componentName = ComponentName(activity, com.app.notiminimalist.service.NotiminimalistNotificationListener::class.java)
        val enabledListeners = Settings.Secure.getString(
            activity.contentResolver,
            "enabled_notification_listeners"
        )
        return enabledListeners?.contains(componentName.flattenToString()) ?: false
    }
    
    /**
     * Check if all required permissions are granted.
     */
    fun hasAllRequiredPermissions(): Boolean {
        return hasDndPermission() && hasExactAlarmPermission() && hasNotificationListenerPermission()
    }
    
    /**
     * Request Do Not Disturb permission.
     */
    fun requestDndPermission() {
        if (hasDndPermission()) return
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS)
            dndPermissionLauncher?.launch(intent)
        }
    }
    
    /**
     * Request exact alarm permission.
     */
    fun requestExactAlarmPermission() {
        if (hasExactAlarmPermission()) return
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
                data = Uri.parse("package:${activity.packageName}")
            }
            exactAlarmPermissionLauncher?.launch(intent)
        }
    }
    
    /**
     * Request notification listener permission.
     */
    fun requestNotificationListenerPermission() {
        if (hasNotificationListenerPermission()) return
        
        val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
        notificationListenerPermissionLauncher?.launch(intent)
    }
    
    /**
     * Request all missing permissions.
     */
    fun requestAllMissingPermissions() {
        if (!hasDndPermission()) {
            requestDndPermission()
        } else if (!hasExactAlarmPermission()) {
            requestExactAlarmPermission()
        } else if (!hasNotificationListenerPermission()) {
            requestNotificationListenerPermission()
        }
    }
    
    /**
     * Check if battery optimization is recommended.
     */
    fun shouldRequestBatteryOptimization(): Boolean {
        return !batteryOptimizer.isIgnoringBatteryOptimizations()
    }
    
    /**
     * Get battery optimization status for UI display.
     */
    fun getBatteryOptimizationStatus(): String {
        return if (batteryOptimizer.isIgnoringBatteryOptimizations()) {
            "Battery optimization disabled (recommended)"
        } else {
            "Battery optimization enabled (may affect reliability)"
        }
    }
    
    /**
     * Get a user-friendly description of missing permissions.
     */
    fun getMissingPermissionsDescription(): String {
        val missingPermissions = mutableListOf<String>()
        
        if (!hasDndPermission()) {
            missingPermissions.add("Do Not Disturb access")
        }
        
        if (!hasExactAlarmPermission()) {
            missingPermissions.add("Exact alarm scheduling")
        }
        
        if (!hasNotificationListenerPermission()) {
            missingPermissions.add("Notification listener access")
        }
        
        return when {
            missingPermissions.isEmpty() -> "All permissions granted"
            missingPermissions.size == 1 -> "${missingPermissions[0]} permission required"
            missingPermissions.size == 2 -> "${missingPermissions[0]} and ${missingPermissions[1]} permissions required"
            else -> "${missingPermissions.dropLast(1).joinToString(", ")} and ${missingPermissions.last()} permissions required"
        }
    }
}
