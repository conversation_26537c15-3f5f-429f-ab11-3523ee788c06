{"logs": [{"outputFile": "com.app.notiminimalist.app-mergeDebugResources-45:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a87ea0a2ae3b302fa8b2e67fc66b56aa\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8769,8854", "endColumns": "84,88", "endOffsets": "8849,8938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\18b22e515b4201f34167a2d64b698d76\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,993,1081,1155,1232,1311,1388,1469,1538", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,988,1076,1150,1227,1306,1383,1464,1533,1651"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "825,924,1006,1100,1200,1283,1365,7743,7838,7920,8005,8093,8167,8244,8323,8501,8582,8651", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "919,1001,1095,1195,1278,1360,1446,7833,7915,8000,8088,8162,8239,8318,8395,8577,8646,8764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac5730e1294228dab72f8d7b7acde09d\\transformed\\material3-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,401,517,616,718,837,983,1106,1262,1349,1447,1542,1641,1763,1885,1988,2128,2266,2399,2576,2705,2821,2940,3063,3159,3257,3380,3521,3627,3732,3840,3979,4123,4232,4334,4425,4520,4616,4723,4811,4896,5010,5090,5173,5272,5373,5464,5560,5649,5753,5851,5951,6068,6148,6253", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "166,281,396,512,611,713,832,978,1101,1257,1344,1442,1537,1636,1758,1880,1983,2123,2261,2394,2571,2700,2816,2935,3058,3154,3252,3375,3516,3622,3727,3835,3974,4118,4227,4329,4420,4515,4611,4718,4806,4891,5005,5085,5168,5267,5368,5459,5555,5644,5748,5846,5946,6063,6143,6248,6342"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1451,1567,1682,1797,1913,2012,2114,2233,2379,2502,2658,2745,2843,2938,3037,3159,3281,3384,3524,3662,3795,3972,4101,4217,4336,4459,4555,4653,4776,4917,5023,5128,5236,5375,5519,5628,5730,5821,5916,6012,6119,6207,6292,6406,6486,6569,6668,6769,6860,6956,7045,7149,7247,7347,7464,7544,7649", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "1562,1677,1792,1908,2007,2109,2228,2374,2497,2653,2740,2838,2933,3032,3154,3276,3379,3519,3657,3790,3967,4096,4212,4331,4454,4550,4648,4771,4912,5018,5123,5231,5370,5514,5623,5725,5816,5911,6007,6114,6202,6287,6401,6481,6564,6663,6764,6855,6951,7040,7144,7242,7342,7459,7539,7644,7738"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\38ea0471cd817173b4203329613a319d\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,310,408,507,612,714,8400", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "200,305,403,502,607,709,820,8496"}}]}]}